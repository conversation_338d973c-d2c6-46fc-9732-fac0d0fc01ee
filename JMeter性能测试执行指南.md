# JMeter性能测试执行指南

## 概述

本文档提供了两个API接口的JMeter性能测试脚本使用指南：
1. `getPagesMenus_性能测试.jmx` - 菜单内容拉取接口
2. `createOrder_性能测试.jmx` - 订单创建接口

## 测试前准备

### 1. 环境要求
- JMeter 5.4.1 或更高版本
- Java 8 或更高版本
- 目标服务器正常运行
- 足够的网络带宽和客户端资源

### 2. 配置修改

#### 菜单接口测试配置
打开 `getPagesMenus_性能测试.jmx`，修改以下变量：
- `SERVER_HOST`: 目标服务器地址（默认：localhost）
- `SERVER_PORT`: 服务器端口（默认：8080）
- `TEST_PATH`: 测试页面路径（默认：index）
- `COMP_TYPE`: 组件类型（默认：home）

#### 订单接口测试配置
打开 `createOrder_性能测试.jmx`，**必须**修改以下变量：
- `SERVER_HOST`: 目标服务器地址
- `SERVER_PORT`: 服务器端口
- `AUTH_TOKEN`: **重要！需要替换为有效的用户认证token**
- `PKG_CODE`: 套餐代码（确保在系统中存在）
- `PRODUCT_CODE`: 产品代码（确保在系统中存在）
- `GUEST_NAME`: 测试用户姓名
- `GUEST_MOBILE`: 测试用户手机号

### 3. 获取认证Token
对于订单创建接口，需要有效的用户token：
1. 使用浏览器登录系统
2. 打开开发者工具（F12）
3. 在Network标签页中找到任意API请求
4. 复制Authorization header中的token值
5. 将token值填入JMeter脚本的`AUTH_TOKEN`变量中

## 测试执行步骤

### 阶段1：功能验证测试
1. 设置线程数为1
2. 循环次数设置为1
3. 运行测试，确保接口正常响应
4. 检查"查看结果树"中的响应数据
5. 确认所有断言都通过

### 阶段2：渐进式负载测试

#### 菜单接口测试建议
| 阶段 | 线程数 | 启动时间 | 持续时间 | 目标 |
|------|--------|----------|----------|------|
| 1 | 50 | 30秒 | 300秒 | 基准性能 |
| 2 | 100 | 60秒 | 300秒 | 正常负载 |
| 3 | 200 | 90秒 | 300秒 | 高负载 |
| 4 | 500 | 120秒 | 300秒 | 极限测试 |

#### 订单接口测试建议
| 阶段 | 线程数 | 启动时间 | 持续时间 | 目标 |
|------|--------|----------|----------|------|
| 1 | 20 | 60秒 | 300秒 | 基准性能 |
| 2 | 50 | 90秒 | 300秒 | 正常负载 |
| 3 | 100 | 120秒 | 300秒 | 高负载 |
| 4 | 200 | 180秒 | 300秒 | 极限测试 |

**注意：订单接口涉及数据库写操作，建议从较少线程开始测试**

## 性能指标监控

### 关键指标
1. **QPS (每秒查询数)**
   - 查看"每秒事务数(QPS)"图表
   - 观察QPS随时间的变化趋势
   - 记录峰值QPS

2. **响应时间**
   - 平均响应时间
   - 90%响应时间
   - 95%响应时间
   - 99%响应时间

3. **错误率**
   - 目标：< 1%
   - 超过1%需要分析原因

4. **吞吐量**
   - 每秒处理的数据量
   - 网络带宽使用情况

### 监控工具使用
- **聚合报告**: 查看整体统计数据
- **响应时间图表**: 观察响应时间变化趋势
- **每秒事务数**: 监控QPS变化
- **查看结果树**: 调试和查看详细响应

## 性能拐点识别

### 识别标准
1. **响应时间急剧增加**
   - 平均响应时间增长超过50%
   - 99%响应时间超过可接受阈值

2. **错误率上升**
   - 错误率超过1%
   - 出现超时或连接错误

3. **QPS不再增长**
   - 增加线程数但QPS不再提升
   - QPS开始下降

### 服务器资源监控
同时监控服务器资源：
- CPU使用率
- 内存使用率
- 数据库连接数
- 数据库响应时间
- 网络I/O
- 磁盘I/O

## 测试结果分析

### 菜单接口预期性能
- 目标QPS: > 1000 (读操作，相对较快)
- 平均响应时间: < 100ms
- 99%响应时间: < 500ms

### 订单接口预期性能
- 目标QPS: > 100 (写操作，涉及业务逻辑)
- 平均响应时间: < 1000ms
- 99%响应时间: < 3000ms

### 性能优化建议
1. **数据库优化**
   - 检查索引使用情况
   - 优化SQL查询
   - 考虑读写分离

2. **应用层优化**
   - 增加缓存
   - 优化业务逻辑
   - 连接池配置

3. **系统资源**
   - 增加服务器配置
   - 负载均衡
   - CDN加速

## 注意事项

### 测试环境
1. 使用独立的测试环境
2. 确保测试数据的一致性
3. 避免在生产环境进行压力测试

### 数据清理
1. 订单接口测试会产生大量测试数据
2. 测试完成后及时清理测试数据
3. 考虑使用测试专用的数据库

### 安全考虑
1. 使用测试专用的token
2. 避免使用真实用户数据
3. 测试完成后撤销测试token

## 故障排除

### 常见问题
1. **认证失败**
   - 检查token是否有效
   - 确认header配置正确

2. **连接超时**
   - 检查网络连接
   - 调整超时时间设置

3. **服务器错误**
   - 检查服务器日志
   - 监控服务器资源使用

4. **数据库连接问题**
   - 检查数据库连接池配置
   - 监控数据库性能

### 调试技巧
1. 先进行单线程测试
2. 逐步增加负载
3. 使用"查看结果树"分析失败请求
4. 检查服务器端日志

## 报告模板

### 测试结果记录表
| 接口 | 线程数 | QPS峰值 | 平均响应时间 | 99%响应时间 | 错误率 | 备注 |
|------|--------|---------|--------------|-------------|--------|------|
| getPagesMenus | | | | | | |
| createOrder | | | | | | |

### 性能拐点记录
- **菜单接口性能拐点**: 线程数___, QPS___, 响应时间___
- **订单接口性能拐点**: 线程数___, QPS___, 响应时间___

### 优化建议
基于测试结果提出具体的性能优化建议。
