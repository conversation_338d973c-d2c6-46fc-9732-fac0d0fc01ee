package com.cw.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.core.report.proc.RpProc_Sample;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Joiner;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.Test;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2023-08-03
 */
@Slf4j
public class TestMap {

    @Test
    public void ssxxs() {
        System.out.println("TICKET".equals(ProdType.TICKET.name()));
    }


    @Test
    public void testRpform() {
        Stopwatch stopwatch = Stopwatch.createStarted();
        RpProc_Sample sample = new RpProc_Sample();
        System.out.println(JSON.toJSONString(sample.getinitQueryForm(), true));
        System.out.println("--------------");
        System.out.println(JSON.toJSONString(sample.getInitPrintColumn(), true));
        log.info("consume:{}", stopwatch.stop());

        System.out.println("-xxx");

        stopwatch.reset();
    }



    @Test
    public void destest() throws Exception {
        String requestBody = " {\"pageIndex\": 1,\"pageSize\": 10}";
        String userKey = "12345678";

        SecureRandom sr = new SecureRandom();
// 从原始密钥数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(userKey.getBytes());
// 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(dks);
// Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance("DES");
// 用密钥初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, securekey, sr);

        String f = cipher.doFinal(requestBody.getBytes()).toString();

        log.info(f);

        String signBody = "yangjinkuiGetProductInfo1458871246v1.0IPd5HD/HsjyMK7b1JALkopqhDlrgrV3zTEzyThrof28=12345678";


        log.info(SecureUtil.md5(signBody).toLowerCase());

        Date now = new Date();
        System.out.println( CalculateDate.spd.format(now));

    }

    private String bigDecimalToStr(BigDecimal amount) {
        if (amount == null) {
            return null;
        }

        String integerPartStr;
        String decimalPartStr = "";

        // 分离整数和小数部分
        BigDecimal integerPart = amount.setScale(0, BigDecimal.ROUND_DOWN);
        BigDecimal decimalPart = amount.subtract(integerPart);

        // 格式化整数部分
        DecimalFormat integerFormat = new DecimalFormat("0");
        integerPartStr = integerFormat.format(integerPart);

        // 处理小数部分
        if (decimalPart.compareTo(BigDecimal.ZERO) > 0) {
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            decimalPartStr = decimalFormat.format(decimalPart);
            // 去掉小数末尾的 0
            if (decimalPartStr.contains(".")) {
                while (decimalPartStr.endsWith("0")) {
                    decimalPartStr = decimalPartStr.substring(0, decimalPartStr.length() - 1);
                }
                if (decimalPartStr.endsWith(".")) {
                    decimalPartStr = decimalPartStr.substring(0, decimalPartStr.length() - 1);
                }
                // 去掉整数部分的 0
                decimalPartStr = decimalPartStr.replaceFirst("^0", "");
            }
        }

        // 补空格使整数部分长度为 6
        StringBuilder paddedBuilder = new StringBuilder(integerPartStr);
        while (paddedBuilder.length() < 6) {
            paddedBuilder.insert(0, ' ');
        }

        // 组合整数和小数部分
        return paddedBuilder.toString() + decimalPartStr;
    }


    private static final String ALGORITHM = "DES";
    // 加密转换模式，包含算法、模式和填充方式
    private static final String TRANSFORMATION = "DES/ECB/PKCS5Padding";

    /**
     * DES 加密方法
     * @param data 待加密的明文数据
     * @param key  加密密钥
     * @return 加密后的 Base64 编码字符串
     * @throws Exception 加密过程中可能抛出的异常
     */
    public static String encrypt(String data, String key) throws Exception {
        // 创建 DESKeySpec 对象，用于指定 DES 密钥
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
        // 获取 SecretKeyFactory 实例，用于生成密钥
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        // 生成 SecretKey 对象
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        // 获取 Cipher 实例，用于加密操作
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        // 初始化 Cipher 为加密模式，并传入密钥
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        // 执行加密操作
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        // 将加密后的字节数组进行 Base64 编码
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * DES 解密方法
     * @param encryptedData 加密后的 Base64 编码字符串
     * @param key           解密密钥，与加密密钥相同
     * @return 解密后的明文数据
     * @throws Exception 解密过程中可能抛出的异常
     */
    public static String decrypt(String encryptedData, String key) throws Exception {
        // 对 Base64 编码的加密数据进行解码
        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
        // 创建 DESKeySpec 对象，用于指定 DES 密钥
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
        // 获取 SecretKeyFactory 实例，用于生成密钥
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        // 生成 SecretKey 对象
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        // 获取 Cipher 实例，用于解密操作
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        // 初始化 Cipher 为解密模式，并传入密钥
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        // 执行解密操作
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        // 将解密后的字节数组转换为字符串
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }




    @Test
    public void bba() {
        String bbb = " {\"pageIndex\": 1,\"pageSize\": 10}";
        String key = "12345678";

        DES des = new DES(Mode.ECB.name(), "PKCS7Padding", key.getBytes());

        log.info(des.encrypt(bbb).toString());

        System.out.println("金额转字符串"+ bigDecimalToStr(new BigDecimal("19.91")));

        //DES des=SecureUtil.des(SecureUtil.generateKey());

        try {
            String str = decrypt("HAhd/BIIgYMTQkKTDd3mEA==", "UBINJS3J");
            System.out.println("解析明文" + str);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Long value = new Long(100);
        String prefix = "";
        if (value != null && value >= Math.pow(10, 2)) {
            // 计算需要添加的前缀字母
            int letterIndex = (int) (value / Math.pow(10, 2));
            prefix = String.valueOf((char) ('A' + letterIndex - 1));
            value = value % (long) Math.pow(10, 2);
        }
        String seq = String.format("%0" + (2 - prefix.length()) + "d", value.intValue());
        System.out.println(prefix + seq);

    }

    @Test
    public void sss() {
        String s = "107.192168,29.031063\n" +
                "107.070236,29.068021\n" +
                "107.107335,29.053248\n" +
                "107.108858,29.150006\n" +
                "107.089776,29.150179\n" +
                "107.099878,29.157149\n" +
                "107.103703,29.148726\n" +
                "107.372665,29.156659\n" +
                "107.101379,29.047967\n" +
                "106.985085,29.239054\n" +
                "107.106074,29.154824";

        String[] ss = s.split("\n");
        for (String string : ss) {


        // 获取当前时间戳
        Instant now = Instant.now();

        // 获取东八区时区
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");

        // 将当前时间戳转换为东八区时间戳
        Instant utc8TimeStamp = now.atZone(zoneId).toInstant();

        // 输出东八区时间戳
        System.out.println(utc8TimeStamp);

        log.info("stamp:{}", DateTime.now(DateTimeZone.UTC).getMillis());

        log.info("当前时间戳：{} {} {}", utc8TimeStamp.toEpochMilli(), System.currentTimeMillis(), LocalDateTimeUtil.ofUTC(Instant.now()));

        }

        List<String> ls = Arrays.asList(("107.192168,29.031063".split(",")));

        log.info(Joiner.on(",").join(Lists.reverse(ls)));
    }


    @Test
    public void Amap() {
        MapUtil m = MapUtil.getInstance();

        //左上：106.899783,29.483928
        //右下:107.436079,28.784006

        int zoom = 13;
        double[] left = m.lnglatToTile(106.899783, 29.483928, zoom);
        double[] right = m.lnglatToTile(107.436079, 28.784006, zoom);

        int[] x = new int[]{Math.min((int) left[0], (int) right[0]), Math.max((int) left[0], (int) right[0])};
        int[] y = new int[]{Math.min((int) left[1], (int) right[1]), Math.max((int) left[1], (int) right[1])};

        log.info("x合法范围: {},y合法范围:{}", x, y);

    }


    private boolean isInternationalMobile(String mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return false;
        }
        // 以+开头的国际格式号码
        if (mobile.startsWith("+")) {
            return !mobile.startsWith("+86");
        }

        // 判断是否以00开头（国际前缀）
        if (mobile.startsWith("00")) {
            // 提取区号部分（从第3位到第4-5位，常见区号长度）
            String countryCode = extractCountryCode(mobile);
            System.out.println("countryCode:" + countryCode);
            // 检查是否为国内区号（这里以中国86为例）
            // return !"86".equals(countryCode);
            return !countryCode.startsWith("86");
        }

        // 不以+/00开头，默认为国内号码
        return false;
    }

    private static String extractCountryCode(String mobile) {
        // 提取可能的区号部分（2-5位数字）
        // 常见格式：00+区号+电话号码
        int endIndex = Math.min(mobile.length(), 5); // 最多取前5位
        System.out.println("endIndex:" + endIndex);
        // 从第3位开始查找连续数字作为区号
        for (int i = 3; i < endIndex; i++) {
            if (!Character.isDigit(mobile.charAt(i))) {
                return mobile.substring(2, i);
            }
        }
        // 如果没有非数字字符，返回截取的部分
        return mobile.substring(2, endIndex);
    }


    @Test
    public void testYZM() {

        String mobileNo = "0085254166717";
        System.out.println("手机号：" + mobileNo);
        String timestamp = "1751351650907";
        System.out.println("时间戳："+timestamp);
        String signStr = mobileNo + timestamp;
        String sign = SysFuncLibTool.encodeAesContent(signStr, "001");
        System.out.println("sign:"+sign);

        if (isInternationalMobile(mobileNo)) {
            System.out.println("是国际手机号码");
        }else {
            System.out.println("不是国际手机号码");
        }



    }

    //public boolean 加载图片(x,y,zoom){
    //
    //    return false;
    //}
}
