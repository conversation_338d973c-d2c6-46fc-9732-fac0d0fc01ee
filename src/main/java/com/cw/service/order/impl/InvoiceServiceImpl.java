package com.cw.service.order.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.config.exception.CustomException;
import com.cw.core.orderhandler.InvoiceHandler;
import com.cw.core.orderhandler.InvoiceVendorSwitcher;
import com.cw.core.vendor.invoice.InvoiceVendorHandler;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.outsys.pojo.invoice.response.NNInvoiceCallBackFailResponse;
import com.cw.outsys.pojo.invoice.response.NNInvoiceCallBackSuccessResponse;
import com.cw.outsys.stdop.request.StdInvoiceCreateRequest;
import com.cw.outsys.stdop.request.StdInvoicePushRequest;
import com.cw.outsys.stdop.request.StdInvoiceQueryRequest;
import com.cw.outsys.stdop.request.StdInvoiceReCreateRequest;
import com.cw.outsys.stdop.response.StdInvoiceCreateResponse;
import com.cw.outsys.stdop.response.StdInvoicePushResponse;
import com.cw.outsys.stdop.response.StdInvoiceQueryResponse;
import com.cw.outsys.stdop.response.StdInvoiceReCreateResponse;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.order.req.*;
import com.cw.pojo.dto.order.res.InvoiceListRes;
import com.cw.pojo.dto.order.res.InvoiceLoadRes;
import com.cw.pojo.dto.order.res.InvoiceLoadTitleRes;
import com.cw.pojo.dto.order.res.InvoiceLocalInfoRes;
import com.cw.service.context.GlobalContext;
import com.cw.service.log.UserLogService;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.order.InvoiceService;
import com.cw.utils.*;
import com.cw.utils.easyexcel.EasyExcelUtil;
import com.cw.utils.enums.InvoiceType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import com.google.common.base.Joiner;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;


/**
 * @Describe 发票管理
 * <AUTHOR> Tony Leung
 * @Create on 2021/12/13 0013
 */
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {
    @Autowired
    InvoiceMapper invoiceMapper;
    @Autowired
    UserLogService userLogService;

    @Autowired
    ActrsMapper actrsMapper;

    @Autowired
    BookingrsMapper bookingrsMapper;

    @Autowired
    InvoiceHandler invoiceHandler;

    @Autowired
    InvoiceVendorSwitcher switcher;

    @Autowired
    RedisTemplate redisTemplate;

    @Override
    public InvoiceListRes queryList(InvoiceListReq req) {
        int currentPage = req.getPages().getCurrentpage() < 1 ? 0 : req.getPages().getCurrentpage() - 1;
        int pageSize = req.getPages().getPagesize();
        String bookingid = req.getBookingid();
        String payid = req.getPayid();
        String invoiceno = req.getInvoiceno();
        String invoicecode = req.getInvoicecode();
        String istatus = req.getIstatus();
        String projectId = GlobalContext.getCurrentProjectId();
        //todo 如果支持拆分发票，以发票表为主体
        Page<Invoice> invoicePage = invoiceMapper.findAll((Root<Invoice> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            if (bookingid != null && !bookingid.isEmpty()) {//搜索条件有综合预定号,其它条件忽略
                list.add(cb.equal(root.get("bookingid"), bookingid.trim().toUpperCase()));

            } else {
                if (StringUtils.isNotBlank(istatus)) { //发票状态
                    list.add(cb.like(root.get("istatus"), istatus));
                }
                if (StringUtils.isNotBlank(invoicecode)) { //发票代码
                    list.add(cb.like(root.get("invoicecode"), invoicecode));
                }

                if (StringUtils.isNotBlank(invoiceno)) { //发票号码
                    list.add(cb.equal(root.get("invoiceno"), invoiceno));
                }

            }
            list.add(cb.equal(root.get(SystemUtil.projectClumn), projectId));

            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("createdate")));//订单创建时间降序
            orderList.add(cb.asc(root.get("istatus")));//状态升序
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p))).orderBy(orderList);

            return query.getRestriction();
        }, org.springframework.data.domain.PageRequest.of(currentPage, pageSize));
        Sort sort = Sort.by(req.getPages().getDirection(), req.getPages().getSortname().split(","));
        invoicePage.getSort().and(sort);
        InvoiceListRes res = new InvoiceListRes();
        res.fillPageData(invoicePage);
        ////文字转换
        fillResPageData(res.getRecords(), payid, projectId);
        return res;

    }

    /**
     * 填充数据补充订单信息
     *
     * @param datas
     * @param payid
     * @param projectId
     */
    private void fillResPageData(List<InvoiceListRes.InvoiceListData> datas, String payid, String projectId) {
        if (CollectionUtil.isNotEmpty(datas)) {
            for (InvoiceListRes.InvoiceListData item : datas) {
                String bookingid = item.getBookingid();
                if (!item.getCreatedate().after(SystemUtil.EMPTY_DATETIME)) { //没有开票日期
                    item.setCreatedate(null);
                }
                if (!item.getHandletime().after(SystemUtil.EMPTY_DATETIME)) { //没有操作日期
                    item.setHandletime(null);
                }
                if (!bookingid.startsWith("Y")) {//正常主单
                    Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(bookingid);
                    if (rs != null) {
                        if (StringUtils.isNotBlank(payid) && !rs.getPayid().equals(payid)) { //过滤支付流水号条件搜索
                            continue;
                        }
                        item.setAmount(rs.getAmount());
                        item.setTel(rs.getTel());
                        item.setPtype(rs.getPtype());
                        item.setPtypedesc(CustomData.getDesc(projectId, rs.getPtype(), SystemUtil.CustomDataKey.ptype));
                        item.setPayid(rs.getPayid());
                        item.setPayno(rs.getPayno());
                        item.setBookername(rs.getBookername());
                        item.setTypedesc(InvoiceType.getInvoiceType(item.getType()));//发票类型描述

                    } else { //可能过期搜索历史
                        Booking_rs_his rs_his = SpringUtil.getBean(BookingrsHisMapper.class).findByBookingidAndProjectid(bookingid, projectId);
                        if (rs_his != null) {
                            if (StringUtils.isNotBlank(payid) && !rs_his.getPayid().equals(payid)) { //过滤支付流水号条件搜索
                                continue;
                            }
                            item.setAmount(rs_his.getAmount());
                            item.setTel(rs_his.getTel());
                            item.setPtype(rs_his.getPtype());
                            item.setPtypedesc(CustomData.getDesc(projectId, rs_his.getPtype(), SystemUtil.CustomDataKey.ptype));
                            item.setPayid(rs_his.getPayid());
                            item.setPayno(rs_his.getPayno());
                            item.setBookername(rs_his.getBookername());
                            item.setTypedesc(InvoiceType.getInvoiceType(item.getType()));//发票类型描述
                        }
                    }
                    item.setIstatus(StatusUtil.InvoiceStatusEnum.getInvoiceStatusDesc(item.getIstatus()));//发票状态转换中文

                } else { //预约订单
                    Act_rs rs = actrsMapper.findAct_rsByBookingid(bookingid);
                    if (rs != null) {
                        item.setAmount(rs.getAmount());
                        item.setTel(rs.getTelephone());
                        item.setPtype(ProdType.ACTGROUP.val());
                        item.setPtypedesc(CustomData.getDesc(projectId, rs.getGroupid(), SystemUtil.CustomDataKey.actgroup));
                        item.setPayid(rs.getPayid());
                        item.setPayno(rs.getPayno());
                        item.setBookername(rs.getBookername());
                        item.setTypedesc(InvoiceType.getInvoiceType(item.getType()));//发票类型描述
                        item.setIstatus(StatusUtil.InvoiceStatusEnum.getInvoiceStatusDesc(item.getIstatus()));//发票状态转换中文
                    }

                }

            }

        }

    }

    /**
     * @param req
     * @return 返回发票订单详情
     */
    @Override
    public InvoiceLoadRes load(InvoiceLoadReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        Invoice dbInvoice;
        if (req.getSqlid() > 0L) {
            dbInvoice = invoiceMapper.findBySqlid(req.getSqlid());
            if (dbInvoice == null) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
            }
        }else {

            List<Invoice> invoiceList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(req.getBookingid(), projectId);
            if (CollectionUtil.isEmpty(invoiceList)) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
            }
             dbInvoice = invoiceList.get(0);
        }
        InvoiceLoadRes res = new InvoiceLoadRes();
        BeanUtils.copyPropertiesIgnoreNull(dbInvoice, res);
        res.setIstatus(dbInvoice.getIstatus());//发票状态
        // 判断订单号是否是预约订单
        if (!req.getBookingid().startsWith("Y")) {//正常主单
            Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getBookingid());
            if (rs != null) {
                res.setAmount(rs.getAmount());
                res.setTel(rs.getTel());
                res.setPtype(rs.getPtype());
                res.setPtypedesc(CustomData.getDesc(projectId, rs.getPtype(), SystemUtil.CustomDataKey.ptype));
                res.setPayid(rs.getPayid());
                res.setPayno(rs.getPayno());
                res.setBookername(rs.getBookername());
                res.setTypedesc(InvoiceType.getInvoiceType(res.getType()));//发票类型描述
            } else { //可能过期搜索历史
                Booking_rs_his rs_his = SpringUtil.getBean(BookingrsHisMapper.class).findByBookingidAndProjectid(req.getBookingid(), projectId);
                if (rs_his != null) {
                    res.setAmount(rs_his.getAmount());
                    res.setTel(rs_his.getTel());
                    res.setPtype(rs_his.getPtype());
                    res.setPtypedesc(CustomData.getDesc(projectId, rs_his.getPtype(), SystemUtil.CustomDataKey.ptype));
                    res.setPayid(rs_his.getPayid());
                    res.setPayno(rs_his.getPayno());
                    res.setBookername(rs_his.getBookername());
                }
                res.setTypedesc(InvoiceType.getInvoiceType(res.getType()));//发票类型描述
            }

        } else { //预约订单
            Act_rs rs = actrsMapper.findAct_rsByBookingid(req.getBookingid());
            if (rs != null) {
                res.setAmount(rs.getAmount());
                res.setTel(rs.getTelephone());
                res.setPtype(ProdType.ACTGROUP.val());
                res.setPtypedesc(CustomData.getDesc(projectId, rs.getGroupid(), SystemUtil.CustomDataKey.actgroup));
                res.setPayid(rs.getPayid());
                res.setPayno(rs.getPayno());
                res.setBookername(rs.getBookername());
                res.setTypedesc(InvoiceType.getInvoiceType(res.getType()));//发票类型描述
            }

        }


        res.setIstatus(StatusUtil.InvoiceStatusEnum.getInvoiceStatusDesc(res.getIstatus()));//发票状态转换中文
        return res;
    }

    private boolean checkLocalInvoiceType(String projectId) {
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysconf = sysConfCache.getOne(projectId);
        if (sysconf != null && VendorType.LOCAL_INVOICE.name().equals(sysconf.getInvoicevendor())) {
            return true;
        }
        return false;
    }

    /**
     * 调用第三方电子发票接口开发票
     *
     * @param req
     */
    @Override
    public void invoiceRed(InvoiceRedReq req) throws DefinedException {
        //获取订单支付金额判断是否支付 支付成功才才有开票记录判断是否红冲
        Invoice dbInvoice = load(req.getSqlid());
        if (!StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(dbInvoice.getIstatus()) &&
                !StatusUtil.InvoiceStatusEnum.NEEDRED.getCode().equals(dbInvoice.getIstatus())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票状态不支持红冲！"));
        }
        String userid = GlobalContext.getCurrentUserId();
        String projectId = GlobalContext.getCurrentProjectId();
        Prepay dbPrepay = SpringUtil.getBean(PrepayMapper.class).findPrepayByBookingid(dbInvoice.getBookingid());//获取支付信息
        if (dbPrepay == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单未支付无法开具发票"));
        }
        BigDecimal redInvoiceAmount;
        if ((req.getAmount() != null)) {
            redInvoiceAmount = req.getAmount();
        } else {
            redInvoiceAmount = dbPrepay.getAmount();
        }
        //redInvoiceAmount+税额超过后台设置单张发票最大金额判断拆开多张发票
        if (canInvoiceRed(dbPrepay, redInvoiceAmount,dbInvoice)) {
            //如果是半自动话开票
            if (checkLocalInvoiceType(projectId)) {
                //默认对应红冲金额
                dbInvoice.setRedincltaxamount(redInvoiceAmount);
                dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REDED.getCode());
                dbInvoice.setHandler(userid);
                dbInvoice.setHandletime(new Date());
                invoiceMapper.saveAndFlush(dbInvoice);
                String content = StrUtil.format("【发票红冲审核通过】,操作人:{}",  userid);
                userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, userid,
                        dbInvoice.getBookingid(), content, projectId);
                return;
            }
            List<Invoice> invoices = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(dbInvoice.getBookingid(), projectId);
            Integer redCode = 10;//bookingid + redcode 标识唯一红冲记录
            if (CollectionUtil.isNotEmpty(invoices)) {
                redCode = redCode + invoices.size();
            }
            InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
            //todo 冲红
            StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
            request.setTitle(dbInvoice.getTitle());
            if (dbInvoice.getTitletype() == 2) { //企业类型选填
                request.setTaxno(dbInvoice.getTaxno());
                request.setAddress(dbInvoice.getAddress());
                request.setTel(dbInvoice.getTel());
                request.setBankname(dbInvoice.getBankname());
                request.setBankno(dbInvoice.getBankno());
            }
            request.setType(1);//标记红冲类型 发票代码和发票号码必填
            request.setInvoicecode(dbInvoice.getInvoicecode());
            request.setInvoiceno(dbInvoice.getInvoiceno());
            request.setEmail(dbInvoice.getEmail());//红票不推送发票信息
            request.setBookingid(dbInvoice.getBookingid() + redCode);//红冲订单编号 bookingid+redCode 标识为唯一，长度20
            //发票明细 按照订单
            List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
            if (StringUtils.isNotBlank(req.getName())) {
                StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                item.setName(req.getName());
                item.setAmount(req.getAmount());
                item.setTax(req.getTax());
                details.add(item);
            } else {
                details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), true));//订单信息转换发票明细
            }
            request.setDetail(details);

            //todo 参数转换
            request.setProjectId(projectId);
            StdInvoiceCreateResponse response = invoiceVendorHandler.InvoiceCreate(request);
            if (response.getStd_flag()) {//请求成功  保存本地发票记录
                //todo 判断发票模式，如果是诺诺发票 只返回发票流水号
                //检查红冲金额，如果是全额红冲 发票记录则变成红冲状态
                //BigDecimal redExTaxAmount = dbInvoice.getRedextaxamount().add(response.getExtaxamount());
                //dbInvoice.setRedextaxamount(redExTaxAmount); //红冲不含税金额
                //BigDecimal redInclTaxAmount = dbInvoice.getRedincltaxamount().add(response.getIncltaxamount());
                //dbInvoice.setRedincltaxamount(redInclTaxAmount);//红冲合计金额
                //dbInvoice.setRedpfdurl(response.getPfdurl());//红冲PDF地址
                dbInvoice.setRedinvoiceid(response.getInvoiceid());//红冲返回的发票流水号
                dbInvoice.setHandletime(new Date());//操作时间
                dbInvoice.setHandler(userid);//操作人
                //if (dbPrepay.getAmount().equals(dbInvoice.getRedextaxamount())) { //红冲不含税金额等于订单总金额，完全红冲
                //    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REDED.getCode());//红冲状态
                //}
                invoiceMapper.saveAndFlush(dbInvoice);
                String content = StrUtil.format("【发票红冲】发票流水号：{},操作人:{}", response.getInvoiceid(), userid);
                userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, userid,
                        dbInvoice.getBookingid(), content, projectId);
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(response.getStd_sub_msg()));
            }
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(dbInvoice.getBookingid() + "订单发票状态不支持红冲"));
        }

    }

    /**
     * 发票重试
     *
     * @param invoiceid
     * @param projectId
     * @throws DefinedException
     */
    @Override
    public void invoiceReCreate(String invoiceid, String projectId) throws DefinedException {
        String userid = GlobalContext.getCurrentUserId();
        Invoice dbInvoice = invoiceMapper.findByInvoiceidAndProjectid(invoiceid, projectId);
        if (dbInvoice == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票记录不存在"));
        }
        if (!StatusUtil.InvoiceStatusEnum.REFUSE.getCode().equals(dbInvoice.getIstatus())) {//开票失败才可重试
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("检查发票记录状态，开票失败才可重试开票"));
        }
        if (checkLocalInvoiceType(projectId)) {
            dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.FINISH.getCode());
            dbInvoice.setHandletime(new Date());
            dbInvoice.setHandler(userid);
            invoiceMapper.saveAndFlush(dbInvoice);
            String content = StrUtil.format("【发票开票失败重新开票】发票流水号：{},操作人:{}", invoiceid,
                    userid);
            userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, userid,
                    dbInvoice.getBookingid(), content, projectId);
            return;

        }
        InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
        StdInvoiceReCreateRequest request = new StdInvoiceReCreateRequest();
        request.setProjectId(projectId);
        request.setInvoiceId(invoiceid);//发票流水号
        StdInvoiceReCreateResponse response = invoiceVendorHandler.InvoiceReCreate(request);//todo 诺诺发票开票失败重开接口问题
        if (response != null) {
            if (response.getStd_flag()) {
                String content = StrUtil.format("【发票开票重试】发票流水号：{},操作人:{}", invoiceid,
                        userid);
                userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, userid,
                        dbInvoice.getBookingid(), content, projectId);
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg(response.getStd_sub_msg()));
            }
        }
    }


    /**
     * @param dbPrepay  订单支付记录
     * @param dbInvoice 当前发票
     * @return 检查订单是否支持红冲，订单总金额>红冲总金额
     */
    private boolean canInvoiceRed(Prepay dbPrepay, BigDecimal redInvoiceAmount, Invoice dbInvoice) {
        BigDecimal dbAmount = dbPrepay.getAmount();
        // String bookingid = dbPrepay.getBookingid();
        if (StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(dbInvoice.getIstatus()) &&
                StatusUtil.InvoiceStatusEnum.NEEDRED.getCode().equals(dbInvoice.getIstatus())) { //非已开和待红冲 状态
            if (dbAmount.compareTo(redInvoiceAmount) > -1) {
                return true;
            } else {
                return false;
            }
        }else {
            return false;
        }
        // List<Invoice> invoiceList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(bookingid, dbPrepay.getProjectid());
        // //查看发票历史总金额
        // if (CollectionUtil.isNotEmpty(invoiceList)) {
        //     for (Invoice invoice : invoiceList) {
        //         if (StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(invoice.getIstatus()) ||
        //                 StatusUtil.InvoiceStatusEnum.NEEDRED.getCode().equals(invoice.getIstatus())) { //已开
        //             if (dbAmount.compareTo(redInvoiceAmount) > -1) {
        //                 return true;
        //             } else {
        //                 return false;
        //             }
        //         } else { //部分红冲状态
        //             return false;
        //         }
        //     }
        //
        // } else { //没有开票记录 不可红冲
        //     return false;
        // }
        // return false;
    }


    /**
     * @param sqlid
     * @return 获取本地数据
     */
    public Invoice load(Long sqlid) {
        Invoice dbInvoice = invoiceMapper.findBySqlid(sqlid);
        if (dbInvoice == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
        }
        return dbInvoice;

    }


    /**
     * 发票推送到手机或者邮箱
     *
     * @param stdInvoicePushRequest
     * @throws DefinedException
     */
    @Override
    public void invoicePush(StdInvoicePushRequest stdInvoicePushRequest) throws DefinedException {
        InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(stdInvoicePushRequest.getProjectId());
        StdInvoicePushResponse response = invoiceVendorHandler.InvoicePush(stdInvoicePushRequest);
        if (!response.getStd_flag()) {//重发失败
            String msg = StrUtil.format("发票推送失败，发票流水号：{}", JSON.toJSONString(Joiner.on(",").join(response.getFailPush())));
            throw new CustomException(ResultJson.failure(ResultCode.SYSERR).msg(response.getStd_sub_msg()));
        }
    }

    /**
     * 诺诺发票成功回传发票信息，更新发票状态
     *
     * @param serialNum  发票流水号
     * @param saleTaxNum 发票税号
     * @param projectId  项目ID
     * @param status     回调发票状态，1-成功，2-失败
     * @param data       回调消息
     */
    @Override
    public void invoiceCallback(String serialNum, String saleTaxNum, String projectId, String status, String data) {
        Invoice dbInvoice = invoiceMapper.findByInvoiceidAndProjectid(serialNum, projectId);
        if (dbInvoice != null) {
            String content = null;
            if ("1".equals(status)) {
                NNInvoiceCallBackSuccessResponse successResponse = JSON.parseObject(data, NNInvoiceCallBackSuccessResponse.class);
                //回调信息填充 填充发票号码 判断发票代码是否为空区分是否重复
                if (!StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(dbInvoice.getIstatus()) && StringUtils.isBlank(dbInvoice.getInvoicecode()) &&
                        successResponse != null) { //避免重复回调
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.FINISH.getCode());
                    dbInvoice.setCreatedate(DateUtil.date(Long.parseLong(successResponse.getCKprq())));//开票日期
                    dbInvoice.setQrurl(successResponse.getQrCode());//二维码
                    dbInvoice.setInvoiceno(successResponse.getCFphm());//发票号码
                    dbInvoice.setInvoicecode(successResponse.getCFpdm());//发票代码
                    dbInvoice.setPfdurl(successResponse.getCUrl());//pdf
                    dbInvoice.setCheckcode(successResponse.getCheckCode());//校验码
                    dbInvoice.setExtaxamount(successResponse.getCBhsje());//不含税金额
                    dbInvoice.setIncltaxamount(successResponse.getCHjje());//含税金额
                    dbInvoice.setJpgurl(successResponse.getCImgUrls());//发票图片地址 cImgUrls 逗号分割
                    content = StrUtil.format("【发票开具】发票开具成功，发票流水号：{}，发票代码：{}，发票号码：{},开具金额:{}",
                            dbInvoice.getInvoiceid(), dbInvoice.getInvoicecode(), dbInvoice.getInvoiceno(), dbInvoice.getIncltaxamount());
                    log.info(content);
                    invoiceMapper.saveAndFlush(dbInvoice);//更新发票状态
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                            dbInvoice.getBookingid(), content, projectId);
                }
            } else if ("2".equals(status)) {//发票申请失败
                NNInvoiceCallBackFailResponse failResponse = JSON.parseObject(data, NNInvoiceCallBackFailResponse.class);
                if (failResponse != null) {
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REFUSE.getCode());
                    content = StrUtil.format("【发票开具】发票开具失败 " + failResponse.getCErrorMessage());
                    log.info(content);
                    invoiceMapper.saveAndFlush(dbInvoice);//更新发票状态
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                            dbInvoice.getBookingid(), content, projectId);
                }
            }


        } else {
            //查询红冲发票记录
            Invoice dbRedInvoice = invoiceMapper.findByRedinvoiceidAndProjectid(serialNum, projectId);
            if (dbRedInvoice != null) {
                String content = null;
                if ("1".equals(status)) {
                    NNInvoiceCallBackSuccessResponse successResponse = JSON.parseObject(data, NNInvoiceCallBackSuccessResponse.class);
                    //回调信息填充 填充红冲 判断红冲发票代码是否为空是否重复回调
                    if (!StatusUtil.InvoiceStatusEnum.REDED.getCode().equals(dbRedInvoice.getIstatus()) && StringUtils.isBlank(dbRedInvoice.getRedinvoicecode()) &&
                            successResponse != null) {
                        dbRedInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REDED.getCode());
                        dbRedInvoice.setRedcreatedate(DateUtil.date(Long.parseLong(successResponse.getCKprq())));//红冲开票日期
                        dbRedInvoice.setQrurl(successResponse.getQrCode());//二维码
                        dbRedInvoice.setRedinvoiceno(successResponse.getCFphm());//发票号码
                        dbRedInvoice.setRedinvoicecode(successResponse.getCFpdm());//发票代码
                        dbRedInvoice.setRedpfdurl(successResponse.getCUrl());//pdf
                        dbRedInvoice.setRedcheckcode(successResponse.getCheckCode());//校验码
                        dbRedInvoice.setRedextaxamount(successResponse.getCBhsje());//不含税金额
                        dbRedInvoice.setRedincltaxamount(successResponse.getCHjje());//含税金额
                        dbRedInvoice.setRedjpgurl(successResponse.getCImgUrls());//发票图片 逗号分割
                        content = StrUtil.format("【发票红冲】红冲发票开具成功，红冲发票流水号：{}，红冲发票代码：{}，红冲发票号码：{},红冲金额:{}",
                                dbRedInvoice.getRedinvoiceid(), dbRedInvoice.getRedinvoicecode(), dbRedInvoice.getRedinvoiceno(),
                                dbRedInvoice.getRedextaxamount());
                        log.info(content);
                        invoiceMapper.saveAndFlush(dbRedInvoice);//更新发票状态
                        userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                                dbRedInvoice.getBookingid(), content, projectId);
                        //红冲成功后查看是否有待开发票
                        Invoice initInvoice = invoiceMapper.findByBookingidAndProjectid(dbRedInvoice.getBookingid(), projectId);
                        if (initInvoice != null) { //抬头修改红冲发票
                            invoiceCreate(initInvoice);
                        }
                    } else {
                        log.info("");
                    }
                } else if ("2".equals(status)) {//发票申请失败
                    NNInvoiceCallBackFailResponse failResponse = JSON.parseObject(data, NNInvoiceCallBackFailResponse.class);
                    if (failResponse != null) {
                        dbRedInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REFUSE.getCode());
                        content = StrUtil.format("【发票红冲】红冲发票开具失败 " + failResponse.getCErrorMessage());
                        log.info(content);
                        invoiceMapper.saveAndFlush(dbRedInvoice);//更新发票状态
                        userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                                dbRedInvoice.getBookingid(), content, projectId);
                    } else {
                        log.info("红冲发票回调信息获取失败，红冲发票流水号：" + serialNum);
                    }
                }


            } else {
                // 回调信息太快 发票记录可能没有创建 重新回调， 回调限制次数
                log.info("发票回调信息找不到发票记录，发票流水号：" + serialNum);
                String redisSeriaNum = (String) redisTemplate.opsForValue().get(RedisKey.NNINVOICECALLBACK + serialNum);
                if (redisSeriaNum == null) {
                    invoiceCallback(serialNum, saleTaxNum, projectId, status, data);
                    redisTemplate.opsForValue().set(RedisKey.NNINVOICECALLBACK + serialNum, serialNum, 30, TimeUnit.MINUTES);
                }

            }

        }

    }

    /**
     * 发票记录发起申请发票开具
     *
     * @param postInvoice
     */
    private void invoiceCreate(Invoice postInvoice) {
        if (postInvoice != null) {
            String projectId = postInvoice.getProjectid();
            InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(postInvoice.getProjectid());
            StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
            request.setTitle(postInvoice.getTitle());//抬头
            if (StringUtils.isNotBlank(postInvoice.getEmail())) {
                request.setEmail(postInvoice.getEmail());//接收电子发票的邮箱
            }
            if (StringUtils.isNotBlank(postInvoice.getPhone())) {
                request.setPhone(postInvoice.getPhone());
            }
            request.setBookingid(postInvoice.getOrderno());
            if (postInvoice.getTitletype() == 2) { //企业类型选填
                request.setTaxno(postInvoice.getTaxno());//企业类型抬头必填
                if (StringUtils.isNotBlank(postInvoice.getAddress())) {
                    request.setAddress(postInvoice.getAddress());
                }
                if (StringUtils.isNotBlank(postInvoice.getTel())) {
                    request.setTel(postInvoice.getTel());
                }
                if (StringUtils.isNotBlank(postInvoice.getBankname())) {
                    request.setBankname(postInvoice.getBankname());
                }
                if (StringUtils.isNotBlank(postInvoice.getBankno())) {
                    request.setBankno(postInvoice.getBankno());
                }
            }
            //发票明细 按照订单
            List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
            details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, postInvoice.getBookingid(), false));//订单信息转换发票明细
            request.setDetail(details);

            //todo 参数转换
            request.setProjectId(projectId);
            try {
                StdInvoiceCreateResponse response = invoiceVendorHandler.InvoiceCreate(request);//todo 诺诺发票开票失败重开接口问题
                if (response.getStd_flag()) {//请求成功 保存本地发票记录
                    //发票返回信息
                    postInvoice.setCreatedate(response.getDate());//开票成功日期
                    postInvoice.setInvoiceno(response.getInvoiceno());
                    postInvoice.setInvoiceid(response.getInvoiceid());//发票流水号
                    postInvoice.setInvoicecode(response.getInvoicecode());//发票代码
                    postInvoice.setCheckcode(response.getCheckcode());//发票检查码
                    postInvoice.setPfdurl(response.getPfdurl());
                    postInvoice.setQrurl(response.getQrurl());
                    postInvoice.setExtaxamount(response.getExtaxamount());//不含税金额
                    postInvoice.setIncltaxamount(response.getIncltaxamount());//含税金额
                    //调用发票查询接口后再更改状态
                    postInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());//初始化发票开票成功状态
                    invoiceMapper.saveAndFlush(postInvoice);
                    String content = StrUtil.format("【发票开具】申请开票，主订单号：{}, 发票流水号：{}",
                            postInvoice.getBookingid(), postInvoice.getInvoiceid());
                    UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
                            postInvoice.getBookingid(), content, projectId);
                } else {
                    throw new DefinedException(response.getStd_sub_msg());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 查询
     *
     * @param unFinishList 未完成的正票/红票记录
     * @param lred         标识查询红冲记录还是正票记录
     */
    @Override
    public void invoiceQueryAndUpdate(List<Invoice> unFinishList, String projectId, boolean lred) {
        if (CollectionUtil.isNotEmpty(unFinishList)) {
            try {
                for (Invoice dbInvoice : unFinishList) {
                    //todo 做批量查询 50个一组 对应50个记录修改
                    StdInvoiceQueryRequest request = new StdInvoiceQueryRequest();
                    request.setProjectId(projectId);
                    request.setIsOfferDetail("1");
                    List<String> invoiceIds = new ArrayList<>();
                    //todo  检查缓存到期时间 避免重复查询
                    if (lred) {
                        invoiceIds.add(dbInvoice.getRedinvoiceid());//查询红票
                    } else {
                        invoiceIds.add(dbInvoice.getInvoiceid());//查询正票
                    }
                    request.setInvoiceids(invoiceIds);
                    httpQueryAndUpdate(request, dbInvoice, projectId, lred);
                }
            } catch (DefinedException e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    public void outPutExcel(InvoiceListReq req, HttpServletResponse httpServletResponse) {
        req.getPages().setCurrentpage(1);
        req.getPages().setPagesize(10000);
        InvoiceListRes res = queryList(req);
        List<InvoiceListRes.InvoiceListData> list = res.getRecords();


        if (CollectionUtil.isNotEmpty(list)) {
            String fileName = "发票记录";
            String sheetName = "发票记录";
            EasyExcelUtil.outPortExcel(fileName, InvoiceListRes.InvoiceListData.class, sheetName, httpServletResponse, list);
            System.out.println(GlobalContext.getCurrentUserId() + "导出发票记录Excel");

        }
    }

    /**
     * 诺诺发票申请红字确认单接口回调 第一次返回
     *
     * @param projectId
     * @param data
     */
    @Override
    public void invoiceRedConfirmCallback(String projectId, String data) {
        // 回调消息第一次是红字确定信息  NNInvoiceRedConfirmCallBackSuccessResponse信息，第二次回调 NNInvoiceCallBackSuccessResponse信息，
        //区别字段c_yfphm标识的是蓝字发票的发票号码invoiceno
        NNInvoiceCallBackSuccessResponse successResponse = JSON.parseObject(data, NNInvoiceCallBackSuccessResponse.class);
        String status = successResponse.getCStatus();
        String invoiceno = successResponse.getC_yfphm();//蓝字发票号码
        if (StringUtils.isNotBlank(invoiceno)) {//已经红冲返回信息
            Invoice dbInvoice = invoiceMapper.findByInvoicenoAndProjectid(invoiceno, projectId);
            if ("1".equals(status)) {
                //避免多次回调 重复
                if (dbInvoice != null && StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(dbInvoice.getIstatus()) &&
                        StringUtils.isBlank(dbInvoice.getInvoicecode())) {
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REDED.getCode());
                    dbInvoice.setRedinvoiceid(successResponse.getCFpqqlsh());//冲红发票流水号
                    dbInvoice.setRedcreatedate(DateUtil.date(Long.parseLong(successResponse.getCKprq())));//红冲开票日期
                    dbInvoice.setQrurl(successResponse.getQrCode());//二维码
                    dbInvoice.setRedinvoiceno(successResponse.getCFphm());//发票号码
                    dbInvoice.setRedinvoicecode(successResponse.getCFpdm());//发票代码
                    dbInvoice.setRedpfdurl(successResponse.getCUrl());//pdf
                    dbInvoice.setRedcheckcode(successResponse.getCheckCode());//校验码
                    dbInvoice.setRedextaxamount(successResponse.getCBhsje());//不含税金额
                    dbInvoice.setRedincltaxamount(successResponse.getCHjje());//含税金额
                    dbInvoice.setRedjpgurl(successResponse.getCImgUrls());//发票图片 逗号分割
                    String content = StrUtil.format("【发票红冲】红冲发票开具成功，红冲发票流水号：{}，红冲发票代码：{}，红冲发票号码：{},红冲金额:{}",
                            dbInvoice.getRedinvoiceid(), dbInvoice.getRedinvoicecode(), dbInvoice.getRedinvoiceno(),
                            dbInvoice.getRedextaxamount());
                    log.info(content);
                    invoiceMapper.saveAndFlush(dbInvoice);//更新发票状态
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                            dbInvoice.getBookingid(), content, projectId);
                    //红冲成功后查看是否有待开发票
                    Invoice initInvoice = invoiceMapper.findByBookingidAndProjectid(dbInvoice.getBookingid(), projectId);
                    if (initInvoice != null) { //抬头修改红冲发票
                        invoiceCreate(initInvoice);
                    }
                }
            } else if (dbInvoice != null && "2".equals(status)) {//发票申请失败
                NNInvoiceCallBackFailResponse failResponse = JSON.parseObject(data, NNInvoiceCallBackFailResponse.class);
                if (failResponse != null) {
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REFUSE.getCode());
                    String content = StrUtil.format("【发票红冲】红冲发票开具失败 " + failResponse.getCErrorMessage());
                    log.info(content);
                    invoiceMapper.saveAndFlush(dbInvoice);//更新发票状态
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                            dbInvoice.getBookingid(), content, projectId);
                } else {
                    log.info("红冲发票回调信息获取失败，红冲发票流水号：" + dbInvoice);
                }
            }

        }

    }

    @Override
    public void invoiceConfirmStatus(InvoiceConfirmReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        String userId = GlobalContext.getCurrentUserId();
        BigDecimal orderAmount = BigDecimal.ZERO;
        Invoice dbInvoice = invoiceMapper.findBySqlid(req.getSqlid());
        if (dbInvoice == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票记录不存在"));
        }
        String iStatus = dbInvoice.getIstatus();
        //只有初始化和完成状态才能操作
        if (!StatusUtil.InvoiceStatusEnum.INIT.getCode().equals(iStatus) &&
                !StatusUtil.InvoiceStatusEnum.REPUSH.getCode().equals(iStatus)
         && !StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(iStatus)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票状态异常，无法操作"));
        }
        String bookingId = dbInvoice.getBookingid();
        if (!bookingId.startsWith("Y")) {//正常主单
            Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
            if (rs == null) {
                Booking_rs_his rs_his = SpringUtil.getBean(BookingrsHisMapper.class).findByBookingidAndProjectid(bookingId, projectId);
                if (rs_his != null) {
                    orderAmount = rs_his.getAmount();
                }
            }else {
                orderAmount = rs.getAmount();
            }
        }else {
            Act_rs rs = actrsMapper.findAct_rsByBookingid(bookingId);
            if (rs != null) {
                orderAmount = rs.getAmount();
            }
        }

        if (StringUtils.isNotBlank(req.getIncoideid())) {
            dbInvoice.setInvoiceid(req.getIncoideid());
        }
        if (StringUtils.isNotBlank(req.getIncoideno())) {
            dbInvoice.setInvoiceno(req.getIncoideno());
        }

        if (StringUtils.isNotBlank(req.getIncoidecode())) {
            dbInvoice.setInvoicecode(req.getIncoidecode());
        }
        if (StringUtils.isNotBlank(req.getCheckcode())) {
            dbInvoice.setCheckcode(req.getCheckcode());
        }
        if (StringUtils.isNotBlank(req.getJpgurl())) {
            dbInvoice.setJpgurl(req.getJpgurl());
        }
        if (dbInvoice.getCreatedate() == null || dbInvoice.getCreatedate().after(SystemUtil.EMPTY_DATETIME)) {
            if (req.getCreateDate() != null) {
                dbInvoice.setCreatedate(req.getCreateDate());
            } else {
                dbInvoice.setCreatedate(new Date());
            }
        }
        //不含税金额
        dbInvoice.setIncltaxamount(orderAmount);
        dbInvoice.setHandler(userId);
        dbInvoice.setHandletime(new Date());
        //调用发票查询接口后再更改状态
        dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.FINISH.getCode());//开票成功
        invoiceMapper.saveAndFlush(dbInvoice);
        //第一次确认保存
        if (StatusUtil.InvoiceStatusEnum.INIT.getCode().equals(iStatus)) {
            String content = StrUtil.format("发票申请手动确认，发票流水号：{},操作人:{}", dbInvoice.getInvoiceid(), userId);
            userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                    dbInvoice.getBookingid(), content, projectId);
        }else if (StatusUtil.InvoiceStatusEnum.REPUSH.getCode().equals(iStatus)) {
            String content = StrUtil.format("发票申请重发，操作人:{}",  userId);
            userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                    dbInvoice.getBookingid(), content, projectId);
        }

    }

    /**
     * 检查是否本地开票
     *
     * @return
     */
    @Override
    public InvoiceLocalInfoRes invoiceIsLocalType() {
        InvoiceLocalInfoRes res = new InvoiceLocalInfoRes();
        String projectId = GlobalContext.getCurrentProjectId();
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysconf = sysConfCache.getOne(projectId);
        if (sysconf != null && VendorType.LOCAL_INVOICE.name().equals(sysconf.getInvoicevendor())) {
            res.setLocal(true);
        }
        return res;
    }

    /**
     * 获取订单发票抬头信息
     * @param req
     * @return
     */
    @Override
    public InvoiceLoadTitleRes loadInvoiceTitle(InvoiceLoadReq req) {
        InvoiceLoadTitleRes res = new InvoiceLoadTitleRes();
        Invoice dbInvoice = invoiceMapper.findBySqlid(req.getSqlid());
        if (dbInvoice == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票记录不存在"));
        }
        BeanUtils.copyPropertiesIgnoreNull(dbInvoice,res);
        return res;
    }

    /**
     * 手动拒绝开票
     * @param req
     */
    @Override
    public void invoiceRefuse(InvoiceRefuseReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        String userId = GlobalContext.getCurrentUserId();
        Invoice dbInvoice = invoiceMapper.findBySqlid(req.getSqlid());
        if (dbInvoice == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票记录不存在"));
        }
        if (!dbInvoice.getIstatus().equals(StatusUtil.InvoiceStatusEnum.INIT.getCode())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票状态异常，待开发票方可拒绝"));
        }
        invoiceMapper.delete(dbInvoice);//更新

        String  content = StrUtil.format("【发票拒绝开票】 拒绝开票,删除发票记录，操作人：{},",
                userId);
        userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.DELETE, SystemUtil.DEFAULTUSERID,
                dbInvoice.getBookingid(), content, projectId);


    }

    /**
     * 发票红冲拒绝
     * @param req
     */
    @Override
    public void invoiceRedRefuse(InvoiceLoadReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        String userId = GlobalContext.getCurrentUserId();
        Invoice dbInvoice = invoiceMapper.findBySqlid(req.getSqlid());
        if (dbInvoice == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
        }
        if (!dbInvoice.getIstatus().equals(StatusUtil.InvoiceStatusEnum.NEEDRED.getCode())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票状态异常，无法拒绝"));
        }
        dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.FINISH.getCode());
        dbInvoice.setHandletime(new Date());
        dbInvoice.setHandler(userId);
        invoiceMapper.saveAndFlush(dbInvoice);
        String  content = StrUtil.format("【发票拒绝冲红发票】 拒绝冲红发票，操作人：{},",
                userId);
        userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                dbInvoice.getBookingid(), content, projectId);

    }

    /**
     * @param request   请求参数
     * @param dbInvoice 开票记录
     * @param projectId 项目ID
     * @param lred      标识发票类型  true-红冲  false-正票
     * @return
     * @throws DefinedException
     */
    @Async("commonPool")
    public CompletableFuture<String> httpQueryAndUpdate(StdInvoiceQueryRequest request, Invoice dbInvoice, String projectId, boolean lred) throws DefinedException {
        Stopwatch stopwatch = Stopwatch.createStarted();
        InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
        StdInvoiceQueryResponse response = invoiceVendorHandler.InvoiceQuery(request);
        //todo 发票流水号做个缓存，开票可能通过在盖章等流程中， 避免重复查询
        if (response.getStd_flag()) { //响应成功
            String content;
            if (StringUtils.isNotBlank(response.getStatus()) && response.getStatus().equals("2")) { //开票成功
                if (lred) {
                    //红冲记录响应返回
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REDED.getCode());
                    dbInvoice.setRedcreatedate(response.getDate());//红冲开票成功日期
                    dbInvoice.setRedinvoiceno(response.getInvoiceno());//红冲发票号码
                    dbInvoice.setRedinvoicecode(response.getInvoicecode());//红冲发票代码
                    dbInvoice.setRedcheckcode(response.getCheckcode());//红冲发票检查码
                    dbInvoice.setRedpfdurl(response.getPfdurl());
                    dbInvoice.setRedextaxamount(response.getExtaxamount());//红冲不含税金额
                    dbInvoice.setRedincltaxamount(response.getIncltaxamount());//红冲含税金额
                    dbInvoice.setJpgurl(response.getJpgdurl());
                    content = StrUtil.format("【发票红冲开具】 发票红冲成功，红冲发票流水号：{}，红冲发票代码：{}，红冲发票号码：{},红冲金额:{}",
                            response.getInvoiceid(), response.getInvoicecode(), response.getInvoiceno(), response.getExtaxamount());
                    invoiceMapper.saveAndFlush(dbInvoice);//更新
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                            dbInvoice.getBookingid(), content, projectId);
                    //冲红成功后查询本地有无需要重开发票记录
                    List<Invoice> dbList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(dbInvoice.getBookingid(), dbInvoice.getProjectid());
                    if (StatusUtil.InvoiceStatusEnum.INIT.getCode().equals(dbList.get(0).getIstatus()) && StringUtils.isBlank(dbList.get(0).getInvoiceid())) {
                        //状态为未开，没有正票发票流水号 标识等待红冲结束后重开
                        InvoiceCreateHttp(dbList.get(0));
                    }
                } else {
                    //正票查询返回
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.FINISH.getCode());
                    dbInvoice.setCreatedate(response.getDate());//开票成功日期
                    dbInvoice.setInvoiceno(response.getInvoiceno());
                    dbInvoice.setInvoicecode(response.getInvoicecode());//发票代码
                    dbInvoice.setCheckcode(response.getCheckcode());//发票检查码
                    dbInvoice.setPfdurl(response.getPfdurl());
                    dbInvoice.setQrurl(response.getQrurl());
                    dbInvoice.setExtaxamount(response.getExtaxamount());//不含税金额
                    dbInvoice.setIncltaxamount(response.getIncltaxamount());//含税金额
                    dbInvoice.setJpgurl(response.getJpgdurl());//发票图片地址
                    content = StrUtil.format("【发票开具】申请开票，发票流水号：{}，发票代码：{}，发票号码：{},开具金额:{},含税金额:{}",
                            response.getInvoiceid(), response.getInvoicecode(), response.getInvoiceno(),
                            response.getExtaxamount(), response.getIncltaxamount());
                    invoiceMapper.saveAndFlush(dbInvoice);//更新
                    userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                            dbInvoice.getBookingid(), content, projectId);
                }

            } else if (StringUtils.isNotBlank(response.getStatus()) && response.getStatus().equals("22")) {//开票失败
                dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REFUSE.getCode());
                invoiceMapper.saveAndFlush(dbInvoice);//更新
                content = StrUtil.format("【开票失败】开票，发票流水号：{}，", request.getInvoiceids().get(0));
                userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                        dbInvoice.getBookingid(), content, projectId);
            }

            log.info("发票查询更新耗时：{}，发票流水号：{}，发票状态：{}", stopwatch.stop(), request.getInvoiceids().get(0), response.getStatusMsg());
        }
        return CompletableFuture.completedFuture("OK");

    }

    /**
     * 创建发票记录
     *
     * @param dbInvoice
     */
    private void InvoiceCreateHttp(Invoice dbInvoice) throws DefinedException {
        String projectId = dbInvoice.getProjectid();
        String bookingId = dbInvoice.getBookingid();
        InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
        StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
        request.setTitle(dbInvoice.getTitle());//抬头
        request.setEmail(dbInvoice.getEmail());//接收电子发票的邮箱
        request.setBookingid(dbInvoice.getOrderno());
        if (dbInvoice.getTitletype() == 2) { //企业类型选填
            request.setTaxno(dbInvoice.getTaxno());//企业类型抬头必填
            if (StringUtils.isNotBlank(dbInvoice.getAddress())) {
                request.setAddress(dbInvoice.getAddress());
            }
            if (StringUtils.isNotBlank(dbInvoice.getTel())) {
                request.setTel(dbInvoice.getTel());
            }
            if (StringUtils.isNotBlank(dbInvoice.getBankname())) {
                request.setBankname(dbInvoice.getBankname());
            }
            if (StringUtils.isNotBlank(dbInvoice.getBankno())) {
                request.setBankno(dbInvoice.getBankno());
            }
        }
        //发票明细 按照订单
        List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
        details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), false));//订单信息转换发票明细
        request.setDetail(details);

        //todo 参数转换
        request.setProjectId(projectId);
        StdInvoiceCreateResponse response = invoiceVendorHandler.InvoiceCreate(request);//todo 诺诺发票开票失败重开接口问题
        if (response.getStd_flag()) {//请求成功 保存本地发票记录
            //发票返回信息
            dbInvoice.setCreatedate(response.getDate());//开票成功日期
            dbInvoice.setInvoiceno(response.getInvoiceno());
            dbInvoice.setInvoiceid(response.getInvoiceid());//发票流水号
            dbInvoice.setInvoicecode(response.getInvoicecode());//发票代码
            dbInvoice.setCheckcode(response.getCheckcode());//发票检查码
            dbInvoice.setPfdurl(response.getPfdurl());
            dbInvoice.setQrurl(response.getQrurl());
            dbInvoice.setExtaxamount(response.getExtaxamount());//不含税金额
            dbInvoice.setIncltaxamount(response.getIncltaxamount());//含税金额
            //调用发票查询接口后再更改状态
            dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());//初始化发票开票成功状态
            invoiceMapper.saveAndFlush(dbInvoice);
            String content = StrUtil.format("【发票开具】申请开票，发票主订单号：{}，发票流水号：{}", bookingId, dbInvoice.getInvoiceid());
            UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
            userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
                    bookingId, content, projectId);
        }
    }
}
