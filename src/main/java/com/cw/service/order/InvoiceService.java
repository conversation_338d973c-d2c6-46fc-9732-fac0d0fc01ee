package com.cw.service.order;

import com.cw.entity.Invoice;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.StdInvoicePushRequest;
import com.cw.pojo.dto.order.req.*;
import com.cw.pojo.dto.order.res.InvoiceListRes;
import com.cw.pojo.dto.order.res.InvoiceLoadRes;
import com.cw.pojo.dto.order.res.InvoiceLoadTitleRes;
import com.cw.pojo.dto.order.res.InvoiceLocalInfoRes;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Describe 发票管理
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/13 0013
 */
public interface InvoiceService {

    InvoiceListRes queryList(InvoiceListReq req);

    InvoiceLoadRes load(InvoiceLoadReq req);

    void invoiceRed(InvoiceRedReq req) throws DefinedException;

    void invoiceReCreate(String invoiceid, String projectid) throws DefinedException;


    void invoicePush(StdInvoicePushRequest req) throws DefinedException;


    void invoiceCallback(String serialNum, String saleTaxNum, String projectId, String status, String data);

    void invoiceQueryAndUpdate(List<Invoice> unFinishList, String projectid, boolean lred);

    void outPutExcel(InvoiceListReq req, HttpServletResponse httpServletResponse);

    void invoiceRedConfirmCallback(String projectId, String data);

    void invoiceConfirmStatus(InvoiceConfirmReq req);

    InvoiceLocalInfoRes invoiceIsLocalType();

    InvoiceLoadTitleRes loadInvoiceTitle(InvoiceLoadReq req);

    void invoiceRefuse(InvoiceRefuseReq req);

    void invoiceRedRefuse(InvoiceLoadReq req);
}
