package com.cw.service.log.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.SpelFomulaFactory;
import com.cw.arithmetic.events.WxMsgTemplateNode;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.customs.TemplateDataHandler;
import com.cw.cache.customs.WxTemplateDataHandler;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.exception.CustomException;
import com.cw.core.orderhandler.SmsHandler;
import com.cw.core.platform.wechat.WxMaConfiguration;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.MsgLogMapper;
import com.cw.mapper.PrepayMapper;
import com.cw.mapper.SubusermsgMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.MsgResult;
import com.cw.pojo.dto.common.req.Common_Msg_Req;
import com.cw.pojo.dto.common.res.Common_Msg_Res;
import com.cw.pojo.dto.order.req.OrderLoadReq;
import com.cw.pojo.entity.SmsCode_Entity;
import com.cw.pojo.log.MsgContentData;
import com.cw.pojo.log.SendMsgData;
import com.cw.pojo.log.req.Msg_BatchSend_Req;
import com.cw.pojo.log.req.Template_BatchSend_Req;
import com.cw.pojo.log.req.Template_Content_Req;
import com.cw.service.context.GlobalContext;
import com.cw.service.log.MsgService;
import com.cw.service.mq.msgmodel.bussiness.AppNotifyMsg;
import com.cw.service.order.OrderService;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.enums.sms.WxAppNotifyEnum;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.IntStream;

/**
 * @Describe 短信日志 发送短信功能 获取短信内容
 * <AUTHOR> Tony Leung
 * @Create on 2021/12/24 0024
 */
@Service
@Slf4j
public class MsgServiceImpl implements MsgService {


    //    @Autowired
//    SmsSender smsSender;
    @Autowired
    MsgLogMapper msgLogMapper;
    @Autowired
    GlobalCache globalCache;

    @Autowired
    CustomData customDataFactory;

    @Autowired
    OrderService orderService;

    @Autowired
    SmsHandler smsHandler;

    @Autowired
    SubusermsgMapper subusermsgMapper;

    @Override
    public Common_Msg_Res queryList(Common_Msg_Req req) {
        int currentPage = req.getPages().getCurrentpage() < 1 ? 0 : req.getPages().getCurrentpage() - 1;
        int pageSize = req.getPages().getPagesize();
        String regno = req.getRegno();
        Date date = req.getDate();
        Boolean onlyFail = req.getOnlyFail();
        Page<Msglog> msglogPage = msgLogMapper.findAll((Root<Msglog> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            if (onlyFail) {
                list.add(cb.equal(root.get("status"), false));
            }
            if (regno != null && !regno.isEmpty()) { //有订单号优先
                list.add(cb.or(cb.equal(root.get("regno"), regno.toUpperCase()),
                        cb.equal(root.get("mobile"), regno.toUpperCase())));
            } else {

                if (date != null && !CalculateDate.emptyDate(date)) {
                    list.add(cb.between(root.get("date"), date, CalculateDate.reckonDay(date, 5, 1)));
                }
            }
            list.add(cb.equal(root.get(SystemUtil.projectClumn), GlobalContext.getCurrentProjectId()));
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p)));
            return query.getRestriction();
        }, PageRequest.of(currentPage, pageSize, Sort.by(req.getPages().getDirection(),
                req.getPages().getSortname().split(","))));
        Common_Msg_Res res = new Common_Msg_Res();
        res.fillPageData(msglogPage);
        List<Common_Msg_Res.Msg_List_Data> data = res.getRecords();
        if (data != null && !data.isEmpty()) {
            for (Common_Msg_Res.Msg_List_Data tempData : data) {
                tempData.setDate(tempData.getDate().length() > 19 ? tempData.getDate().substring(0, 19) : tempData.getDate());
            }
        }
        return res;
    }

/*    @Override
    public MsgResult sendMessage(Template_Send_Req req) throws DefinedException {

        MsgTriggerEnum trigger = Enum.valueOf(MsgTriggerEnum.class, req.getTrigger());
        MsgResult msgResult = sendMessage(null, trigger,
                req.getMobile(), req.getContent(), req.getRegno(), GlobalContext.getCurrentProjectId());//这里只需要手机号和内容
        if (msgResult == null) {
            msgResult = new MsgResult();
            msgResult.setSuccess(false);
            msgResult.setMessage("短信接口已关闭");
        }
        return msgResult;
    }*/

    @Override
    public MsgResult sendMessage(Object object, MsgTriggerEnum trigger, String mobile,
                                 String content, String regno, String projectid) throws DefinedException {
        String outid = "";
        Map<String, String> dynamicMap = Maps.newHashMap();
        TemplateDataHandler handler = (TemplateDataHandler) customDataFactory.getHandler(SystemUtil.CustomDataKey.template);
        //国外短信号码00开头
        if (!mobile.isEmpty() && (mobile.startsWith("00")  || mobile.startsWith("+")  || Pattern.matches("^1[3-9][0-9]{9}$", mobile))) {//匹配手机号
            //2024.05.13 不再支持直接发送content 内容.必须全部指定模版内容进行发送.

            List<Template> smsTrigerTemplate = handler.getTemplateList(trigger, projectid);//加载所有已经开启访问的模版
            for (Template template : smsTrigerTemplate) { //遍历模版
                boolean lsend = template.getCondition().isEmpty() ? true : SpelFomulaFactory.getBooleanFomulaResult(template.getCondition(), object);  //条件字段如果为空,必发送.否则判断公式是否满足.
                if (!lsend) {
                    continue;
                }
                String sendContent = SpelFomulaFactory.getStringFomulaResult(template.getContent(), object);   //将模板中的公式替换成实际值
                outid = template.getOutid();//获取外部模板号

                if (StringUtils.isNotBlank(template.getParam())) {
                    //对比模板获取参数值
                    List<String> paramValues = getDifferentStrParamValues(sendContent, template.getContent());
                    //获取模板参数名集合
                    List<String> paramKeys = Arrays.asList(template.getParam().split(","));
                    if (CollectionUtil.isNotEmpty(paramValues)) {
                        //填充参数集合
                        dynamicMap = fillParamMap(paramKeys, paramValues);
                    }
                }

                if (StringUtils.isNotBlank(sendContent)) {
                    boolean lok = smsHandler.sendSms(projectid, mobile, sendContent, outid, dynamicMap);
                    MsgResult msgResult = new MsgResult();
                    msgResult.setSuccess(lok);
                    msgResult.setMessage(lok ? "发送成功" : "发送失败");
                    if (!lok) {
                        log.error("短信发送失败: mobile: {}, content: {}", mobile, sendContent);
                    }
                    SystemUtil.MsgType msgType = Enum.valueOf(SystemUtil.MsgType.class, trigger.getGroup().name());
                    writeMsgLog(msgType, regno, mobile, sendContent, lok, projectid);
                    continue;
                } else {
                    log.error("短信发送失败: 发送内容为空");
                    continue;
                }

            }

            return getMsgResult(true, "已发送");
        } else {
            log.error("短信发送失败: 手机号码格式不正确 mobile: {}", mobile);
            return getMsgResult(false, "手机号码格式不正确");
        }

    }


    /**
     * @param paramKeys
     * @param paramValues
     * @return
     */
    private Map<String, String> fillParamMap(List<String> paramKeys, List<String> paramValues) {
        Map<String, String> map = IntStream.range(0, paramKeys.size()).collect(HashMap::new,
                (m, i) -> m.put(paramKeys.get(i), paramValues.get(i)), (m, n) -> {
                });
        return map;
    }

    /**
     * @param result 解析后的字符串
     * @param org    解析前字符串
     * @return 对比解析前后字符，获取参数值集合
     */
    private List<String> getDifferentStrParamValues(String result, String org) {
        String orgStr = org.replaceAll("\\'", "");//去除符号
        List<String> orgStrList = Arrays.asList(orgStr.split("\\+"));//分割带参数的表达式文字，用+分割
        String paramsStr = result;
        for (int i = 0; i < orgStrList.size(); i++) {
            if (result.contains(orgStrList.get(i))) {
                if (i == 0 || i == (orgStrList.size() - 1)) { //判断前后元素，是否添加逗号
                    paramsStr = paramsStr.replace(orgStrList.get(i), "");
                } else {
                    paramsStr = paramsStr.replace(orgStrList.get(i), ",");
                }
            }
        }

        return Arrays.asList(paramsStr.split(","));
    }

    private String getWxAppOpenId(Object obj) {
        if (obj instanceof Feedback) {
            return ((Feedback) obj).getWxopenid();
        }
        if (obj instanceof AppNotifyMsg) {
            return ((AppNotifyMsg) obj).getWxOpenId();
        }

        return "";
    }

    @Override
    public MsgResult sendWxAppSubscrbeMessage(Object object, MsgTriggerEnum trigger, String mobile, String content, String regno, String projectid) throws DefinedException {
        MsgResult msgResult = new MsgResult();
        msgResult.setSuccess(true);
        msgResult.setMessage("发送成功");
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig wxconfig = configCache.getVendorConfig(projectid, VendorType.WX_APP);

        WxTemplateDataHandler handler = (WxTemplateDataHandler) customDataFactory.getHandler(SystemUtil.CustomDataKey.wxtemplate);
        Wxtemplate template = handler.getTemplate("", trigger, projectid);

        String wxopenid = getWxAppOpenId(object);


        if (wxconfig == null || template == null || StrUtil.isBlank(wxopenid)) {
            return msgResult;
        }
        List<WxMsgTemplateNode> nodes = JSON.parseArray(template.getContent(), WxMsgTemplateNode.class);
        for (WxMsgTemplateNode node : nodes) {
            node.setValue(SpelFomulaFactory.getStringFomulaResult(node.getFormula(), object));//将每个节点做公式运算
        }
        WxMaService maService = WxMaConfiguration.getMaService(wxconfig.getAppid());
        WxMaSubscribeMessage subscribeMessage = new WxMaSubscribeMessage();
        subscribeMessage.setPage(SpelFomulaFactory.getStringFomulaResult(template.getPageurl(), object));
        subscribeMessage.setTemplateId(template.getOutid());
        subscribeMessage.setToUser(wxopenid);
        subscribeMessage.setMiniprogramState(WxAppNotifyEnum.getMiniAppState(template.getTarget()));//发送目的地.默认是正式版

        for (WxMsgTemplateNode node : nodes) {
            WxMaSubscribeMessage.MsgData msgData = new WxMaSubscribeMessage.MsgData();
            msgData.setName(node.getParam());
            msgData.setValue(StrUtil.sub(node.getValue(), 0, 20));   //暂定全部用字符串通知.最长20位
            subscribeMessage.addData(msgData);
        }

        try {
            maService.getMsgService().sendSubscribeMsg(subscribeMessage);
            log.info("发送微信订阅成功" + JSON.toJSONString(subscribeMessage));
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        return msgResult;
    }

    @Override
    public List<SendMsgData> batchSendMessage(Template_BatchSend_Req req) throws DefinedException {
        List<SendMsgData> sendMsgDataList = new ArrayList<>();
        String content = req.getContent();
        String templateCode = req.getTemplateCode();
        MsgTriggerEnum trigger = Enum.valueOf(MsgTriggerEnum.class, req.getTrigger());
        List<Msg_BatchSend_Req> datas = req.getDatas();
        if (datas != null && !datas.isEmpty()) {
            SendMsgData sendMsgData = null;
            String outerid = "";
            String projectId = GlobalContext.getCurrentProjectId();
            Template template = (Template) GlobalCache.getDataStructure()
                    .getCache(SystemUtil.GlobalDataType.TEMPLATE).getRecord(projectId, templateCode);
            if (template != null) {
                outerid = template.getOutid();
            }
            for (Msg_BatchSend_Req msgData : datas) {
                sendMsgData = new SendMsgData();
                sendMsgData.setSqlid(msgData.getSqlid());
                sendMsgData.setName(msgData.getName());
                sendMsgData.setTelephone(msgData.getTelephone());
                //if (msgInterfaceOpen) {
                String mobile = msgData.getTelephone();
                if (!mobile.isEmpty() && Pattern.matches("^1[3-9][0-9]{9}$", mobile)) {//匹配手机号
                    String regno = "";
                    Map<String, String> dynamicMap = Maps.newHashMap();
                    if (!content.isEmpty()) {
                        //解析content
                        Object[] objValue = getEntityObject(msgData.getSqlid(), trigger, msgData.getName(), msgData.getTelephone());
                        regno = (String) objValue[1];
                        MsgContentData msgContentData = calcTemplateContent(objValue[0], content);
                        content = msgContentData.getContent();
                        dynamicMap = msgContentData.getParams();//如果是阿里云的发送.就需要这个
                    }
                    if (!content.isEmpty()) {
                        boolean lok = smsHandler.sendSms(projectId, mobile, content, outerid, dynamicMap);
                        sendMsgData.setSuccess(lok);
                        sendMsgData.setSend(lok ? "发送成功" : "发送失败");
                        //写日志
                        SystemUtil.MsgType msgType = Enum.valueOf(SystemUtil.MsgType.class, trigger.getGroup().name());
                        writeMsgLog(msgType, regno, mobile, content, lok, projectId);
                    } else {
                        sendMsgData.setSuccess(false);
                        sendMsgData.setSend("发送内容为空");
                    }
                } else {
                    sendMsgData.setSuccess(false);
                    sendMsgData.setSend("手机号码格式不正确");
                }
                sendMsgDataList.add(sendMsgData);
            }
        }
        return sendMsgDataList;
    }

    @Override
    public List<String> loadContent(Template_Content_Req req) {
        String projectid = GlobalContext.getCurrentProjectId();
        List<String> contentList = new ArrayList<>();
        Template template = null;
        if (!EnumUtils.isValidEnumIgnoreCase(MsgTriggerEnum.class, req.getTrigger())) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("trigger代码不正确"));
        }
        MsgTriggerEnum msgTrigger = Enum.valueOf(MsgTriggerEnum.class, req.getTrigger());
        TemplateDataHandler handler = (TemplateDataHandler) customDataFactory.getHandler(SystemUtil.CustomDataKey.template);
        if (handler != null) {
            //通过模板代码
            template = handler.getTemplate("", msgTrigger, projectid);
        }
        if (template != null) {
            String content = template.getContent();
            //解析内容
            Object[] objValue = getEntityObject(req.getSqlid(), msgTrigger, "", "");
            //MsgContentData msgContentData = calcTemplateContent(objValue[0], content);
            //content = msgContentData.getContent();
            content = SpelFomulaFactory.getStringFomulaResult(template.getContent(), objValue[0]);
            contentList.add(content);

        }
        return contentList;
    }

    /**
     * @param objId      订单sqlid
     * @param msgTrigger 消息类型
     * @param name
     * @param mobile
     * @return
     */
    private Object[] getEntityObject(Long objId, MsgTriggerEnum msgTrigger, String name, String mobile) {
        String regno = "";
        Object object = null;
        if (msgTrigger.getGroup().equals(SystemUtil.MsgType.M)) { //客房短信
            if (objId != null && objId > 0) {
                OrderLoadReq loadReq = new OrderLoadReq();
                loadReq.setSqlid(objId);
                object = orderService.load(loadReq);
            }
            if (object != null) {
                regno = ((com.cw.pojo.entity.Booking_rs_Entity) object).getBookingid();
            } else {
                object = new Booking_rs();
                ((Booking_rs) object).setBookername(name);
                ((Booking_rs) object).setTel(mobile);
            }
        } else if (msgTrigger.getGroup().equals(SystemUtil.MsgType.V)) { //验证码
            String randomCode = RandomUtil.randomString("0123456789", 6);
            object = new SmsCode_Entity(randomCode);
        } else if (msgTrigger.getGroup().equals(SystemUtil.MsgType.R)) { //退款
            if (objId != null && objId > 0) {
                OrderLoadReq loadReq = new OrderLoadReq();
                loadReq.setSqlid(objId);
                object = orderService.load(loadReq);
            }
            if (object != null) {
                regno = ((com.cw.pojo.entity.Booking_rs_Entity) object).getBookingid();
                object = SpringUtil.getBean(PrepayMapper.class).findPrepayByBookingid(regno);//查找主订单号 去查找prepay表数据退款数据
            } else {
                object = new Booking_rs();
                ((Booking_rs) object).setBookername(name);
                ((Booking_rs) object).setTel(mobile);
            }
        }
        return new Object[]{object, regno};
    }

    /**
     * @param success
     * @param msg
     * @return 短信发送状态
     */
    private MsgResult getMsgResult(Boolean success, String msg) {
        MsgResult msgResult = new MsgResult();
        msgResult.setSuccess(success);
        msgResult.setMessage(msg);
        return msgResult;
    }

    /**
     * 保存短信发送日志
     *
     * @param msgType
     * @param regno
     * @param mobile
     * @param content
     * @param send
     */
    private void writeMsgLog(SystemUtil.MsgType msgType, String regno, String mobile, String content, Boolean send, String projectId) {
        Msglog msglog = new Msglog();
        msglog.setGroup(msgType.name());
        msglog.setRegno(regno);
        msglog.setDate(new Date());
        msglog.setMobile(mobile);
        msglog.setContent(content);
        msglog.setStatus(send);
        msglog.setProjectid(projectId);
        msgLogMapper.saveAndFlush(msglog);
        //globalCache.refreshAndNotify(SystemUtil.GlobalDataType.ROOMTYPE, projectId);

    }

    private MsgContentData calcTemplateContent(Object object, String content) {
        MsgContentData msgContentData = new MsgContentData();
        Map<String, String> params = new HashMap<>();
        if (object != null) {
            //Pattern pattern = Pattern.compile("#\\{(\\w*\\.\\w*[,\\w]*)\\}");
            Pattern pattern = Pattern.compile("#\\{(\\w*\\.\\w*[,\\w]*)\\}");
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                String patternStr = matcher.group();
                if (patternStr.length() > 3) {
                    String subStr = patternStr.substring(2, patternStr.length() - 1);
                    String[] array = subStr.split("\\.");
                    String table = "";
                    String field = "";
                    String other = "";
                    String[] otherArray = null;
                    if (array.length > 1) {
                        table = array[0];
                        other = array[1];
                        if (other.contains(",")) {
                            //增加对字符串截取处理
                            otherArray = other.split(",");
                            field = otherArray[0];
                        } else {
                            field = other;
                        }
                        try {
                            Class<?> clazz = Class.forName("com.cw.pojo.entity." + table);
                            Method method = clazz.getMethod("get" + field.substring(0, 1).toUpperCase() + field.substring(1));
                            String value = "";
                            Object returnObj = method.invoke(object);
                            if (returnObj instanceof Date) {
                                value = CalculateDate.dateToString((Date) returnObj);
                            } else {
                                value = returnObj.toString();
                            }
                            if (otherArray != null) {
                                try {
                                    if (otherArray.length == 2) {
                                        value = value.substring(Integer.parseInt(otherArray[1]));
                                    } else if (otherArray.length == 3) {
                                        value = value.substring(Integer.parseInt(otherArray[1]), Integer.parseInt(otherArray[2]));
                                    }
                                } catch (StringIndexOutOfBoundsException e) {
                                } finally {
                                }
                            }
                            content = content.replaceAll("\\#\\{" + table + "\\." + other + "\\}", value);
                            params.put(field, value);
                        } catch (Exception e) {
                        }
                    }
                }
            }
        }
        msgContentData.setContent(content);
        msgContentData.setParams(params);
        return msgContentData;
    }
}
