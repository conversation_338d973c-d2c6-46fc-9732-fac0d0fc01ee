package com.cw.service.app;

import com.cw.config.confyaml.node.Conf_Map;
import com.cw.exception.DefinedException;
import com.cw.pojo.dto.app.req.*;
import com.cw.pojo.dto.app.res.*;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.res.cms.PageFoot;
import com.cw.pojo.dto.conf.res.cms.PageHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/10 09:23
 **/
public interface AppResourceService {

    /**
     * 获取会议大组列表
     *
     * @param req
     * @return
     */
    AppProductGroupRes getMeetingGroupList(AppMeetingGroupListReq req);


    /**
     * 获取酒店列表
     *
     * @param req
     * @return
     */
    AppHotelListRes getHotelList(AppHotelListReq req);


    /**
     * 获取门票大组列表
     *
     * @param req
     * @return
     */
    AppProductGroupRes getTicketGroupList(AppProductGroupReq req);


    /**
     * 获取套餐大组列表
     *
     * @param req
     * @return
     */
    AppProductGroupRes getKitList(AppKitListReq req);

    /**
     * 获取会议室列表
     *
     * @param req
     * @return
     */
    AppMeetingRoomListRes getMeetingList(AppProductDetailReq req);

    /**
     * 获取指定酒店下房型列表
     *
     * @param req
     * @return
     */
    AppRoomProductListRes getRoomTypeList(AppProductDetailReq req);

    /**
     * 获取大组下的所有门票
     *
     * @param req
     * @return
     */
    AppTicketProductListRes getTicketList(AppProductDetailReq req);

    /**
     * 获取套餐大组下的所有套餐
     *
     * @param req
     * @return
     */
    AppKitProductListRes getKitdetailList(AppProductDetailReq req);


    /**
     * 获取产品价格库存明细
     *
     * @param req
     * @return
     */
    List<AppProductAvailItem> getProductAvailDetail(AppProductQueryAvailReq req);


    /**
     * @param req 获取产品的免费取消时间
     * @return
     */
    Common_response getFreeCancelTime(AppProductQueryAvailReq req);


    /**
     * 计算套餐价格
     *
     * @param req
     * @return
     */
    AppCalcKitDetailRes calcKitPrice(AppCalcKitDetailReq req);


    AppOrderNoticeTextRes getProductNoticeText(AppProductQueryReq req);


    /**
     * 获取额外服务选项
     *
     * @param req
     * @return
     */
    AppExtraServiceRes getExtraServices(AppProductDetailReq req);

    AppProductIntroRes getRoomProductIntro(AppProductIntroReq req);

    AppProductIntroRes getMeetingProductIntro(AppProductIntroReq req);

    AppHotelIntroRes getHotelIntro(AppProductIntroReq req);

    /**
     * @return 获取PC端网页页眉内容
     */

    List<PageHeader> getPageHeaderInfo();

    /**
     * @return 获取PC端网页页脚内容
     */

    PageFoot getPageFootInfo();


    AppSpuGroupRes getSpuGroupList(AppProductGroupReq req);


    AppSpuProductListRes getSpuList(AppProductDetailReq req);


    AppGiftitemListRes getGiftitemList(AppGiftitemListReq req);

    AppGiftitemProductRes getGiftitem(AppProductDetailReq req);

    AppGiftitemSkuAvlPrice getGiftitemAvailDetail(AppActsiteQueryAvailReq req);

    PageFoot getNewPageFootInfo(String projectid);

    List<PageHeader> getNewPageHeaderInfo(String projectId);

    AppPostageDetail getPostage(AppProvinceReq req);

    AppPriceDetail calcPrice(AppCalcPriceReq req) throws DefinedException;

    AppTravelTipListRes getTravelTipList(AppTravelTipListReq req);

    AppTravelTipDetRes getTravelTipDet(String code);

    List<AppTravelTipMap> getTravelTipDetMap(AppTraveltipMapReq req);

    Conf_Map getMapConf(String currentAppProjectId) throws DefinedException;

    AppCouponDetailRes getCouponDetail(AppGetCouponDetailReq req);

    AppPerformListRes getPerformList(AppPerformListReq req);

    AppPerformProductRes getPerform(AppPerformQueryReq req);

    AppIsLocalInvoiceRes queryInvoiceType();
}
