package com.cw.service.app.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.PojoUtils;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.func.Var;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.func.prodfactory.impl.BaseProdInfo;
import com.cw.arithmetic.func.prodfactory.impl.ProdTicketInfo;
import com.cw.arithmetic.func.prodfactory.impl.ProdWareInfo;
import com.cw.arithmetic.lang.R;
import com.cw.arithmetic.others.CodeDetail;
import com.cw.arithmetic.others.SkuDetail;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.customs.WxTemplateDataHandler;
import com.cw.cache.impl.*;
import com.cw.config.confyaml.ConfYaml;
import com.cw.config.confyaml.node.Conf_Map;
import com.cw.core.CoreAvl;
import com.cw.core.CoreCache;
import com.cw.core.CorePrice;
import com.cw.core.CoreSync3;
import com.cw.core.func.order.OrderReqTransFactory;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.SysconfMapper;
import com.cw.pojo.dto.app.req.*;
import com.cw.pojo.dto.app.res.*;
import com.cw.pojo.dto.app.res.node.KitProductItem;
import com.cw.pojo.dto.area.TipCoordinate;
import com.cw.pojo.dto.area.TipsNode;
import com.cw.pojo.dto.area.TipsNodeDet;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.res.cms.BizBanners;
import com.cw.pojo.dto.conf.res.cms.BusinessBanner;
import com.cw.pojo.dto.conf.res.cms.PageFoot;
import com.cw.pojo.dto.conf.res.cms.PageHeader;
import com.cw.pojo.dto.conf.res.perform.PerformSchedule;
import com.cw.pojo.sqlresult.Produce_calcpricePo;
import com.cw.service.app.AppResourceService;
import com.cw.service.config.sys.impl.ManagerToolServiceImpl;
import com.cw.service.context.GlobalContext;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.*;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.*;
import com.cw.utils.enums.menus.MenuContentReserveId;
import com.cw.utils.enums.menus.MenuReserveId;
import com.cw.utils.enums.menus.ProductQrcodePathFactory;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.pagedata.SelectDataNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/8 10:13
 **/
@Slf4j
@Service
public class AppResourceServiceImpl implements AppResourceService {

    @Autowired
    CorePrice corePrice;

    @Autowired
    CoreAvl coreAvl;

    @Autowired
    CoreSync3 coreSync3;

    @Autowired
    SysconfMapper sysconfMapper;

    @Autowired
    CoreCache coreCache;

    @Autowired
    CustomData customDataFactory;


    /**
     * 获取会议大组
     *
     * @param req
     * @return
     */
    @Override
    public AppProductGroupRes getMeetingGroupList(AppMeetingGroupListReq req) {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        MeetingGroupCache meetingGroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP);
        AppProductGroupRes res = new AppProductGroupRes();
        List<Meetinggroup> records = meetingGroupCache.getDataList(projectid);
        List<AppProductGroupRes.ProductGroupListData> list;
        String people = req.getMeetingPeople();
        String type = req.getMeetingType();
        String meetType = req.getActtype();
        String styles = req.getStyle();
        if (records.size() > 0) {
            list = records.stream().filter(Meetinggroup::getLsell).filter(
                    mg -> {
                        //if (StringUtils.isBlank(tags) && StringUtils.isBlank(type) && StringUtils.isBlank(people) &&
                        //        StringUtils.isBlank(meetType) && StringUtils.isBlank(styles)) {
                        //    return true;
                        //}
                        if (StringUtils.isNotBlank(people)) { //判断人数是否在范围
                            if (!ContentCacheTool.getMeetingGroupPeople(projectid, mg.getPeople(), people)) {
                                return false;
                            }
                        }
                        if (StringUtils.isNotBlank(type)) { //判断场地类型是否一致
                            if (!mg.getType().toUpperCase(Locale.ROOT).equals(type.toUpperCase(Locale.ROOT))) {
                                return false;
                            }
                        }
                        if (StringUtils.isNotBlank(meetType)) { //判断活动会议室类型是否一致
                            if (!SysFuncLibTool.lInputMatch(mg.getRoomtype(), meetType)) {
                                return false;
                            }
                        }
                        if (StringUtils.isNotBlank(styles)) { //判断会场下会议室布局风格
                            if (!ContentCacheTool.hasMeetingStyle(projectid, mg, styles)) {
                                return false;
                            }
                        }
                        if (StringUtils.isNotBlank(req.getTags())) { //判断会场标签是否包含所选
                            if (StringUtils.isBlank(mg.getTags())) {
                                return false;
                            } else {
                                if (!SysFuncLibTool.lInputMatch(mg.getTags(), req.getTags())) {
                                    return false;
                                }
                            }
                        }
                        //判断价格是否在区间内显示
                        Var<String> var = new Var<>("");
                        List<BigDecimal> prices = Lists.newArrayList();
                        BigDecimal price = ContentCacheTool.getChildProductCachedMinPrice(ProdType.MEETING, req.getQuerydate(), mg.getCode(), projectid, var, prices);//TODO 查找最低起步价
                        boolean canadd = false;
                        for (BigDecimal checkPrice : prices) {
                            if (NumberUtil.isGreaterOrEqual(checkPrice, req.getPricebegin()) && NumberUtil.isLessOrEqual(checkPrice, req.getPriceend())) {
                                canadd = true;
                                break;
                            }
                        }
                        if (prices.size() > 0 && !canadd) { //如果有符合价格区间的会议室 返回true
                            return false;
                        }
                        return true;
                    }
            ).map(r -> {
                AppProductGroupRes.ProductGroupListData data = new AppProductGroupRes.ProductGroupListData();
                data.setGroupid(r.getCode());
                data.setTitle(R.lang(r.getDescription()));
                data.setIntro(R.lang(r.getIntroduction()));
                data.setPicurl(CollectionUtil.getFirst(Arrays.asList(r.getMslidepics().split(","))));
                data.setPicurls(true ? Arrays.asList(r.getMslidepics().split(",")) : Arrays.asList(r.getSlidepics().split(",")));
                Integer org = RandomUtil.randomInt(500, 999);
                Var<String> var = new Var<String>();
                data.setPriceInfo(ContentCacheTool.getChildProductCachedMinPrice(ProdType.MEETING, req.getQuerydate(), r.getCode(), projectid, var, Lists.newArrayList()).doubleValue()); //TODO 查找子会议室最低价
                data.setOrgpriceInfo(Double.valueOf(org + "")); // todo 参考价格
                data.setTags(Arrays.stream(r.getTags().split(",")).filter(StrUtil::isNotEmpty).collect(Collectors.toList())); //中文标签

                String intro2 = StrUtil.format(R.lang("容纳人数:{}人|会场总面积:{}平方米|会议室数量:{}个|联系电话:{}"), r.getPeople(), r.getSize(), r.getTotalroom(), r.getTel());
                data.setIntro2(intro2);

                return data;
            }).collect(Collectors.toList());
        } else {
            list = Lists.newArrayList();
        }
        res.setRecords(list);

        //res.setTitlepicurl(ProdFactory.getProd(ProdType.MEETING).getBannerPic(projectid));//业态顶图
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        res.setTitlepicurl(ContentCacheTool.getShowPic(projectid, lmobile ? MenuContentReserveId.meetinggrouppic.name() : MenuContentReserveId.pcmeetinggrouppic.name()));//根据
        if (lmobile) {//如果是移动端.加载成功案例
            res.setOthterLinks(Lists.newArrayList());
            MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
            List<Menu_content> contents = menuContentCache.getDataListWithCondition(projectid, p -> p.getMenuid().equals(MenuReserveId.meetexp.name()) && p.getOstatus() == 1
                    && StrUtil.isNotBlank(p.getRichtext()) && StrUtil.isNotBlank(p.getImgurl()));
            for (Menu_content random : contents) {
                MenuContentRes.ContentLinkData data = new MenuContentRes.ContentLinkData();
                data.setTitle(R.lang(random.getTitle()));
                data.setPicurl(R.lang(random.getImgurl()));
                data.setContentid(random.getContentid());
                res.getOthterLinks().add(data);
            }
        }
        return res;
    }

    @Override
    public AppHotelListRes getHotelList(AppHotelListReq req) {
//        Stopwatch  s=Stopwatch.createStarted();

        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        HotelCache hotelCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL);
        List<Hotel> records = hotelCache.getDataList(projectId);
        List<AppHotelListRes.ProductGroupListData> list = new ArrayList<>();
        if (records.size() > 0) {
            List<Hotel> hotelList = records.stream().filter(Hotel::getLsell).collect(Collectors.toList());
            if (StringUtils.isNotBlank(req.getHotelName())) {  //按酒店名称搜索
                hotelList = hotelList.stream().filter(r -> r.getDescription().contains(req.getHotelName())).collect(Collectors.toList());
            }

            if (StringUtils.isNotBlank(req.getHotelStyle())) {//酒店主题
                hotelList = hotelList.stream().filter(r -> r.getGroupid().equals(req.getHotelStyle())).collect(Collectors.toList());
            }
            if (StringUtils.isNotBlank(req.getHotelTags())) {//配套设施
                List<String> hotelTag = Arrays.asList(req.getHotelTags().split(","));
                hotelList = hotelList.stream().filter(r -> Arrays.asList(r.getTags().split(",")).containsAll(hotelTag)).collect(Collectors.toList());
            }
            //酒店房间床型 根据房型缓存的床型查找对应酒店代码
            if (StringUtils.isNotBlank(req.getBedType())) {//酒店房型床型
                hotelList = ContentCacheTool.getHotelCodeByBedTye(projectId, req.getBedType(), hotelList);
            }

            for (Hotel r : hotelList) {
                AppHotelListRes.ProductGroupListData data = new AppHotelListRes.ProductGroupListData();
                data.setGroupid(r.getCode());//酒店代码
                data.setThemecode(r.getGroupid());//主题
                String tags = ContentCacheTool.getSupportDescString(r.getProjectid(), r.getFeaturetags(), true);
                data.setTags(Arrays.stream(r.getTags().split(",")).filter(StrUtil::isNotEmpty).collect(Collectors.toList())); //中文标签


                data.setTitle(R.lang(r.getDescription()));
                data.setPicurl(CollectionUtil.getFirst(Arrays.asList(r.getMslidepics().split(","))));
                data.setSubtitle(r.getIntroduction());
                data.setDeviceInfo(ContentCacheTool.getSupportDescString(projectId, r.getPackagecode(), true));
                data.setSlidepics(Arrays.asList(true ? r.getMslidepics().split(",") : r.getSlidepics().split(",")));

                Var<String> var = new Var<>("");
                List<BigDecimal> prices = Lists.newArrayList();
                BigDecimal price = ContentCacheTool.getChildProductCachedMinPrice(ProdType.ROOM, req.getQuerydate(), r.getCode(), projectId, var, prices);//TODO 查找最低起步价
                data.setPriceInfo(price.doubleValue());  //查找子房型最低价

                boolean canadd = false;
                for (BigDecimal checkPrice : prices) {
                    if (NumberUtil.isGreaterOrEqual(checkPrice, req.getPricebegin()) && NumberUtil.isLessOrEqual(checkPrice, req.getPriceend())) {
                        canadd = true;
                        break;
                    }
                }

                data.setOrgpriceInfo(ProdFactory.getProd(ProdType.ROOM).getOrgPrice(var.getValue(), projectId, price).doubleValue());

                if (canadd) {
                    list.add(data);
                }
            }

        } else {
            list = Lists.newArrayList();
        }
        AppHotelListRes res = new AppHotelListRes();
        //酒店排序
        if (StringUtils.isNotBlank(req.getSort()) && !req.getSort().equals(HotelSortType.SortType_1.getVal())) {
            HotelSortType hotelSortType = HotelSortType.getHotelSortType(req.getSort());
            if (hotelSortType.getSortorder().equals("descending")) {
                Comparator<AppHotelListRes.ProductGroupListData> comparator = Comparator.comparing(
                        AppHotelListRes.ProductGroupListData::getPriceInfo).reversed();
                list.sort(comparator);
            } else {
                list.sort(Comparator.comparing(AppHotelListRes.ProductGroupListData::getPriceInfo));
            }
        }
        res.setRecords(list);

        List<SelectDataNode> selectDataNodes = customDataFactory.getSelectData(projectId, SystemUtil.CustomDataKey.hoteltype);
        res.setThemeinfos(selectDataNodes);


        res.setTitlepicurl(ContentCacheTool.getShowPic(projectId, MenuContentReserveId.pchotelpic.name()));
        //res.setTitlepicurl(ProdFactory.getProd(ProdType.ROOM).getBannerPic(projectId));
        return res;
    }

    @Override
    public AppProductGroupRes getTicketGroupList(AppProductGroupReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        TicketgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
        List<Ticketgroup> records = cache.getDataList(projectId);
        Date currentDate = DateUtil.date();


        Set<String> filterSet = null;
        if (StrUtil.isNotBlank(req.getQrgroup())) {//如果指定大组
            ActqrCache actqrCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTQR);
            Actqr actqr = actqrCache.getRecord(projectId, req.getQrgroup());
            if (actqr != null && !actqr.getGroups().isEmpty()) {
                filterSet = Sets.newHashSet(actqr.getGroups().split(","));
            }
        }

        List<AppProductGroupRes.ProductGroupListData> list = null;
        if (records.size() > 0) {
            Set<String> finalFilterSet = filterSet;
            list = records.stream().filter(Ticketgroup::getLsell).filter(
                    t -> {
                        boolean flag = true;
                        if (!req.getSearchkey().isEmpty()) {
                            if (!t.getDescription().contains(req.getSearchkey()) && !t.getCode().contains(req.getSearchkey())) {
                                flag = false;
                            }
                        }
                        if (!CalculateDate.emptyDate(t.getStartsell()) && !CalculateDate.emptyDate(t.getEndsell())) { //新增.如果增加了可售卖的日期范围,则过滤掉不在范围内的项目
                            if (!CalculateDate.isInRange(currentDate, t.getStartsell(), t.getEndsell())) {
                                flag = false;
                            }
                        }
                        if (finalFilterSet != null && !finalFilterSet.contains(t.getCode())) {//如果指定二维码大组.并且二维码大组内不含有票务代码的话.就不显示
                            flag = false;
                        }
                        return flag;
                    }
            ).map(t -> {
                AppProductGroupRes.ProductGroupListData data = new AppProductGroupRes.ProductGroupListData();
                data.setGroupid(t.getCode());
                data.setTags(new ArrayList<>());
                data.setTitle(R.lang(t.getDescription()));
                data.setPicurl(CollectionUtil.getFirst(Arrays.asList(t.getMslidepics().split(","))));
                data.setPicurls(true ? Arrays.asList(t.getMslidepics().split(",")) : Arrays.asList(t.getSlidepics().split(",")));
                data.setSubtitle(t.getOpeningtext());
                Var<String> var = new Var<String>();
                BigDecimal price = ContentCacheTool.getChildProductCachedMinPrice(ProdType.TICKET, req.getQuerydate(), t.getCode(), projectId, var, Lists.newArrayList());
                data.setPriceInfo(price.doubleValue());
                data.setOrgpriceInfo(ProdFactory.getProd(ProdType.TICKET).getOrgPrice(var.getValue(), projectId, price).doubleValue());
                data.setTags(Arrays.stream(t.getTags().split(",")).filter(StrUtil::isNotEmpty).collect(Collectors.toList())); //中文标签

                return data;
            }).collect(Collectors.toList());
        } else {
            list = Lists.newArrayList();
        }
        AppProductGroupRes res = new AppProductGroupRes();
        res.setRecords(list);

        res.setTitlepicurl(ContentCacheTool.getShowPic(projectId, MenuContentReserveId.pcticketpic.name()));
        //res.setTitlepicurl(ProdFactory.getProd(ProdType.TICKET).getBannerPic(projectId));//业态顶图
        ProdTicketInfo ticketInfo = ProdFactory.getProd(ProdType.TICKET);
        res.setTabs(ticketInfo.getTabsGroup(projectId, StrUtil.EMPTY));


        return res;
    }

    @Override
    public AppProductGroupRes getKitList(AppKitListReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        KitGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP);
        List<Kitgroup> records = cache.getDataList(projectId);
        List<AppProductGroupRes.ProductGroupListData> list = null;
        if (records.size() > 0) {
            list = records.stream().filter(r -> {
                if (!r.getLsell() || !CalculateDate.isInRange(req.getQuerydate(), r.getStartsell(), r.getEndsell())) {
                    return false;
                }
                if (!req.getSearchkey().isEmpty() && !r.getDescription().contains(req.getSearchkey())) {
                    return false;
                }
                if (StringUtils.isNotBlank(req.getTheme()) && !r.getTheme().equals(req.getTheme())) { //套餐主题
                    return false;
                }
                return true;
            }).map(t -> {
                AppProductGroupRes.ProductGroupListData data = new AppProductGroupRes.ProductGroupListData();
                data.setGroupid(t.getCode());
                data.setTags(new ArrayList<>());
                data.setTitle(R.lang(t.getDescription()));
                data.setIntro(R.lang(t.getIntroduction()));
                data.setPicurl(CollectionUtil.getFirst(Arrays.asList(t.getMslidepics().split(","))));
                data.setPicurls(Arrays.asList(t.getMslidepics().split(",")));
                BigDecimal minPrice = ContentCacheTool.getChildProductCachedMinPrice(ProdType.TAOCAN, req.getQuerydate(), t.getCode(), projectId, new Var<>(""), new ArrayList<BigDecimal>());
                data.setPriceInfo(minPrice.doubleValue());
                return data;
            }).collect(Collectors.toList());
        } else {
            list = Lists.newArrayList();
        }
        AppProductGroupRes res = new AppProductGroupRes();
        res.setRecords(list);

        res.setTitlepicurl(ContentCacheTool.getShowPic(projectId, MenuContentReserveId.pckitpic.name()));
        //res.setTitlepicurl(ProdFactory.getProd(ProdType.TAOCAN).getBannerPic(projectId));//业态顶图
        return res;
    }

    @Override
    public AppMeetingRoomListRes getMeetingList(AppProductDetailReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        MeetingGroupCache meetingGroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP);
        Meetinggroup meetinggroup = meetingGroupCache.getRecord(projectId, req.getGroupid());
        if (meetinggroup == null) {
            log.error("错误的会场查询:{}", req.getGroupid());
        }
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysConf = sysConfCache.getOne(projectId);

        AppMeetingRoomListRes res = new AppMeetingRoomListRes();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());

        res.setPicurls(Arrays.asList(lmobile ? ContentCacheTool.getContentStrValue(meetinggroup.getMslidepics()).split(",") :
                ContentCacheTool.getContentStrValue(meetinggroup.getSlidepics()).split(",")));
        res.setAddress(meetinggroup.getAddress());
        res.setSingal(meetinggroup.getCoordinate());
        res.setTitle(R.lang(meetinggroup.getDescription()));
        res.setMeetingIntro(R.lang(meetinggroup.getIntroduction()));
        res.setRichtext(meetinggroup.getRichtext());
        res.setArea(meetinggroup.getSize().toString());
        res.setTotlroom(meetinggroup.getTotalroom());
        res.setPeople(meetinggroup.getPeople());
        res.setParking(meetinggroup.getParking());
        res.setTelno(meetinggroup.getTel());//添加电话号码
        res.setTypedesc(ContentCacheTool.getSupportDescString(projectId, meetinggroup.getType(), lmobile));//会场类型描述
        res.setTags(Arrays.stream(meetinggroup.getTags().split(",")).filter(StrUtil::isNotEmpty).collect(Collectors.toList())); //中文标签


        Var<String> var = new Var<String>();
        BigDecimal showPrice = ContentCacheTool.getChildProductCachedMinPrice(ProdType.MEETING,
                CalculateDate.NULLDATE, meetinggroup.getCode(), meetinggroup.getProjectid(), var, Lists.newArrayList());

        res.setShowprice(SysFuncLibTool.getShowPrice(showPrice));
        res.setProp(ContentCacheTool.getPackageCodeNodeLst(projectId, meetinggroup.getPackagecode()));//会场配套
        //推荐其他会场
        AppProductDetailReq getMeetingGroupReq = req;
        getMeetingGroupReq.setKeyword(ProdType.MEETING.val());
        res.setRecommend(getProductRecommendList(getMeetingGroupReq));

        MeetingCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING);
        List<Meeting> meetings = roomTypeCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(req.getGroupid()) && r.getLsell()).collect(Collectors.toList());
        List<AppMeetingRoomListRes.MeetingListData> ls = meetings.stream().map(r -> {
            AppMeetingRoomListRes.MeetingListData p = new AppMeetingRoomListRes.MeetingListData();
            p.setPicurl(CollectionUtil.getFirst(Arrays.asList(r.getMslidepics().split(","))));
            p.setPicurls(Arrays.asList(r.getMslidepics().split(",")));
            p.setCode(r.getCode());
            p.setPrice(SysFuncLibTool.getShowPrice(r.getPrice()));
            p.setPunit(r.getPunit());
            p.setDescription(R.lang(r.getDescription()));
            p.setStyledesc(ContentCacheTool.getMeetingStyleDesc(projectId, r.getStyle()));
            p.setProp(ContentCacheTool.getPackageCodeNodeLst(projectId, r.getPackagecode()));//会议室配套
            //p.setTags(Arrays.stream(r.getTags().split(",")).map(t -> CustomData.getDesc(projectId, t, SystemUtil.CustomDataKey.rmtags)).collect(Collectors.toList()));

            return p;
        }).collect(Collectors.toList());

        res.setRecords(ls);
        return res;
    }

    @Override
    public AppRoomProductListRes getRoomTypeList(AppProductDetailReq req) {
//        Stopwatch s=Stopwatch.createStarted();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        HotelCache hotelCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL);
        Hotel hotel = hotelCache.getRecord(projectId, req.getGroupid());
        if (hotel == null) {
//            log.error("错误的酒店查询:{}", req.getGroupid());
            hotel = new Hotel();
        }
        AppRoomProductListRes res = new AppRoomProductListRes();
        res.setPicurls(Arrays.asList((true ? ContentCacheTool.getContentStrValue(hotel.getMslidepics()) :
                ContentCacheTool.getContentStrValue(hotel.getSlidepics())).split(",")));
        res.setHoteldesc(R.lang(hotel.getDescription()));
        res.setRooms(hotel.getTotalroom());
        res.setSingals(hotel.getCoordinate());
        res.setTelno(hotel.getTel());
        res.setAddress(R.lang(hotel.getAddress()));


        res.setListpicurl(ProdFactory.getProd(ProdType.ROOM).getProductGroupShowPic(hotel.getCode(), projectId, true));
        res.setYear(CalculateDate.getDateProperty(CalculateDate.stringToDate(hotel.getOpeningtime()), Calendar.YEAR));
        res.setPoint(RandomUtil.randomDouble(4, 5, 1, RoundingMode.HALF_UP));
        res.setHotelIntro(R.lang(hotel.getIntroduction()));
        res.setGuidetext(hotel.getTipstext());
        res.setProp(ContentCacheTool.getPackageCodeNodeLst(projectId, hotel.getPackagecode()));


        Date start = req.getFromdate();
        Date end = req.getEnddate();
        if (start == null || end == null) {
            start = CalculateDate.getSystemDate();
            end = CalculateDate.reckonDay(start, 5, 7);
        } else if (CalculateDate.isAfter(end, start)) {
            end = CalculateDate.reckonDay(end, 5, -1);  //客房的查询范围要减1天
        }
        //推荐其他酒店
        AppProductDetailReq getHotelReq = req;
        getHotelReq.setKeyword(ProdType.ROOM.val());
        getHotelReq.setFromdate(start);
        getHotelReq.setEnddate(end);
        res.setRecommend(getProductRecommendList(getHotelReq));


        RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        List<Roomtype> roomtypes = roomTypeCache.getDataList(projectId).stream().filter(r -> r.getHotelcode().equals(req.getGroupid()) && r.getLsell()).collect(Collectors.toList());

//        CodeDetail codeDetail =
//                corePrice.ratesQuery(projectId, ProdType.ROOM, roomtypes.stream().map(Roomtype::getCode).collect(Collectors.toList()),
//                start);//TODO 从缓存中获取

        CodeDetail codeDetail = coreCache.ratesQueryCache(projectId, ProdType.ROOM, roomtypes.stream().map(Roomtype::getCode).collect(Collectors.toList()), start);

        SkuDetail skuDetail = coreCache.queryMinAvailNum(start, end, roomtypes.stream().map(Roomtype::getCode).collect(Collectors.toList()), projectId, -9999, ProdType.ROOM, req.getGroupid());

        List<AppRoomProductListRes.RoomProductDetailListData> ls = Lists.newArrayList();

        if (hotel.getLsell()) {
            for (Roomtype r : roomtypes) {
                AppRoomProductListRes.RoomProductDetailListData p = new AppRoomProductListRes.RoomProductDetailListData();
                p.setPicurl(CollectionUtil.getFirst(Arrays.asList(r.getMslidepics().split(","))));
                p.setLoverbook(skuDetail.getProductAvl(r.getCode()) <= 0);
                BigDecimal price = codeDetail.getProductPrice(r.getCode());
                if (price.doubleValue() <= 0) {
                    continue;
                }
                p.setPriceInfo(SysFuncLibTool.getShowPrice(price));
                p.setProductCode(r.getCode());
                p.setTitle(R.lang(r.getDescription()));
                p.setSellCount(RandomUtil.randomInt(800, 9000));
                p.setBedtype(r.getBedenum());
                p.setMaxp(r.getMaxp(0));
                p.setOrgpriceInfo(SysFuncLibTool.getShowPrice(r.getShowprice()));
                p.setProp(ContentCacheTool.getPackageCodeNodeLst(projectId, r.getPackagecode()));
                p.setTags(Arrays.stream(r.getTags().split(",")).map(t -> CustomData.getDesc(projectId, t, SystemUtil.CustomDataKey.rmtags)).collect(Collectors.toList()));
                ls.add(p);
            }
        }


//        排序(将售罄的房型放在最下方)
        ls.sort(Comparator.comparing(AppRoomProductListRes.RoomProductDetailListData::isLoverbook));

        res.setRecords(ls);
        return res;
    }

    /**
     * @param req
     * @return 返回推荐其他大组列表 默认返回4个
     */
    private <T> List<T> getProductRecommendList(AppProductDetailReq req) {
        List<T> recommendList = new ArrayList<>();
        String keyword = req.getKeyword();
        //if (!WebAppGlobalContext.getCurrentAppAgentType().equals(AgentType.PCH5)) {//PC端才需要推荐列表
        //    return recommendList;
        //}
        if (ProdType.ROOM.val().equals(keyword)) {//酒店房型
            AppHotelListReq hotelListReq = new AppHotelListReq();
            hotelListReq.setQuerydate(req.getFromdate());
            hotelListReq.setQueryEndDate(req.getEnddate());
            recommendList = (List<T>) getHotelList(hotelListReq).getRecords();
            if (StringUtils.isNotBlank(req.getGroupid())) {//排除当前酒店
                recommendList = recommendList.stream().filter(item ->
                        !req.getGroupid().equals(((AppHotelListRes.ProductGroupListData) item).getGroupid())
                ).collect(Collectors.toList());
            }

        } else if (ProdType.TICKET.val().equals(keyword)) {//票务组
            AppProductGroupReq appProductGroupReq = new AppProductGroupReq();
            appProductGroupReq.setQuerydate(req.getFromdate() == null ? new Date() : req.getFromdate());
            recommendList = (List<T>) getTicketGroupList(appProductGroupReq).getRecords();
            if (StringUtils.isNotBlank(req.getGroupid())) {//排除当前票务组
                recommendList = recommendList.stream().filter(item ->
                        !req.getGroupid().equals(((AppProductGroupRes.ProductGroupListData) item).getGroupid())
                ).collect(Collectors.toList());
            }

        } else if (ProdType.TAOCAN.val().equals(keyword)) {//套餐大类
            AppKitListReq appKitListReq = new AppKitListReq();
            appKitListReq.setQuerydate(req.getFromdate() == null ? new Date() : req.getFromdate());
            recommendList = (List<T>) getKitList(appKitListReq).getRecords();
            if (StringUtils.isNotBlank(req.getGroupid())) {//排除当前套餐组
                recommendList = recommendList.stream().filter(item ->
                        !req.getGroupid().equals(((AppProductGroupRes.ProductGroupListData) item).getGroupid())
                ).collect(Collectors.toList());
            }

        } else if (ProdType.WARES.val().equals(keyword)) {//商品
            AppProductGroupReq appProductGroupReq = new AppProductGroupReq();
            appProductGroupReq.setQuerydate(req.getFromdate() == null ? new Date() : req.getFromdate());
            recommendList = (List<T>) getSpuGroupList(appProductGroupReq).getRecords();
            if (StringUtils.isNotBlank(req.getGroupid())) {//排除当前商品组
                recommendList = recommendList.stream().filter(item ->
                        !req.getGroupid().equals(((AppSpuGroupRes.ProductGroupListData) item).getGroupid())
                ).collect(Collectors.toList());
            }

        } else if (ProdType.ITEMS.val().equals(keyword)) {//伴手礼商品
            AppGiftitemListReq appProductGroupReq = new AppGiftitemListReq();
            recommendList = (List<T>) getGiftitemList(appProductGroupReq).getRecords();
            if (StringUtils.isNotBlank(req.getGroupid())) {//排除当前伴手礼商品组
                recommendList = recommendList.stream().filter(item ->
                        !req.getGroupid().equals(((AppGiftitemListRes.AppGiftitemListData) item).getGiftitem()) //传过来的是伴手礼商品代码
                ).collect(Collectors.toList());
            }

        } else if (ProdType.MEETING.val().equals(keyword)) {//会场
            AppMeetingGroupListReq appProductGroupReq = new AppMeetingGroupListReq();
            appProductGroupReq.setQuerydate(req.getFromdate() == null ? new Date() : req.getFromdate());
            recommendList = (List<T>) getMeetingGroupList(appProductGroupReq).getRecords();
            if (StringUtils.isNotBlank(req.getGroupid())) {//排除当前会场组
                recommendList = recommendList.stream().filter(item ->
                        !req.getGroupid().equals(((AppProductGroupRes.ProductGroupListData) item).getGroupid())
                ).collect(Collectors.toList());
            }

        }
        if (recommendList.size() > 4) { //默认取头四个数据
            recommendList = recommendList.subList(0, 4);
        }
        return recommendList;
    }

    @Override
    public AppTicketProductListRes getTicketList(AppProductDetailReq req) {
        AppTicketProductListRes res = new AppTicketProductListRes();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        TicketgroupCache ticketgroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
        Ticketgroup ticketgroup = ticketgroupCache.getRecord(projectId, req.getGroupid());

        List<String> tags = new ArrayList<>();
        if (ticketgroup != null) {
            //if (StringUtils.isNotBlank(ticketgroup.getTags())) {
            //    tags = Arrays.stream(ticketgroup.getTags().split(",")).
            //            map(code -> CustomData.getDesc(projectId, code, SystemUtil.CustomDataKey.tickettags)).collect(Collectors.toList());
            //}
        } else {
//            log.error("错误的票务组查询：" + req.getGroupid());
            ticketgroup = new Ticketgroup();
        }
        if (!StringUtils.isAllBlank(ticketgroup.getOpeningtime(), ticketgroup.getClosetime())) {
            boolean lin = CalculateDate.isInTimeRange(new Date(), ticketgroup.getOpeningtime(), ticketgroup.getClosetime(), DateStyle.HH_MM.getValue());
            res.setOpstatus(lin ? "1" : "0");
        }
        res.setListpicurl(ProdFactory.getProd(ProdType.TICKET).getProductGroupShowPic(ticketgroup.getCode(), projectId, true));
        res.setPicurls(Arrays.asList(true ? ContentCacheTool.getContentStrValue(ticketgroup.getMslidepics()).split(",") :
                ContentCacheTool.getContentStrValue(ticketgroup.getSlidepics()).split(",")));
        //res.setTags(tags);
        res.setOpentime(ticketgroup.getOpeningtime() + "-" + ticketgroup.getClosetime());
        res.setSingals(ticketgroup.getCoordinate());
        res.setPoint(RandomUtil.randomDouble(4, 5, 1, RoundingMode.HALF_UP));//TODO 还没有统计
        res.setTelno(ticketgroup.getTel());
        res.setTitle(R.lang(ticketgroup.getDescription()));
        res.setGroupid(ticketgroup.getCode());
        res.setAddress(R.lang(ticketgroup.getAddress()));

        res.setInfotext(ticketgroup.getInfotext());
        res.setIntrotext(ticketgroup.getIntrotext());//原来是景点介绍.现在已经修改为预订须知
        res.setGuidetext(ticketgroup.getGuidetext());

        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        List<Menu_content> contents = menuContentCache.getGroupList(projectId, MenuReserveId.ticketnote.name());
        res.setOrdernote(contents.size() > 0 ? contents.get(0).getRichtext() : "");
        res.setAlertnote(ticketgroup.getAlertops() != 0 ? ticketgroup.getIntrotext() : "");//统一为introtext 对应为预订须知
        res.setAlertops(ticketgroup.getAlertops());//购买弹窗类型


        Date nowDate = DateUtil.beginOfDay(CalculateDate.getSystemDate());
        if (!CalculateDate.emptyDate(ticketgroup.getStartdate()) && !CalculateDate.emptyDate(ticketgroup.getEnddate())) {//如果指定了可选的日期段. 一般是那种预售票
            // 判断当前日期在约定范围
            res.setStartdate(CalculateDate.maxDate(nowDate, ticketgroup.getStartdate()));//让用户只能选这段时间
            res.setEnddate(CalculateDate.maxDate(nowDate, ticketgroup.getEnddate()));//让用户只能选这段时间
            if (!DateUtil.isIn(req.getFromdate(), DateUtil.beginOfDay(res.getStartdate()),
                    DateUtil.endOfDay(res.getEnddate()))) { //如果选择不在这段范围 默认可用日期
                req.setFromdate(res.getStartdate());
                req.setEnddate(res.getEnddate());
            }
        } else {
            res.setStartdate(null);
            res.setEnddate(null);
        }


        //推荐其他票务组
        AppProductDetailReq getTicketGroupReq = req;
        getTicketGroupReq.setKeyword(ProdType.TICKET.val());
        res.setRecommend(getProductRecommendList(getTicketGroupReq));

        TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        List<Ticket> tickets = ticketCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(req.getGroupid()) && r.getLsell()).collect(Collectors.toList());

        Date start = req.getFromdate();
        Date end = req.getEnddate();
        if (start == null || end == null) {
            if (start == null) {
                start = CalculateDate.getSystemDate();
            }
            if (end == null) {
                end = start;// CalculateDate.reckonDay(start, 5, 7);
            }
        }

        boolean ltoday = CalculateDate.isEqual(start, new Date());

//        CodeDetail codeDetail = corePrice.ratesQuery(projectId, ProdType.TICKET, tickets.stream().map(Ticket::getCode).collect(Collectors.toList()),
//                start);

        CodeDetail codeDetail = coreCache.ratesQueryCache(projectId, ProdType.TICKET, tickets.stream().map(Ticket::getCode).collect(Collectors.toList()), start);
        //票型默认取当天
        SkuDetail skuDetail = coreCache.queryMinAvailNum(start, start, tickets.stream().map(Ticket::getCode).collect(Collectors.toList()), projectId, -9999, ProdType.TICKET, ticketgroup.getCode());

        List<AppTicketProductListRes.TicketProductDetailListData> ls = Lists.newArrayList();

        for (Ticket t : tickets) {
            AppTicketProductListRes.TicketProductDetailListData p = new AppTicketProductListRes.TicketProductDetailListData();
            BigDecimal price = codeDetail.getProductPrice(t.getCode());
            if (price.doubleValue() < 0) {
                continue;
            }
            p.setPriceInfo(SysFuncLibTool.getShowPrice(price));
            p.setProductCode(t.getCode());
            //p.setTags(tags);
            p.setTitle(R.lang(t.getDescription()));

            Integer shownum = 0;
            if (t.getShowmode() == 1 || t.getShowmode() == 2) {
                shownum = t.getShowmode() == 1 ? t.getPinnum(0) : Math.max(skuDetail.getProductAvl(t.getCode()), 0);
            }

            p.setLsingleNotice(StrUtil.isNotBlank(t.getRichtext()));//预订须知是否单独弹窗
            p.setDispNum(shownum);
            p.setShowmode(t.getShowmode());
            p.setLneedIdcard(t.getLreal());
            p.setLoneToMany(t.getLo2m());//是否一张证件.对应多个身份证
            p.setOverreason(CustomData.getDesc(t.getProjectid(), t.getOverreason(), SystemUtil.CustomDataKey.closereason));//默认的是库存原因关闭购买
            p.setLoverbook(skuDetail.getProductAvl(t.getCode()) <= 0);
            p.setLshowtime(t.getLshowtime());
            if (ltoday && !p.isLoverbook()) {
                boolean lcanbuy = SysFuncLibTool.judgeRuleCanBook(start, -1, ProdType.TICKET.val(), t.getCode(), projectId, null).getLallow();//2023.9.27 购买数量改为-1.展示时不做数量限制
                if (!lcanbuy) {
                    p.setLoverbook(true);
                    p.setOverreason(CustomData.getDesc(t.getProjectid(), t.getReason(), SystemUtil.CustomDataKey.closereason));//再到规则原因,关闭购买
                }
            }

            p.setOrgpriceInfo(SysFuncLibTool.getShowPrice(t.getShowprice()));
            p.setShortinfo(t.getShortinfo());
            p.setExtranodesc(t.getNodesc());

            Rules rules = SysFuncLibTool.getProductRule(ProdType.TICKET.val(), t.getCode(), projectId);
            p.setLimitmax(rules == null ? 999 : rules.getLimitmax());

            ls.add(p);
        }

        ls.sort(Comparator.comparing(AppTicketProductListRes.TicketProductDetailListData::isLoverbook));//

        res.setRecords(ls);
        return res;
    }

    @Override
    public AppKitProductListRes getKitdetailList(AppProductDetailReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        AppKitProductListRes res = new AppKitProductListRes();
        KitGroupCache kitGroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP);
        Kitgroup kitGroup = kitGroupCache.getRecord(projectId, req.getGroupid());
        if (kitGroup == null) {
            log.error("错误的套餐大类查询:" + req.getGroupid());
            kitGroup = new Kitgroup();
        }
        res.setPicurls(ContentCacheTool.getProductSlidPics(ProdType.TAOCAN.val(), kitGroup.getCode(), projectId, true));
        res.setTitle(R.lang(kitGroup.getDescription()));
        res.setIntro(R.lang(kitGroup.getIntroduction()));
        res.setListpicurl(CollectionUtil.getFirst(Arrays.asList(kitGroup.getMslidepics().split(","))));
        //产品介绍
        res.setRichtext(kitGroup.getDetailtext());
        res.setVideourl(kitGroup.getVideourl());
        res.setVideotime(StrUtil.isNotBlank(kitGroup.getVideotime()) ? kitGroup.getVideotime() : TimeUnit.SECONDS.toMillis(10) + "");
        res.setStartdate(CalculateDate.maxDate(CalculateDate.getSystemDate(), kitGroup.getStartdate()));//让用户只能选这段时间
        res.setEnddate(CalculateDate.maxDate(CalculateDate.getSystemDate(), kitGroup.getEnddate()));//让用户只能选这段时间

        //预订须知kitGroup
        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        List<Menu_content> contents = menuContentCache.getGroupList(projectId, MenuReserveId.kitnote.name());
        String pubnotice = contents.size() > 0 ? contents.get(0).getRichtext() : "";
        //res.setNotictext(kitGroup.getNoticetext().isEmpty() ? pubnotice : kitGroup.getNoticetext());//TODO  这个预订须知应该是kitgroup 的

        ProductKitCache kitCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT);
        KititemCache kititemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
        TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);

        boolean lexpire = !CalculateDate.isInRange(CalculateDate.getSystemDate(), kitGroup.getStartsell(), kitGroup.getEndsell());//如果网页请求的预订日期还没到可订时间

        List<Productkit> kits = lexpire ? Lists.newArrayList() : kitCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(req.getGroupid())
                && CalculateDate.isInRange(req.getFromdate(), r.getStartdate(), r.getEnddate()) && r.getLsell()).collect(Collectors.toList());
        List<AppKitProductListRes.KitProductDetailListData> ls = Lists.newArrayList();

        Set<String> calcRoomtypes = Sets.newHashSet();
        List<String> calcKits = Lists.newArrayList();
        for (Productkit kit : kits) {
            List<Kititem> kititems = kititemCache.getGroupList(projectId, kit.getCode());
            for (Kititem kititem : kititems) {
                if (kititem.getProducttype().equals(ProdType.ROOM.val())) {
                    calcRoomtypes.add(kititem.getProductcode());
                }
            }

            calcKits.add(kit.getCode());
        }
        SkuDetail skuDetail = coreCache.queryMinAvailNum(req.getFromdate(), req.getFromdate(),
                new ArrayList<>(calcRoomtypes), projectId, -9999, ProdType.ROOM, "");

        SkuDetail kitsDetail = coreCache.queryMinAvailNum(req.getFromdate(), req.getFromdate(), calcKits, projectId, -999, ProdType.TAOCAN, "");

        for (Productkit kit : kits) {//循环所有小类
            List<Kititem> kititems = kititemCache.getGroupList(projectId, kit.getCode());
            AppKitProductListRes.KitProductDetailListData p = new AppKitProductListRes.KitProductDetailListData();
            p.setKitcode(kit.getCode());
            p.setTitle(StrUtil.isNotBlank(kit.getShowdesc()) ? R.lang(kit.getShowdesc()) : R.lang(kit.getDescription()));
            p.setStartdate(kit.getStartdate());
            p.setEnddate(kit.getEnddate());
            p.setAvlnum(kitsDetail.getProductAvl(kit.getCode()));//kitsDetail.getProductAvl(kit.getCode())   //TODO  读取套餐缓存
            for (Kititem kititem : kititems) {
                Integer avl = 9999;
                if (kititem.getProducttype().equals(ProdType.ROOM.val())) {
                    avl = skuDetail.getProductAvl(kititem.getProductcode());
                }
                KitProductItem item = p.addItem2Group(kititem, avl);
                if (kititem.getProducttype().equals(ProdType.TICKET.val())) {
                    Ticket t = ticketCache.getRecord(projectId, kititem.getProductcode());
                    if (t != null) {
                        item.setLneedIdcard(t.getLreal());
                    }
                }
            }
            ls.add(p);
        }
        res.setKits(ls);
        //推荐其他套餐组
        AppProductDetailReq getKitGroupReq = req;
        getKitGroupReq.setKeyword(ProdType.TAOCAN.val());
        res.setRecommend(getProductRecommendList(getKitGroupReq));
        return res;
    }

    /**
     * @param req
     * @return
     */
    @Override
    public List<AppProductAvailItem> getProductAvailDetail(AppProductQueryAvailReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();

        LinkedHashMap<String, Integer> avlMap = coreAvl.queryProductAvailGrid(projectId, req.getProductCode(), req.getStartdate(),
                req.getEnddate(), ProdType.getProdType(req.getType()), req.getSpecode());

        List<Produce_calcpricePo> ratelsit = null;
        try {
            ratelsit = corePrice.calcPrice_DB(projectId, ProdType.getProdType(req.getType()), req.getProductCode(), req.getStartdate(), req.getEnddate(),
                    1, 0, 0, "", "", "", req.getSpecode(), false);
        } catch (DefinedException e) {
            ratelsit = Lists.newArrayList();
        }
        LinkedHashMap<String, BigDecimal> rateMap = Maps.newLinkedHashMap();
        for (Produce_calcpricePo po : ratelsit) {
            rateMap.put(CalculateDate.dateToString(po.getDate()), po.getPrice());
        }
        List<AppProductAvailItem> list = Lists.newArrayList();
        //调用价格查询方法
        int len = 0;
        if (req.getStartdate() != null && req.getEnddate() != null) {
            len = CalculateDate.compareDates(req.getEnddate(), req.getStartdate()).intValue();
            len++;
        }
        for (int i = 0; i < len; i++) {
            AppProductAvailItem item = new AppProductAvailItem();
            Date rowd = CalculateDate.reckonDay(req.getStartdate(), 5, i);
            String sd = CalculateDate.dateToString(rowd);
            item.setDate(sd);
            item.setDatedesc(CalculateDate.getShortWeekDay(rowd) + "");
            item.setAvl(avlMap.getOrDefault(sd, 0));
            item.setPrice(rateMap.getOrDefault(sd, BigDecimal.ZERO).doubleValue());
            list.add(item);
        }
        return list;
    }

    @Override
    public Common_response getFreeCancelTime(AppProductQueryAvailReq req) {
        Common_response res = new Common_response();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String freetimeDesc = SysFuncLibTool.getCancelRuleDescription(req.getStartdate(), req.getType(), req.getProductCode(), projectId);
        res.setMsg(freetimeDesc);
        return res;
    }

    @Override
    public AppCalcKitDetailRes calcKitPrice(AppCalcKitDetailReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        AppCalcKitDetailRes res = new AppCalcKitDetailRes();
        KititemCache kititemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
        List<Kititem> kititems = kititemCache.getGroupList(projectId, req.getPkgCode());
        Set<Long> selects = req.getSelects().stream().map(r -> r.getId()).collect(Collectors.toSet());
        List<Kititem> calcitems = kititems.stream().
                filter(r -> selects.contains(r.getSqlid()) || r.getItemtype().isEmpty()).collect(Collectors.toList());  //选中的产品跟固定项目.加入到item列表中

    /*    Set<String> calcRoomtypes = Sets.newHashSet();
        for (Kititem kititem : kititems) {
            if (kititem.getProducttype().equals(ProdType.ROOM.val())) {
                calcRoomtypes.add(kititem.getProductcode());
            }
        }
        SkuDetail skuDetail = coreCache.queryMinAvailNum(req.getCalcdate(), req.getCalcdate(),
                calcRoomtypes.stream().collect(Collectors.toList()), projectId, -9999, ProdType.ROOM);*/
        //计算价格
        BigDecimal totalPrice = BigDecimal.ZERO;
//        for (int i = 0; i < calcitems.size(); i++) {
//            Kititem item = calcitems.get(i);
//            KitProductItem row = new KitProductItem();
//            row.setPrice(BigDecimal.valueOf((RandomUtil.randomInt(10, 500) / 10) * 10).doubleValue());
//            row.setId(item.getSqlid());
//            totalPrice = totalPrice.add(BigDecimal.valueOf(row.getPrice()));
//            res.getItem().add(row);
//        }

        Var<BigDecimal> totalPriceVar = new Var<>();
        List<KitProductItem> items = corePrice.calcKitItemPrIce(projectId, req.getPkgCode(), calcitems, req.getCalcdate(),
                req.getPkgNum(), totalPriceVar);
        res.setItem(items);

        res.getItem().sort(Comparator.comparing(KitProductItem::getPrice).reversed());//价格降序排列

        //校验库存
        res.setLcanBuy(true);//现在点开的时候已经校验了
        res.setTotalAmount(totalPriceVar.getValue());
        return res;
    }

    @Override
    public AppOrderNoticeTextRes getProductNoticeText(AppProductQueryReq req) {
        AppOrderNoticeTextRes res = new AppOrderNoticeTextRes();

        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        BaseProdInfo info = ProdFactory.getProd(ProdType.getProdType(req.getType()));
        String noticeText = info.
                getNoticeText(req.getProductCode(), req.getGroupid(), projectId);
        res.setNoticetext(noticeText);
        res.setWxAppMsgTemplateId(info.getNotifyMsgTemplateId("", projectId));

        return res;
    }

    @Override
    public AppExtraServiceRes getExtraServices(AppProductDetailReq req) {
        AppExtraServiceRes res = new AppExtraServiceRes();
        List<AppExtraServiceRes.ExtraData> dataList = new ArrayList<>();
        for (int i = 1; i < 4; i++) {
            AppExtraServiceRes.ExtraData data = new AppExtraServiceRes.ExtraData();
            data.setServiceCode(StringUtils.leftPad(i + "", 3, "0"));
            data.setServiceDesc("测试选项" + i);
            data.setPrice(Double.valueOf(RandomUtil.randomInt(1, 99) + ""));
            dataList.add(data);
        }
        res.setServices(dataList);
        return res;
    }

    @Override
    public AppProductIntroRes getRoomProductIntro(AppProductIntroReq req) {
        AppProductIntroRes res = new AppProductIntroRes();
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        List<String> slidePics = ContentCacheTool.getProductSlidPics(ProdType.ROOM.val(), req.getProductCode(), projectid, true);
        res.setPicurls(slidePics);

        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        List<Menu_content> contents = menuContentCache.getGroupList(projectid, MenuReserveId.roomnote.name());
        res.setRichtext(contents.size() > 0 ? contents.get(0).getRichtext() : "");

        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        Roomtype roomtype = cache.getRecord(projectid, req.getProductCode());
        if (roomtype != null) {
            res.setProp(ContentCacheTool.getPackageCodeNodeLst(projectid, roomtype.getPackagecode()));
        }
        res.setCheckinNotice(ContentCacheTool.getShowNotice(projectid, MenuReserveId.checkinnotice.name(), false));
        res.setRoomNotice(ContentCacheTool.getShowNotice(projectid, MenuReserveId.roomnote.name(), true));

        return res;
    }

    @Override
    public AppHotelIntroRes getHotelIntro(AppProductIntroReq req) {
        AppHotelIntroRes res = new AppHotelIntroRes();
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();

        HotelCache hotelCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL);

        Hotel hotel = hotelCache.
                getRecord(projectid, req.getProductCode());
        if (hotel != null) {
            List<String> slidePics = Arrays.asList((true ? ContentCacheTool.getContentStrValue(hotel.getMslidepics()) :
                    ContentCacheTool.getContentStrValue(hotel.getSlidepics())).split(","));
            res.setPicurls(slidePics);
            res.setProductIntro(R.lang(hotel.getIntroduction()));
            res.setRooms(hotel.getTotalroom());
            res.setYear(CalculateDate.getDateProperty(CalculateDate.stringToDate(hotel.getOpeningtime()), Calendar.YEAR));
            res.setTelno(hotel.getTel());
            res.setProp(ContentCacheTool.getPackageCodeNodeLst(projectid, hotel.getPackagecode()));


            res.setGroupid(hotel.getCode());//酒店代码
            res.setThemecode(hotel.getGroupid());//主题
            //String tags = ContentCacheTool.getPackageDescString(hotel.getProjectid(), hotel.getTags(), lmobile);
            //res.setTags(Arrays.asList(tags.split("/"))); //中文标签
            res.setTags(Arrays.stream(hotel.getTags().split(",")).filter(StrUtil::isNotEmpty).collect(Collectors.toList())); //中文标签

            res.setTitle(R.lang(hotel.getDescription()));
            res.setPicurl(true ? hotel.getMlistpic() : hotel.getListpic());
            res.setSubtitle(R.lang(hotel.getIntroduction()));
            res.setDeviceInfo(ContentCacheTool.getSupportDescString(projectid, hotel.getPackagecode(), true));
            res.setAddress(hotel.getAddress());

            Var<String> var = new Var<>("");
            List<BigDecimal> prices = Lists.newArrayList();
            BigDecimal price = ContentCacheTool.getChildProductCachedMinPrice(ProdType.ROOM, new Date(), hotel.getCode(), hotel.getProjectid(), var, prices);//TODO 查找最低起步价
            res.setPriceInfo(price.doubleValue());  //查找子房型最低价

            RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
            Roomtype minRoomtype = null;
            try {
                minRoomtype = roomTypeCache.getRecord(hotel.getProjectid(), var.getValue());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (minRoomtype != null) {
                res.setOrgpriceInfo(minRoomtype.getShowprice().doubleValue());
            } else {
                res.setOrgpriceInfo(price.doubleValue());
            }
            List<Roomtype> myrms = roomTypeCache.getRoomtypeList(projectid, hotel.getCode());
            StringBuilder rms = new StringBuilder();
            for (Roomtype myrm : myrms) {
                rms.append((rms.length() == 0) ? R.lang(myrm.getDescription()) : "," + R.lang(myrm.getDescription()));
            }
            res.setRoomtypes(rms.toString());

            List<Hotel> hotels = hotelCache.getDataList(projectid).stream().filter(r -> r.getGroupid().equals(hotel.getGroupid())).collect(Collectors.toList());
            List<Hotel> hotelList = RandomUtil.randomEleList(hotels, 10);//随机一批
            for (Hotel ot : hotelList) {
                AppHotelIntroRes.HotelLinkData linkData = new AppHotelIntroRes.HotelLinkData();
                linkData.setTitle(R.lang(ot.getDescription()));
                linkData.setGroupid(ot.getCode());
                linkData.setPicurl(CollectionUtil.getFirst(Arrays.asList(ot.getMslidepics().split(","))));
                if (!ot.getCode().equals(hotel.getCode()) && ot.getLsell() && res.getOthers().size() < 3) {//最多加载3个推荐
                    res.getOthers().add(linkData);
                }
            }
        }


        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        List<Menu_content> contents = menuContentCache.getGroupList(projectid, MenuReserveId.roomnote.name());
        res.setRichtext(contents.size() > 0 ? contents.get(0).getRichtext() : "");


        return res;
    }

    @Override
    public List<PageHeader> getPageHeaderInfo() {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        //获取内容对应设置
        List<PageHeader> list = ContentCacheTool.getPageHeaderInfo(projectid);
        return list;
    }

    @Override
    public PageFoot getPageFootInfo() {
        PageFoot res = new PageFoot();
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        WebconfCache webConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.WEBCONF);
        Webconf webconf = webConfCache.getOne(projectid);//获取配置表信息
        if (webconf != null) {
            res = ContentCacheTool.transWebConfToFootInfo(webconf);
        }
        return res;
    }

    @Override
    public AppSpuGroupRes getSpuGroupList(AppProductGroupReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        SpugroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        List<Spugroup> records = cache.getDataList(projectId);
        List<AppSpuGroupRes.ProductGroupListData> list = null;
        AppSpuGroupRes res = new AppSpuGroupRes();
        res.setCname("景区套餐");
        String groupid = req.getGroupid(); //这个跟qrgroup 是二选一的关系
        if (records.size() > 0) {
            if (!StrUtil.isBlank(req.getQrgroup()) && !req.getQrgroup().equals("ALL")) {//如果指定二维码大组
                SpuqrCache qrCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUQR);
                Spuqr spuqr = qrCache.getRecord(projectId, req.getQrgroup());
                res.setCname(spuqr.getDescription());
                if (spuqr != null && !spuqr.getGroups().isEmpty()) {//如果指定了关联的大组
                    req.setGroupid(spuqr.getGroupid());
                    Set<String> s = Sets.newHashSet(spuqr.getGroups().split(","));
                    records = records.stream().filter(r -> s.contains(r.getCode())).collect(Collectors.toList());

                }
            }
            records = records.stream().filter(r -> r.getGroupid().equals(req.getGroupid())).collect(Collectors.toList());  //先根据大组筛选一轮

            Date now = CalculateDate.getSystemDate();
            list = records.stream().filter(Spugroup::getLsell).filter(r -> {
                if (!CalculateDate.emptyDate(r.getStartsell()) && !CalculateDate.emptyDate(r.getEndsell())) {
                    if (!CalculateDate.isInRange(now, r.getStartsell(), r.getEndsell())) {
                        return false;
                    }
                }

                if (req.getSearchkey().isEmpty()) {
                    return true;
                } else {
                    return r.getDescription().contains(req.getSearchkey());
                }
            }).map(t -> {
                AppSpuGroupRes.ProductGroupListData data = new AppSpuGroupRes.ProductGroupListData();
                data.setGroupid(t.getCode());
                data.setTags(new ArrayList<>());
                data.setTitle(t.getDescription());
                data.setPicurl(CollectionUtil.getFirst(Arrays.asList(t.getMslidepics().split(","))));
                data.setPicurls(true ? Arrays.asList(t.getMslidepics().split(",")) : Arrays.asList(t.getSlidepics().split(",")));
                data.setSubtitle(t.getRichtext());
                Var<String> var = new Var<String>();
                BigDecimal price = ContentCacheTool.getChildProductCachedMinPrice(ProdType.WARES,
                        req.getQuerydate(), t.getCode(), projectId, var, Lists.newArrayList());//TODO 取缓存价格.或者直接从产品身上取
                data.setPriceInfo(price.doubleValue());
                data.setOrgpriceInfo(Double.valueOf(BigDecimal.ZERO + ""));
                data.setType(t.getType());
                return data;
            }).collect(Collectors.toList());
        } else {
            list = Lists.newArrayList();
        }

        res.setRecords(list);
        res.setCname(ProdFactory.getProd(ProdType.WARES).getProdGroupDesc(req.getSearchkey(), projectId));

        if (StrUtil.isNotBlank(req.getContentid())) {
            res.setTitlepicurl(ContentCacheTool.getContentPicUrl(req.getContentid(), projectId));
        }

        ProdWareInfo wareInfo = ProdFactory.getProd(ProdType.WARES);
        res.setTabs(wareInfo.getTabsGroup(projectId, groupid));
        //res.setTitlepicurl(ProdFactory.getProd(ProdType.WARES).getBannerPic(projectId));//业态顶图
        return res;
    }

    @Override
    public AppSpuProductListRes getSpuList(AppProductDetailReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        SpugroupCache spugroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        Spugroup spugroup = spugroupCache.getRecord(projectId, req.getGroupid());
        if (spugroup == null) {
            log.error("错误的景区商品大组查询:" + req.getGroupid());
            return new AppSpuProductListRes();
        }
        AppSpuProductListRes res = new AppSpuProductListRes();

        res.setPicurls(Arrays.asList(true ? ContentCacheTool.getContentStrValue(spugroup.getMslidepics()).split(",") :
                ContentCacheTool.getContentStrValue(spugroup.getSlidepics()).split(",")));
        res.setTitle(R.lang(spugroup.getDescription()));
        res.setType(spugroup.getType());
        res.setInfotext(spugroup.getRichtext());//文字简介
        res.setIntro(spugroup.getDetailtext());//图文简介
        res.setAlertnote(spugroup.getNoticetext());//预订须知
        res.setCoordinate(spugroup.getCoordinate());
        res.setAddress(spugroup.getAddress());
        res.setOpeningtime(spugroup.getOpeningtime());
        res.setTel(spugroup.getTel());
        res.setAlertops(spugroup.getAlertops());


        //推荐其他商品
        AppProductDetailReq getSpuGroupReq = req;
        getSpuGroupReq.setKeyword(ProdType.WARES.val());
        res.setRecommend(getProductRecommendList(getSpuGroupReq));

        SpusitemCache spusitemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        List<Spusitem> spusitems = spusitemCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(req.getGroupid()) && r.getLsell()).collect(Collectors.toList());

        Date start = req.getFromdate();
        Date end = req.getEnddate();
        if (start == null || end == null) {
            start = CalculateDate.getSystemDate();
            end = CalculateDate.reckonDay(start, 5, 7);
        }

//        boolean ltoday = CalculateDate.isEqual(start, new Date());
        List<Spusitem> avlprods = spusitems.stream().filter(r -> r.getLavl()).collect(Collectors.toList());

        CodeDetail codeDetail = new CodeDetail();
        SkuDetail skuDetail = new SkuDetail();

        boolean laudio = spugroup.getType().equals(SpusType.AUDIO.getVal());


        if (avlprods.size() > 0) {
            //codeDetail = coreCache.ratesQueryCache(projectId, ProdType.WARES, avlprods.stream().map(Spusitem::getCode).collect(Collectors.toList()), start);
            skuDetail = coreCache.queryMinAvailNum(start, end, avlprods.stream().map(Spusitem::getCode).collect(Collectors.toList()), projectId, -9999, ProdType.WARES, spugroup.getCode());
        }


        List<AppSpuProductListRes.SpuProductDetailListData> ls = Lists.newArrayList();

        Date now = CalculateDate.getSystemDate();
        for (Spusitem r : spusitems) {
            AppSpuProductListRes.SpuProductDetailListData p = new AppSpuProductListRes.SpuProductDetailListData();


            BigDecimal price = r.getShowprice(); // r.getLavl() ? codeDetail.getProductPrice(r.getCode()) : r.getShowprice();
            if (price.doubleValue() < 0) {
                continue;
            }
            p.setPriceInfo(SysFuncLibTool.getShowPrice(price));
            p.setPicurl(CollectionUtil.getFirst(Arrays.asList(r.getMlistpic().split(","))));
            p.setProductCode(r.getCode());
            p.setTitle(R.lang(r.getDescription()));
            p.setLoverbook(skuDetail.getProductAvl(r.getCode()) <= 0);//r.getLavl() ? skuDetail.getProductAvl(r.getCode()) <= 0 :  TODO  取缓存库存
            p.setAudiourl(laudio && CalculateNumber.isZero(r.getShowprice()) ? r.getAudiourl() : StrUtil.EMPTY);
            p.setExpireDay(r.getExpireday() == 0 ? 1 : r.getExpireday());
            p.setLsingleNotice(StrUtil.isNotBlank(r.getRichtext()));//判断是否预订须知单独弹窗
            if (r.getLpack()) {
                p.setExpireDay(-1);
                if (CalculateDate.isAfter(CalculateDate.getSystemDate(), r.getEndsell())) {
                    continue; //过期的产品不做展示
                }
            }

            if (!CalculateDate.emptyDate(r.getStartsell()) && !CalculateDate.emptyDate(r.getEndsell())) {
                if (!CalculateDate.isInRange(now, r.getStartsell(), r.getEndsell())) {
                    p.setLoverbook(true);//没到开卖时间就先做处理.不可购买
                }
            }

            p.setIntroduce(r.getIntroduce());
            p.setUsetips(r.getUsetips());

            ls.add(p);
        }

        res.setRecords(ls);
        return res;
    }

    /**
     * @param req
     * @return 获取伴手礼商品列表
     */
    @Override
    public AppGiftitemListRes getGiftitemList(AppGiftitemListReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        GiftItemCache giftItemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        List<Giftitem> records = giftItemCache.getDataList(projectId);
        List<AppGiftitemListRes.AppGiftitemListData> list = null;
        boolean lmobile = WebAppGlobalContext.isMobileRequest();
        if (records.size() > 0) {
            list = records.stream().filter(Giftitem::getLsell).filter(r -> {
                if (StringUtils.isNotBlank(req.getType()) && !r.getGroupid().equals(req.getType())) {//按照商品类型过滤
                    return false;
                }
                if (!lmobile) {//PC端是搜索名称
                    if (StringUtils.isBlank(req.getSearchkey())) {
                        return true;
                    } else {
                        return r.getDescription().contains(req.getSearchkey());
                    }
                } else { //移动端搜索名称/标签
                    if (StringUtils.isBlank(req.getSearchkey())) {
                        return true;
                    } else {
                        //过滤伴手礼商品名称和标签
                        if (req.getSearchkey().equals("包邮")) {
                            return r.getPosttype() == 0;
                        }
                        Gift gift = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).getRecord(projectId, r.getGroupid());
                        return r.getDescription().contains(req.getSearchkey()) || gift.getDescription().contains(req.getSearchkey());
                    }
                }

            }).map(t -> {
                AppGiftitemListRes.AppGiftitemListData data = new AppGiftitemListRes.AppGiftitemListData();
                data.setGiftitem(t.getCode());
                data.setTitle(R.lang(t.getDescription()));
                data.setPicurl(ContentCacheTool.getProductOrderShowPic(ProdType.ITEMS.val(), t.getCode(), t.getProjectid(), false));//主商品图片可能是多个 取第一张
                data.setSubtitle(t.getIntroduction());
                Var<String> var = new Var<String>();
                BigDecimal price = ContentCacheTool.getChildProductCachedMinPrice(ProdType.ITEMS,
                        new Date(), t.getCode(), projectId, var, Lists.newArrayList());
                data.setPriceInfo(price.doubleValue());
                data.setOrgpriceInfo(Double.valueOf(price + ""));
                data.setPinnum(t.getPinnum(0));
                data.setGroupid(t.getGroupid());//类目模板
                data.setTags(ContentCacheTool.getGiftItemTags(projectId, t));
                return data;
            }).collect(Collectors.toList());
        } else {
            list = Lists.newArrayList();
        }
        AppGiftitemListRes res = new AppGiftitemListRes();
        res.setRecords(list);
        //获取banner图
        res.setTitlepicurl(ProdFactory.getProd(ProdType.ITEMS).getBannerPic(projectId));
        List<SelectDataNode> selectDataNodes = customDataFactory.getSelectData(projectId, SystemUtil.CustomDataKey.gift);
        res.setTypeinfos(selectDataNodes);

        if (StrUtil.isNotBlank(req.getContentid())) {
            res.setTitlepicurl(ContentCacheTool.getContentPicUrl(req.getContentid(), projectId));
        }
        return res;
    }

    /**
     * @param req
     * @return 获取伴手礼商品预定详情
     */
    @Override
    public AppGiftitemProductRes getGiftitem(AppProductDetailReq req) {
        AppGiftitemProductRes res = new AppGiftitemProductRes();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        GiftItemCache giftItemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        Giftitem giftitem = giftItemCache.getRecord(projectId, req.getGroupid());
        if (giftitem != null) {
            res.setGiftitem(giftitem.getCode());
            res.setTags(ContentCacheTool.getGiftItemTags(projectId, giftitem));//标签
            res.setTitle(R.lang(giftitem.getDescription()));
            res.setRichtext(giftitem.getRichtext());//富文本详情
            res.setInfotext(giftitem.getIntroduction());//简介
            res.setSpes(ContentCacheTool.getgiftItemSpesList(projectId, giftitem));//规格集合
            res.setSpespic(ContentCacheTool.getgiftItemSpesPicList(projectId, giftitem));//规格图片集合
            res.setDetailList(ContentCacheTool.getGiftItemProductDetailList(projectId, giftitem));//产品参数详情集合
            res.setDetail(ContentCacheTool.getGiftItemProductDetailString(projectId, giftitem));//产品参数名集合
            List<String> picUrls = Arrays.asList(giftitem.getMlistpic().split(","));
            res.setPicurls(picUrls);
            res.setListpicurl(picUrls.get(0));
            res.setAddress(giftitem.getArea());//后台发货
            res.setType(giftitem.getPosttype());
            if (giftitem.getPosttype() == 1) {
                res.setPostageDetail(ContentCacheTool.getPostageDetail(projectId, null));
            }

        }
        //推荐其他商品
        AppProductDetailReq getGiftitemListReq = req;
        getGiftitemListReq.setKeyword(ProdType.ITEMS.val());
        res.setRecommend(getProductRecommendList(getGiftitemListReq));
        return res;
    }

    @Override
    public AppGiftitemSkuAvlPrice getGiftitemAvailDetail(AppActsiteQueryAvailReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        AppGiftitemSkuAvlPrice res = new AppGiftitemSkuAvlPrice();
        String productCode = req.getProductcode();
        String itemcode = productCode;
        if (req.getProductcode().contains(":")) { //传code:specs1:specs2查询单个产品库存价格
            productCode = req.getProductcode().substring(0, req.getProductcode().indexOf(":"));
            itemcode = productCode;
        }
        Giftitem giftitem = ContentCacheTool.getGiftItemByProductStr(productCode, projectId);
        if (giftitem != null) {
            res.setName(R.lang(giftitem.getDescription()));//伴手礼商品名称
        }
        res.setSkuInfo(ContentCacheTool.getGiftitemSkuInfo(projectId, productCode));//规格信息集合

        List<String> querySpecs = ContentCacheTool.getGiftAllSpec(itemcode, projectId);

        //快速查询价格缓存
        CodeDetail detail = coreCache.rateQuerySpecCache(projectId, ProdType.ITEMS, itemcode, querySpecs);
        SkuDetail skuDetail = coreCache.queryMinAvailNum(null, null, querySpecs, projectId, -9999, ProdType.ITEMS, itemcode);

        //log.info("来一场伴手礼，查询价格结果：{}", JSON.toJSONString(detail));

        List<AppProdSkuAvlAndPrice> prices = Lists.newArrayList();
        for (String spec : querySpecs) {
            AppProdSkuAvlAndPrice skuinfo = new AppProdSkuAvlAndPrice();
            skuinfo.setSku(spec);
            skuinfo.setPrice(detail.getProductPrice(spec));
            skuinfo.setAvl(skuDetail.getProductAvl(spec));
            skuinfo.setDifference(ContentCacheTool.getGiftDescFast(spec, "|", false));
            prices.add(skuinfo);
        }
        //log.info("伴手礼查询API结果：{}", JSON.toJSONString(prices));
        res.setPriceInfo(prices);//商品库存价格集合

     /*   List<AppProdSkuAvlAndPrice> prices = giftitemMapper.getProdSkuAvlPrice(req.getProductcode() + "%", projectId);
        if (CollectionUtil.isNotEmpty(prices)) {
            for (AppProdSkuAvlAndPrice item : prices) {
                List<String> diffs = Arrays.asList(item.getDifference().split(","));
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < diffs.size(); i++) {
                    if (i > 0) {
                        sb.append(",");
                        sb.append(ContentCacheTool.giftItemSpecsCodeTransToDesc(productCode, null, diffs.get(i), projectId));
                    } else {
                        sb.append(ContentCacheTool.giftItemSpecsCodeTransToDesc(productCode, diffs.get(i), null, projectId));
                    }
                }
                item.setDifference(sb.toString());//代码转换
            }
            res.setPriceInfo(prices);//商品库存价格集合
        }*/
        return res;
    }

    /**
     * @return 获取修改版页脚信息
     */
    @Override
    public PageFoot getNewPageFootInfo(String projectId) {
        PageFoot pageFoot = new PageFoot();
        Sysconf dbconf = sysconfMapper.findFirstByProjectid(projectId);
        if (StringUtils.isNotBlank(dbconf.getPcfoot())) {
            pageFoot = JSON.parseObject(dbconf.getPcfoot(), new TypeReference<PageFoot>() {
            });
            //添加版权翻译
            if (StringUtils.isNotBlank(pageFoot.getCopyright())) {
                pageFoot.setCopyright(R.lang(pageFoot.getCopyright()));
            }
            //友情链接翻译
            if (CollectionUtil.isNotEmpty(pageFoot.getFriendlinks())) {
                for (LinkData node : pageFoot.getFriendlinks()) {
                    node.setTile(R.lang(node.getTile()));
                }

            }
            //图片集合翻译
            if (CollectionUtil.isNotEmpty(pageFoot.getImagelinks())) {
                for (LinkData node : pageFoot.getImagelinks()) {
                    node.setTile(R.lang(node.getTile()));
                }

            }
            //文本链接翻译
            if (CollectionUtil.isNotEmpty(pageFoot.getTextlinks())) {
                for (LinkData node : pageFoot.getTextlinks()) {
                    node.setTile(R.lang(node.getTile()));
                }

            }

        }
        if (StringUtils.isNotBlank(dbconf.getBizbanners())) {
            List<BusinessBanner> businessBanners = JSON.parseObject(dbconf.getBizbanners(), new TypeReference<List<BusinessBanner>>() {
            });
            businessBanners = businessBanners.stream().filter(BusinessBanner::isShow).collect(Collectors.toList());
            //翻译业态中文
            for (BusinessBanner node : businessBanners) {
                node.setBusiness(R.lang(node.getBusiness()));
            }
            BizBanners bizBanners = new BizBanners();
            bizBanners.setBanners(businessBanners);
            pageFoot.setBizBanners(bizBanners);
        }
        return pageFoot;
    }

    /**
     * @param projectId
     * @return 获取新版页眉信息
     */
    @Override
    public List<PageHeader> getNewPageHeaderInfo(String projectId) {
        List<PageHeader> list = new ArrayList<>();
        SysConfCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf dbconf = cache.getOne(projectId);
        if (StringUtils.isNotBlank(dbconf.getPcheader())) {
            list = JSON.parseObject(dbconf.getPcheader(), new TypeReference<List<PageHeader>>() {
            });
        }
        new PageHeader().transTitleLanguage(list);
        //todo 检查是否包含首页单项数据，没有则添加
        return list;
    }

    /**
     * @param req
     * @return 获取指定省份代码邮费
     */
    @Override
    public AppPostageDetail getPostage(AppProvinceReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        AppPostageDetail res = ContentCacheTool.getPostageDetail(projectId, req.getSearchkey());
        return res;
    }

    @Override
    public AppPriceDetail calcPrice(AppCalcPriceReq req) throws DefinedException {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        AppPriceDetail priceDetail = new AppPriceDetail();
        //暂时只处理普通产品价格计算
        OrderReqTransFactory transFactory = SpringUtil.getBean(OrderReqTransFactory.class);
        StdOrderData orderData = transFactory.parseOrderForm(req);
        transFactory.writeProjectId(projectId, orderData);
        transFactory.updateCombieInfo(orderData, req.getAddressInfo());

        coreSync3.updPriceAndFill(orderData);  //TODO 改成一个快速通过缓存计算的方法

        priceDetail.setTotalAmount(orderData.getTotalAmount());
        priceDetail.setExtrafee(orderData.getExtrafee());
        priceDetail.setOrgAmount(orderData.getTotalAmount().subtract(orderData.getExtrafee()).add(orderData.getDiscAmount()));

        return priceDetail;
    }

    /**
     * 获取路线列表
     *
     * @param req
     * @return
     */
    @Override
    public AppTravelTipListRes getTravelTipList(AppTravelTipListReq req) {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        TraveltipCache traveltipCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TRAVELTIP);
        AppTravelTipListRes res = new AppTravelTipListRes();
        List<Traveltip> records = traveltipCache.getDataList(projectid);
        List<AppTravelTipListRes.ProductGroupListData> list;
        String searchKey = req.getSearchkey();
        if (records.size() > 0) {
            list = records.stream().filter(Traveltip::getLshow).filter(
                    tvl -> {
                        if (StringUtils.isNotBlank(searchKey)) {
                            if (!tvl.getDescription().contains(searchKey)) {//搜索名称条件不符过滤
                                return false;
                            }
                        }
                        return true;
                    }
            ).map(r -> {
                AppTravelTipListRes.ProductGroupListData data = new AppTravelTipListRes.ProductGroupListData();
                data.setTravel(r.getCode());
                data.setDescription(r.getDescription());
                data.setIntroduction(r.getIntroduction());
                data.setSlidepics(Arrays.asList(r.getSlidepics().split(",")));
                data.setTags(Arrays.stream(r.getTags().split(",")).filter(StrUtil::isNotEmpty).map(
                        node -> CustomData.getDesc(projectid, node, SystemUtil.CustomDataKey.tvltags)
                ).collect(Collectors.toList())); //中文标签转换
                return data;
            }).collect(Collectors.toList());
        } else {
            list = Lists.newArrayList();
        }
        res.setRecords(list);


        res.setTitlepicurl(ContentCacheTool.getShowPic(projectid, MenuContentReserveId.pcmeetinggrouppic.name()));
        return res;
    }

    /**
     * 获取路线详情
     *
     * @param code 路线代码
     * @return
     */
    @Override
    public AppTravelTipDetRes getTravelTipDet(String code) {
        AppTravelTipDetRes result = new AppTravelTipDetRes();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        TraveltipCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TRAVELTIP);
        Traveltip traveltip = cache.getRecord(projectId, code);
        if (traveltip != null) {
            result.setTitle(R.lang(traveltip.getDescription()));
            result.setIntroduction(R.lang(traveltip.getIntroduction()));
            result.setSlidepics(Arrays.asList(traveltip.getSlidepics().split(",")));//轮播图
            result.setRouteType(traveltip.getTags());// 原来的标签没有什么用.现在用来控制移动端的路线图绘制方式
            if (WebAppGlobalContext.getCurrentAppAgentType().equals(AgentType.PCH5)) {
                result.setQrcode(SpringUtil.getBean(ManagerToolServiceImpl.class).
                        generateQrList(projectId, ProductQrcodePathFactory.TRAVELTIP, AgentType.WXAPP.name(), code, ProductQrcodePathFactory.QRSIZE_ONLINE).getBase64());
            }

            //获取日程管理
            if (StringUtils.isNotBlank(traveltip.getTipinfo())) {
                List<TipsNode> tipsNodes = JSONArray.parseArray(traveltip.getTipinfo(), TipsNode.class);
                //防止更新点位坐标，攻略内保存的点位坐标不是最新
                for (TipsNode node : tipsNodes) {
                    if (CollectionUtil.isNotEmpty(node.getDets())) {

                        ParksiteCache parksiteCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARKSITE);
                        for (TipsNodeDet nodeDet : node.getDets()) {
                            List<String> picList = nodeDet.getPicurls();
                            List<String> list = new ArrayList<>();
                            //检查保存的字段
                            if (CollectionUtil.isNotEmpty(picList)) {
                                for (String picUrl : picList) {
                                    list.addAll(Arrays.asList(picUrl.split(",")));
                                }
                                nodeDet.setPicurls(list);
                            }
                            if (StringUtils.isNotBlank(nodeDet.getSitecode())) {//点位坐标信息更新
                                Parksite parksite = parksiteCache.getRecord(projectId, nodeDet.getSitecode());
                                if (parksite != null) {
                                    nodeDet.setSingals(parksite.getCoordinate());
                                    nodeDet.setSitedesc(R.lang(parksite.getDescription()));
                                    nodeDet.setAudiourl(parksite.getAudiourl());
                                    nodeDet.setAddress(parksite.getAddress());
                                    nodeDet.setIntroduction(parksite.getIntroduction());
                                    nodeDet.setSitepicurls(StringUtils.isBlank(parksite.getSlidepics()) ? new ArrayList<>() : Arrays.asList(parksite.getSlidepics().split(",")));

                                }
                            } else {
                                nodeDet.setSitecode(RandomUtil.randomString(8));
                            }
                        }
                    }
                }
                result.setRecords(tipsNodes);

            }
        }
        return result;
    }

    /**
     * @param req
     * @return 返回路线日程管理
     */
    @Override
    public List<AppTravelTipMap> getTravelTipDetMap(AppTraveltipMapReq req) {
        String code = req.getSearchkey();
        //Integer index = Integer.valueOf(req.getIndex());
        List<AppTravelTipMap> list = new ArrayList<>();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        TraveltipCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TRAVELTIP);
        Traveltip traveltip = cache.getRecord(projectId, code);
        if (traveltip != null && StringUtils.isNotBlank(traveltip.getTipinfo())) {
            List<TipsNode> tipsNodeList = JSONArray.parseArray(traveltip.getTipinfo(), TipsNode.class);
            ParksiteCache parksiteCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARKSITE);
            list = tipsNodeList.stream().map(r -> {
                AppTravelTipMap data = new AppTravelTipMap();
                data.setTitle(r.getTitle());//行程标题
                List<TipCoordinate> tipInfo = new ArrayList<>();
                r.getDets().stream().forEach(item -> { //行程坐标集合
                    TipCoordinate node = new TipCoordinate();
                    node.setTitle(item.getTitle());
                    //防止更新点位坐标，攻略内保存的点位坐标不是最新
                    if (StringUtils.isNotBlank(item.getSitecode())) {
                        Parksite parksite = parksiteCache.getRecord(projectId, item.getSitecode());
                        if (parksite != null) {
                            node.setCoordinate(parksite.getCoordinate());
                            node.setAudiourl(parksite.getAudiourl());
                            node.setVideourl(parksite.getVideourl());
                        }
                    }
                    tipInfo.add(node);
                });
                data.setTipInfo(tipInfo);
                return data;
            }).collect(Collectors.toList());
        }
        return list;
    }

    /**
     * @param projectId
     * @return 获取地图配置
     */
    @Override
    public Conf_Map getMapConf(String projectId) throws DefinedException {
        Conf_Map resultMap = new Conf_Map();
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        ConfYaml confYaml = sysConfCache.getConfYaml(projectId);
        if (confYaml == null) {
            throw new DefinedException("系统未配置yaml");
        }
        if (confYaml != null) {
            resultMap = PojoUtils.cloneEntity(confYaml.getMap());//避免加密污染
        }

        if (StrUtil.isNotEmpty(resultMap.getSecret())) {
            resultMap.setKey(SysFuncLibTool.encodeAesContent(resultMap.getKey(), projectId));
            resultMap.setSecret(SysFuncLibTool.encodeAesContent(resultMap.getSecret(), projectId));
        }
        return resultMap;
    }

    /**
     * 获取优惠券产品详情
     *
     * @param req
     * @return
     */
    @Override
    public AppCouponDetailRes getCouponDetail(AppGetCouponDetailReq req) {
        AppCouponDetailRes res = new AppCouponDetailRes();
        String couponcode = req.getCouponcode();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        CouponCache couponCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPON);
        Coupon coupon = couponCache.getRecord(projectId, couponcode);
        if (coupon == null) {
            return res;
        }
        CoupongroupCache coupongroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP);
        Coupongroup coupongroup = coupongroupCache.getRecord(projectId, coupon.getGroupid());
        BeanUtils.copyPropertiesIgnoreNull(coupon, res);
        res.setGroupdesc(coupongroup.getDescription());//大类描述
        res.setUnit(coupongroup.getType());//优惠券类型  金额还是折扣
        res.setCouponcode(coupon.getCode());
        //
        res.setStartget(coupongroup.getStartget());
        res.setEndget(coupongroup.getEndget());
        res.setRichtext(coupongroup.getRichtext());
        // 查看缓存库存
        Integer avl = coreCache.getCouponAvlCache(couponcode, projectId);
        res.setAvl(avl); //查询缓存
        //如果是折扣取百分比
        if (coupongroup.getType() == 1) {
            BigDecimal newPrice = res.getPrice().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            newPrice = newPrice.multiply(new BigDecimal(10));
            res.setPrice(newPrice.stripTrailingZeros());
        }
        return res;
    }

    /**
     * 演出节目列表
     *
     * @param req
     * @return
     */
    @Override
    public AppPerformListRes getPerformList(AppPerformListReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        PerFormCache perFormCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PERFORM);
        List<Perform> records = perFormCache.getDataList(projectId);
        List<AppPerformListRes.AppPerformListData> list = new ArrayList<>();
        //boolean lmobile = WebAppGlobalContext.isMobileRequest();
        Date startDate = req.getStartDate();
        //这个方法返回1-7，1是周日，而后台保存的是0-6 0是周日
        Integer week = DateUtil.dayOfWeek(startDate) - 1;
        Map<String, List<AppPerformListRes.NodeData>> map = new LinkedHashMap<>();
        if (records.size() > 0) {
            records.stream().filter(Perform::getLshow).filter(r -> {
                //过滤日期
                if (r.getStartdate().after(startDate) || r.getEnddate().before(startDate)) {
                    return false;
                }
                List<Integer> weekList = JSONArray.parseArray(r.getWeek(), Integer.class);
                if (!weekList.contains(week)) {
                    return false;
                }
                //查看排期有无包含
                return true;

            }).forEach(t -> {
                // 根据排期拆分
                List<PerformSchedule> schedules = JSONArray.parseArray(t.getSchedule(), PerformSchedule.class);
                for (PerformSchedule node : schedules) {
                    if (node.getWeek().contains(week + "")) {
                        for (String period : node.getPerformPeriod()) {
                            AppPerformListRes.NodeData data = new AppPerformListRes.NodeData();
                            data.setPerform(t.getCode());
                            data.setTitle(R.lang(t.getDescription()));
                            data.setIntroduction(R.lang(t.getIntroduction()));
                            data.setAddress(t.getAddress());
                            data.setCoordinate(t.getCoordinate());
                            data.setPicurl(ContentCacheTool.getProductOrderShowPic(ProdType.PERFORM.val(), t.getCode(), t.getProjectid(), false));
                            data.setPeriod(period);
                            data.setClickurl(t.getClickurl());
                            String time = period.split("~")[0];
                            //判断开始时间是否包含数据
                            if (map.containsKey(time)) {
                                List<AppPerformListRes.NodeData> nodeDataList = map.get(time);
                                nodeDataList.add(data);
                                map.put(time, nodeDataList);
                            } else {
                                List<AppPerformListRes.NodeData> nodeDataList = new ArrayList<>();
                                nodeDataList.add(data);
                                map.put(time, nodeDataList);
                            }
                        }
                    }

                }
            });
        }
        AppPerformListRes res = new AppPerformListRes();

        WxTemplateDataHandler handler = (WxTemplateDataHandler) customDataFactory.getHandler(SystemUtil.CustomDataKey.wxtemplate);
        Wxtemplate template = handler.getTemplate("", MsgTriggerEnum.SHOWREADY, projectId);
        res.setWxAppMsgTemplateId(template != null ? template.getOutid() : "");  //如果配置有订阅通知模板则返回模板id

        List<String> times = new ArrayList<>();
        if (!map.isEmpty()) {
            List<AppPerformListRes.AppPerformListData> finalList = list;
            times = new ArrayList<>(map.keySet());
            //排序开始时间
            times.sort(Comparator.comparing(period -> period.split("~")[0]));
            //按照顺序显示
            for (String time : times) {
                AppPerformListRes.AppPerformListData data = new AppPerformListRes.AppPerformListData();
                data.setTime(time);
                List<AppPerformListRes.NodeData> nodeDataList = map.get(time);
                nodeDataList.sort(Comparator.comparing(nodeData -> nodeData.getPeriod().split("~")[0])); // 按照时段开始时间排序
                data.setNodes(nodeDataList);
                //添加演出集合
                finalList.add(data);
            }

        } else {
            list = new ArrayList<>();
        }
        res.setTime(times);
        res.setRecords(list);
        return res;
    }

    /**
     * 演出节目详情
     *
     * @param req
     * @return
     */
    @Override
    public AppPerformProductRes getPerform(AppPerformQueryReq req) {
        AppPerformProductRes res = new AppPerformProductRes();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        PerFormCache perFormCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PERFORM);
        Perform perform = perFormCache.getRecord(projectId, req.getPerform());
        String period = req.getPeriod();
        if (perform != null) {
            res.setPerform(perform.getCode());
            res.setTitle(R.lang(perform.getDescription()));
            res.setIntroduction(R.lang(perform.getIntroduction()));
            res.setRichtext(perform.getRichtext());//富文本详情
            res.setCoordinate(perform.getCoordinate());
            //根据前端传值获取的时段返回
            res.setPeriod(period);
            List<String> picUrls = Arrays.asList(perform.getSlidepics().split(","));
            res.setPicurls(picUrls);//轮播图图片

            res.setAddress(perform.getAddress());//演出地址
            res.setClickurl(perform.getClickurl());//跳转链接


        }
        //AppProductDetailReq getGiftitemListReq = req;
        //getGiftitemListReq.setKeyword(ProdType.ITEMS.val());
        //res.setRecommend(getProductRecommendList(getGiftitemListReq));
        return res;
    }

    /**
     *
     * @return
     */
    @Override
    public AppIsLocalInvoiceRes queryInvoiceType() {
        AppIsLocalInvoiceRes res = new AppIsLocalInvoiceRes();
        String projectId = GlobalContext.getCurrentProjectId();
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysconf = sysConfCache.getOne(projectId);
        if (sysconf != null && VendorType.LOCAL_INVOICE.name().equals(sysconf.getInvoicevendor())) {
            res.setLocalInvoice(true);
        }
        return res;
    }


    @Override
    public AppProductIntroRes getMeetingProductIntro(AppProductIntroReq req) {
        AppProductIntroRes res = new AppProductIntroRes();
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        List<String> slidePics = ContentCacheTool.getProductSlidPics(ProdType.MEETING.val(), req.getProductCode(), projectid, true);
        res.setPicurls(slidePics);

        Meeting meeting = (Meeting) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.MEETING).getRecord(projectid, req.getProductCode());
        if (meeting != null) {
            res.setRichtext(meeting.getRichtext());
            res.setProp(ContentCacheTool.getPackageCodeNodeLst(projectid, meeting.getPackagecode()));
            if (StringUtils.isNotBlank(meeting.getStyle())) {
                res.setMeetingstyle(ContentCacheTool.getMeetingStyleDesc(projectid, meeting.getStyle()));
            }
        }

        return res;
    }
}
