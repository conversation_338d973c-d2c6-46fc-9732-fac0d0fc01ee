package com.cw.service.app.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.emoji.EmojiUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.func.prodfactory.impl.ProdWareInfo;
import com.cw.arithmetic.others.SkuDetail;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.customs.WxTemplateDataHandler;
import com.cw.cache.impl.CouponCache;
import com.cw.cache.impl.CoupongroupCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.cache.impl.TicketgroupCache;
import com.cw.config.confyaml.ConfYaml;
import com.cw.config.confyaml.node.Conf_Invoicenode;
import com.cw.config.exception.CustomException;
import com.cw.core.CoreCache;
import com.cw.core.CorePay;
import com.cw.core.CoreRs;
import com.cw.core.SeqNoService;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.InvoiceHandler;
import com.cw.core.vendor.invoice.InvoiceVendorHandler;
import com.cw.core.vendor.invoice.NNInvoiceVendor;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.stdop.request.StdInvoiceCreateRequest;
import com.cw.outsys.stdop.request.StdInvoicePushRequest;
import com.cw.outsys.stdop.request.StdInvoiceRedConfirmRequest;
import com.cw.outsys.stdop.response.StdInvoiceCreateResponse;
import com.cw.outsys.stdop.response.StdInvoicePushResponse;
import com.cw.outsys.stdop.response.StdInvoiceRedConfirmResponse;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.pojo.common.spuitem.SpuPackCheckInfo;
import com.cw.pojo.dto.app.req.*;
import com.cw.pojo.dto.app.req.node.*;
import com.cw.pojo.dto.app.res.*;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.res.coupon.AppCouponVoucherRes;
import com.cw.pojo.dto.conf.res.coupon.AppUsercouonGetRes;
import com.cw.pojo.dto.conf.res.coupon.CouponUserData;
import com.cw.pojo.dto.conf.res.oss.OSSUploadFileRes;
import com.cw.service.app.AppUserActionService;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.oss.OSSService;
import com.cw.utils.*;
import com.cw.utils.coupon.CouponUtil;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.CancelOptions;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.invoice.InvoiceUtil;
import com.cw.utils.oss.OSSFileTypeEnum;
import com.cw.utils.pay.PayUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/10 09:24
 **/
@Slf4j
@Service
public class AppUserActionServiceImpl implements AppUserActionService {
    @Autowired
    DaoLocal<?> daoLocal;

    @Autowired
    ShoppingitemMapper shoppingitemMapper;

    @Autowired
    Ticket_rsMapper ticket_rsMapper;

    @Autowired
    BookingrsMapper bookingrsMapper;

    @Autowired
    ActrsMapper actrsMapper;

    @Autowired
    GiftrsMapper giftrsMapper;

    @Autowired
    PrepayMapper prepayMapper;

    @Autowired
    SeqNoService seqNoService;

    @Autowired
    AppuserMapper appuserMapper;

    @Autowired
    UsertitleMapper usertitleMapper;

    @Autowired
    UserCouponMapping userCouponMapping;

    @Autowired
    RcouponMapper rcouponMapper;

    @Autowired
    CouponMapper couponMapper;

    @Autowired
    InvoiceMapper invoiceMapper;
    @Autowired
    InvoiceHisMapping invoiceHisMapping;

    @Autowired
    RedissonClient redissonClient;
    @Autowired
    CoreRs coreRs;

    @Autowired
    CorePay corePay;

    @Autowired
    CoreCache coreCache;


    @Autowired
    FeedbackMapper feedbackMapper;

    @Autowired
    InvoiceHandler invoiceHandler;


    @Resource(name = "${oss.type}")
    OSSService ossService;


    @Override
    public Integer updItem(AppUpdShopItemReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String userId = WebAppGlobalContext.getCurrentAppUserId();
        Shopping_item item = null;
        //判断购物车数量...太多的话.就不能增加了
        Integer num = shoppingitemMapper.countShopping_itemByAppuserid(userId);
        if (num >= 30) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("购物车物品数量不可超过上限"));
        }

        if (req.getItemid() < 0L) {
            item = shoppingitemMapper.findSameItem(userId, projectId, req.getStartdate(),
                    req.getEnddate(), req.getPtype(), req.getSkuid());
            if (item == null) {
                item = new Shopping_item();
                item.setProjectid(WebAppGlobalContext.getCurrentAppProjectId());
                item.setAppuserid(WebAppGlobalContext.getCurrentAppUserId());
                item.setStartdate(req.getStartdate());
                item.setEnddate(req.getEnddate());
                item.setSkuid(req.getSkuid());
                item.setPtype(req.getPtype());
                item.setNum(req.getNum());
                item.setSkugroup(ContentCacheTool.getProductGroupInfo(req.getPtype(), req.getSkuid(), item.getProjectid(), true));//关联产品组
                num = num + 1;
            } else {//同样日期产品的添加.认为是增加多一件
                item.setNum(item.getNum() + req.getNum());
            }
        } else { //通常是修改数量
            Optional<Shopping_item> itemOptional = shoppingitemMapper.findById(req.getItemid());
            if (itemOptional.isPresent()) {
                item = itemOptional.get();
                item.setNum(req.getNum());
            }
        }
        Rules rules = SysFuncLibTool.getProductRule(req.getPtype(), req.getSkuid(), projectId);
        if (rules != null && item.getNum() > rules.getLimitmax()) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("超出购物车最大数量限制"));
        }

        shoppingitemMapper.saveAndFlush(item);
        return num;
    }

    @Override
    public Long updAddr(AppUpdAddrReq req) {
        String userId = WebAppGlobalContext.getCurrentAppUserId();
        Useraddr useraddr = null;
        UseraddrMapper useraddrMapper = SpringUtil.getBean(UseraddrMapper.class);

        boolean lneedactive = false;
        if (req.getSqlid() < 0L) {
            Integer num = useraddrMapper.countUseraddrsByAppuserid(userId);
            if (num >= 5) {//收货地址不能超过5个
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("个人收货地址不可超过5个"));
            }
            useraddr = new Useraddr();
            useraddr.setProjectid(WebAppGlobalContext.getCurrentAppProjectId());
            useraddr.setAppuserid(userId);
            useraddr.setActive(num == 0 ? true : req.getActive());
            num++;
            lneedactive = num > 1 && req.getActive();//原来有多条.现在激活了.要设置其他的为非激活
        } else {
            Optional<Useraddr> optional = useraddrMapper.findById(req.getSqlid());
            if (optional.isPresent()) {
                useraddr = optional.get();
                lneedactive = !useraddr.getActive().equals(req.getActive()) && req.getActive();//原来非激活的.现在激活了.要设置其他地址非激活
                useraddr.setActive(req.getActive());
            } else {
                return -1L;
            }
        }
        useraddr.setAddressdet(req.getAddressdet());
        useraddr.setProvince(req.getProvince());
        useraddr.setCity(req.getCity());
        useraddr.setArea(req.getArea());
        useraddr.setDispdesc(req.getDispdesc());
        useraddr.setFullname(req.getFullname());
        useraddr.setTelno(req.getTelno());
        useraddr.setPostcode(req.getPostcode());

        useraddr = useraddrMapper.saveAndFlush(useraddr);
        if (lneedactive) {
            useraddrMapper.inactiveOthers(useraddr.getSqlid(), useraddr.getAppuserid());
        }
        return useraddr.getSqlid();
    }


    @Override
    public void delItem(AppDelShopItemReq req) {
        List<Long> itemids = req.getItemids();
        String uid = WebAppGlobalContext.getCurrentAppUserId();
        for (Long itemid : itemids) {
            Optional<Shopping_item> itemOptional = shoppingitemMapper.findById(itemid);
            if (itemOptional.isPresent()) {
                Shopping_item item = itemOptional.get();
                if (item.getAppuserid().equals(uid)) {  //防止地址泄露造成误删除
                    shoppingitemMapper.deleteById(itemid);
                }
            }
        }
    }

    @Override
    public void delAddr(Common_Del_Req req) {

        UseraddrMapper useraddrMapper = SpringUtil.getBean(UseraddrMapper.class);

        useraddrMapper.deleteBySqlid(req.getSqlid());

    }

    @Override
    public List<AppShopItemListNode> itemList() {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        List<Shopping_item> items = shoppingitemMapper.
                findAllByAppuseridAndProjectid(WebAppGlobalContext.getCurrentAppUserId(), projectid);


        List<AppShopItemListNode> itemListNodes = items.stream().map(r -> {
            AppShopItemListNode node = new AppShopItemListNode();
            node.setItemid(r.getSqlid());
            node.setType(r.getPtype());
            node.setPicurl(ContentCacheTool.getProductOrderShowPic(r.getPtype(), r.getSkuid(), projectid, lmobile));
            node.setProductCode(r.getSkuid());
            node.setNum(r.getNum());

            SkuDetail skuDetail = coreCache.queryMinAvailNum(r.getStartdate(), r.getEnddate(),
                    Arrays.asList(r.getSkuid()), projectid, -9999, ProdType.getProdType(r.getPtype()), r.getSkugroup());
            Rules rules = SysFuncLibTool.getProductRule(r.getPtype(), r.getSkuid(), r.getProjectid());  //商品允许的最大购买数量
            int maxnum = 0;//比较库存.返回实际最小的一个数字到前端
            if (ProdType.ITEMS.val().equals(r.getPtype())) { //如果是伴手礼类型
                maxnum = Math.min(rules.getLimitmax(), 999);
            } else {
                maxnum = Math.min(rules.getLimitmax(), skuDetail.getProductAvl(r.getSkuid()));
            }
            node.setMaxnum(maxnum);

            node.setStartdate(r.getStartdate());
            node.setEnddate(r.getEnddate());
            node.setGroupid(ContentCacheTool.getProductGroupInfo(r.getPtype(), r.getSkuid(), projectid, true));
            node.setGroupdesc(ContentCacheTool.getProductGroupInfo(r.getPtype(), r.getSkuid(), projectid, false));
            node.setDescription(ContentCacheTool.getProductDesc(r.getPtype(), r.getSkuid(), projectid));
            node.setTotalprice(ContentCacheTool.getProductCachedPrice(ProdType.getProdType(r.getPtype()), r.getStartdate(), r.getSkuid(), projectid).doubleValue());
//            BeanUtil.copyProperties(r, node);
            if (r.getPtype().equals(ProdType.TICKET.val())) {//门票是否实名制
                node.setLneedIdcard(ContentCacheTool.isNeedIdCard(r.getSkuid(), projectid));
                node.setLshowtime(ContentCacheTool.isNeedShowTime(r.getSkuid(), projectid));
                node.setLoneToMany(ContentCacheTool.isOneIdCard2ManyPerson(r.getSkuid(), projectid));
            }
            if (r.getPtype().equals(ProdType.ITEMS.val())) {//伴手礼
                Giftitem giftitem = ContentCacheTool.getGiftItemByProductStr(r.getSkuid(), projectid);
                node.setPosttype(giftitem == null ? 1 : giftitem.getPosttype());
            }
            return node;
        }).collect(Collectors.toList());
        return itemListNodes;
    }

    @Override
    public List<AppAddrItemListNode> getAddrList() {
        UseraddrMapper useraddrMapper = SpringUtil.getBean(UseraddrMapper.class);
        List<Useraddr> useraddrs = useraddrMapper.
                findAllByAppuseridAndProjectid(WebAppGlobalContext.getCurrentAppUserId(),
                        WebAppGlobalContext.getCurrentAppProjectId());
        useraddrs.sort(Comparator.comparing(Useraddr::getActive).reversed());//默认激活地址第一个显示
        List<AppAddrItemListNode> listNodes = useraddrs.stream().map(
                r -> {
                    AppAddrItemListNode node = new AppAddrItemListNode();
                    BeanUtil.copyProperties(r, node);
                    return node;
                }
        ).collect(Collectors.toList());

        return listNodes;
    }

    public AppAddrItemListNode getActiveAddrNode() {
        AppAddrItemListNode node = null;
        List<AppAddrItemListNode> nodes = getAddrList();
        for (AppAddrItemListNode appAddrItemListNode : nodes) {
            if (appAddrItemListNode.getActive()) {
                return appAddrItemListNode;
            }
        }
        if (node == null && !nodes.isEmpty()) {//有几个地址没有激活.返回最新一条
            return nodes.get(nodes.size() - 1);
        }
        return new AppAddrItemListNode();
    }


    @Override
    public List<AppShopItemGroupNode> itemGroupList() {
        List<AppShopItemListNode> list = itemList();
        Multimap<String, AppShopItemListNode> map = ArrayListMultimap.create();
        for (AppShopItemListNode appShopItemListNode : list) {
            map.put(appShopItemListNode.getType(), appShopItemListNode);
        }
        List<AppShopItemGroupNode> groupNodeList = Lists.newArrayList();
        for (Map.Entry<String, Collection<AppShopItemListNode>> entry : map.asMap().entrySet()) {
            AppShopItemGroupNode node = new AppShopItemGroupNode();
            node.setType(entry.getKey());
            node.setTypedesc(ProdType.getProdType(entry.getKey()).getDesc());
            node.setItems(Lists.newArrayList(entry.getValue()));
            groupNodeList.add(node);
        }
        return groupNodeList;
    }

    @Override
    public void validateOrder(AppOrderReq req) {

    }

    @Override
    public AppSingleOrderResult createOrder(AppOrderReq req) throws DefinedException {
//        log.info("{} {}创建订单", WebAppGlobalContext.getCurrentAppProjectId(), WebAppGlobalContext.getCurrentAppUserId());
        return coreRs.createNewRs_now(req);
    }

    @Override
    public AppBatchItem2OrderResult itemBatchOrder(AppBatchItem2OrderReq req) throws DefinedException {
        AppBatchItem2OrderResult batchItem2OrderResult = new AppBatchItem2OrderResult();
        if (req.getShopItemIds().size() > 5) {
            throw new DefinedException("批量提交一次不能超过5个", SystemUtil.SystemerrorCode.ERR015_FORMERR);
        }

//        for (OrderShopItemNode item : req.getItems()) {
//            SysFuncLibTool.checkTIcketForm(item.getTicketGuestInfo(),);
//        }

        List<Shopping_item> items = shoppingitemMapper.findAllByIds(req.getShopItemIds());
        Map<Long, OrderShopItemNode> itemForm = req.getItems().stream().collect(Collectors.toMap(OrderShopItemNode::getShopitemId,
                Function.identity(), (a, b) -> b));

        Map<Long, AppOrderReq> orderReqMap = new HashMap<>();

        List<Shopping_item> signleOrderItems = items.stream().filter(r -> !r.getPtype().equals(ProdType.ITEMS.val())).collect(Collectors.toList());
        List<Shopping_item> combineitems = items.stream().filter(r -> r.getPtype().equals(ProdType.ITEMS.val())).collect(Collectors.toList());

        List<AppOrderReq> orderReqs = signleOrderItems.stream().map(r -> {
            OrderShopItemNode node = itemForm.get(r.getSqlid());
            AppOrderReq orderReq = new AppOrderReq();
            orderReq.setStartdate(r.getStartdate());
            orderReq.setEnddate(r.getEnddate());
            orderReq.setSalesid(req.getSalesid());
            orderReq.setCheckinGuestInfo(itemForm.get(r.getSqlid()).getCheckinGuestInfo());
            orderReq.setTicketGuestInfo(itemForm.get(r.getSqlid()).getTicketGuestInfo());

            orderReq.setRemark(node.getRemark());

            OrderItemNode orderItemNode = new OrderItemNode();
            orderItemNode.setType(r.getPtype());
            orderItemNode.setNum(r.getNum());
            orderItemNode.setProductcode(r.getSkuid());
            orderReq.setItems(Collections.singletonList(orderItemNode));
            orderReqMap.put(r.getSqlid(), orderReq);
            return orderReq;
        }).collect(Collectors.toList());

        Long combieId = null;//特殊ID
        if (CollectionUtil.isNotEmpty(combineitems)) {//购物车的合并成一个请求建订单
            AppOrderReq orderReq = new AppOrderReq();
            orderReq.setStartdate(CalculateDate.getSystemDate());
            orderReq.setEnddate(orderReq.getStartdate());
            orderReq.setAddressInfo(req.getAddressInfo());//收货地址
            orderReq.setCheckinGuestInfo(req.getGiftGuestInfo());//合单购买人信息
            for (Shopping_item r : combineitems) {
                OrderItemNode orderItemNode = new OrderItemNode();
                orderItemNode.setType(r.getPtype());
                orderItemNode.setNum(r.getNum());
                orderItemNode.setProductcode(r.getSkuid());
                orderReq.getItems().add(orderItemNode);
            }
            combieId = 0L;
            orderReqMap.put(combieId, orderReq);
        }


        BigDecimal totalAmount = BigDecimal.ZERO;

        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        for (AppOrderReq orderReq : orderReqMap.values()) {
            coreRs.checkForm(orderReq, projectId);
        }

        for (Map.Entry<Long, AppOrderReq> entry : orderReqMap.entrySet()) {
            AppOrderReq orderReq = entry.getValue();
            Long itemid = entry.getKey();
            try {
                AppSingleOrderResult result = coreRs.createNewRs_now(orderReq);
                batchItem2OrderResult.getOrderids().add(result.getOrderid());

                if (itemid.equals(combieId)) {
                    for (Shopping_item combineitem : combineitems) {
                        shoppingitemMapper.deleteById(combineitem.getSqlid());  //删除伴手礼购物车物品
                    }
                } else {
                    shoppingitemMapper.deleteById(itemid);//创建订单成功后.删除购物车 ID
                }

                if (batchItem2OrderResult.getExpireTime().isEmpty()) {
                    batchItem2OrderResult.setExpireTime(result.getExpireTime());
                }
                totalAmount = totalAmount.add(result.getTotalAmount());
//                int count=  daoLocal.batchNativeOption("delete  Shopping_item where sqlid=?1", entry);
            } catch (DefinedException e) {
                if (batchItem2OrderResult.getFailDescs() == null) {
                    batchItem2OrderResult.setFailDescs(Maps.newHashMap());
                }
                batchItem2OrderResult.getFailDescs().put(itemid + "", e.getMessage());
                String faildesc = batchItem2OrderResult.getFailInfo();
                faildesc += faildesc.isEmpty() ? e.getMessage() : "\n" + e.getMessage();
                batchItem2OrderResult.setFailInfo(faildesc);
            }
        }
        batchItem2OrderResult.setTotalAmount(totalAmount);

//        log.info("购物车支付截止时间: "+DateUtil.format(DateUtil.date(Long.valueOf(batchItem2OrderResult.getExpireTime())), "yyyy-MM-dd HH:mm:ss"));

        return batchItem2OrderResult;
    }

    @Override
    public void createMeetingOrder(AppMeetingOrderReq req) {
        Meeting_rs meetingRs = new Meeting_rs();
        meetingRs.setProjectid(WebAppGlobalContext.getCurrentAppProjectId());
        meetingRs.setBookername(req.getBookname());
        meetingRs.setPersons(req.getPersonOption());
        meetingRs.setBudget(req.getBudgetOption());
        meetingRs.setRequireoption(req.getRequireOptions());
        meetingRs.setMtype(req.getMeetingType());
        meetingRs.setTelephone(req.getTelno());
        meetingRs.setUid(WebAppGlobalContext.getCurrentAppUserId());
        meetingRs.setStatus(StatusUtil.MeetingRsStatus.BOOKING);
        meetingRs.setStartdate(req.getStartdate() == null ? new Date() : req.getStartdate());
        meetingRs.setEnddate(req.getEnddate() == null ? new Date() : req.getEnddate());
        meetingRs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
        meetingRs.setCreatetime(LocalDateTime.now());
        meetingRs.setMemo(req.getRemark());//客人备注


        daoLocal.merge(meetingRs);

    }

    /**
     * 应用端发起取消
     *
     * @param req
     */
    @Override
    public void cancelOrder(AppWxCancelOrderReq req) throws DefinedException {
        //TODO  判断取消规则.是否允许取消. 是直接取消还是进入到取消申请
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();

        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RLock orderLock = redissonClient.getLock(RedisKey.getOrderLockKey(projectId, req.getOrderid()));
        boolean llock = false;
        try {
            llock = orderLock.tryLock(10, 10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new DefinedException("请稍后重试", SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);
        }

        if (llock) {//如果获取到了锁
            StdOrderData stdOrderData = coreRs.getOrderDetail(req.getOrderid(), projectId);
            Booking_rs rs = stdOrderData.getBookingRs();

            String uid = WebAppGlobalContext.getCurrentAppUserId();
            if (rs == null || !rs.getUid().equals(uid)) {
                throw new DefinedException("记录未找到", ResultCode.SYSERR.code());
            }

            //先加锁.判断订单当前状态.以及查询相关子系统中的订单状态是否可以取消
            if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.CANCEL)) {
                orderLock.unlock();
                return;
            }

            coreRs.checkOrderCanCancel(stdOrderData);//需要在退款之前.做人工审核的订单..是否允许申请人工审核.

            PrepayMapper prepayMapper = SpringUtil.getBean(PrepayMapper.class);
            Prepay prepay = prepayMapper.findPrepayByBookingid(req.getOrderid());

            CancelOrderRuleValidData data = SysFuncLibTool.judgeRuleCanCancel(rs.getArrdate(), rs.getDeptdate(),
                    new Date(), rs.getPtype(), rs.getProduct(), rs.getProjectid(), prepay);


            if (data.getLallow()) {
                UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);


                CancelOrderRuleValidData feeData = SysFuncLibTool.calcCancelPay(data, prepay, rs.getArrdate(), new Date(),
                        rs.getPtype(), rs.getProduct(), rs.getProjectid(), null);
                stdOrderData.setCancelOrderRuleValidData(feeData);// 根据付款信息.将违约金,扣款金额费用信息注入


                if (prepay != null && data.getLNeedAudit()) {//有退款就需要人工审核
                    coreRs.excuteLockBeforeAudit(stdOrderData);//如果退款需要人工审核.就提前释放订单资源.付款等财务审核再退
                    //todo 退款审核将订单信息和付款信息补充完整
                    //检查本地是否未审核的退款申请记录 有则不创建审核订单
                    AuditingMapper auditingMapper = SpringUtil.getBean(AuditingMapper.class);
                    //判断如果是含有需要审核的门票订单.先调用门票接口发起人工审核 .不让检票通过
                    if (auditingMapper.countAuditing(prepay.getBookingid(), projectId) <= 0) {
                        Auditing auditing = new Auditing();
                        auditing.setBookingid(req.getOrderid());
                        auditing.setCreatetime(LocalDateTime.now());
                        auditing.setUid(WebAppGlobalContext.getCurrentAppUserId());
                        auditing.setAstatus(StatusUtil.AuditingStatus.EXPECTED);//初始化审核状态
                        //订单信息
                        auditing.setAmount(rs.getAmount());
                        auditing.setBookername(rs.getBookername());
                        auditing.setTel(rs.getTel());
                        auditing.setMainstatus(rs.getMainstatus());
                        auditing.setPtype(rs.getPtype());
                        auditing.setRefund(data.getRefund());
                        auditing.setDebit(data.getDebit());
                        auditing.setProjectid(projectId);
                        //支付信息
                        auditing.setStatus(prepay.getPstatus() + prepay.getStatus());//付款状态+预付款状态
                        auditing.setSerialno(prepay.getSerialno());
                        auditing.setPaymethod(prepay.getPaymethod());
                        auditing.setPayno(prepay.getPayno());
                        auditing.setPaytime(prepay.getCreatetime());
                        auditing.setReason(req.getReason());//取消原因

                        daoLocal.merge(auditing);
                        rs.setMainstatus(StatusUtil.BookingRsStatus.AUDTING);//将订单状态更新为等待审核中
                        daoLocal.merge(rs);
                        //todo 发送短信通知提交退款申请
                        SysPushEvent sysPushEvent = new SysPushEvent(rs, MsgTriggerEnum.REFUNDAUDITING, rs.getTel(), "", rs.getBookingid(), rs.getProjectid());
                        SpringUtil.getApplicationContext().publishEvent(sysPushEvent);//异步发送退款申请短信


                        //判断是李庄福利票务项目
                        if ("005".equals(projectId) && ProdType.TICKET.val().equals(rs.getPtype()) &&
                                ContentCacheTool.checkTicketNotifyConsole(projectId, rs.getProduct())) {
                            sysPushEvent = new SysPushEvent(rs, MsgTriggerEnum.REFUNDNOTIFY, rs.getTel(), "", rs.getBookingid(), rs.getProjectid());
                            SpringUtil.getApplicationContext().publishEvent(sysPushEvent);//异步发送退款申请短信通知后台人员
                        }

                        //操作日志
                        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.REFUND, SystemUtil.DEFAULTUSERID,
                                rs.getBookingid(), "发起退款申请，订单号：" + rs.getBookingid(), projectId);

//                        orderLock.unlock();
                    } else {
                        return;//重复提交
                    }


                } else {
//                   data= SysFuncLibTool.calcCancelPay(data, prepay, rs.getArrdate(), new Date(), rs.getPtype(), rs.getProduct(), rs.getProjectid(), null);
                    //先取消所有子系统中的订单
                    coreRs.appCancelOrder_now(stdOrderData);//如果所有子订单取消成功
                    //如果所有子系统中的所有订单都可取消成功. 判断是否已经支付.支付就直接调用退款

                    if (prepay != null) {
                        corePay.refundPay(prepay, data);
                    }


                }
            } else {
                throw new DefinedException(data.getReason(), SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);//不能取消订单
            }

            log.info("{} {} 用户申请 取消订单成功", WebAppGlobalContext.getCurrentAppProjectId(), WebAppGlobalContext.getCurrentAppUserId());
            orderLock.unlock();

        } else {
            return;
        }


    }

    @Override
    public AppOrderDetailResult loadOrder(AppOrderOpReq req) throws DefinedException {
        AppOrderDetailResult result = new AppOrderDetailResult();
        String uid = WebAppGlobalContext.getCurrentAppUserId();
        StdOrderData stdOrderData = coreRs.getOrderDetail(req.getOrderid(), WebAppGlobalContext.getCurrentAppProjectId());//2024.11.1 just优化.别在下面自己找子订单啦
        Booking_rs rs = stdOrderData.getBookingRs();  // bookingrsMapper.findBooking_rsByBookingid(req.getOrderid());
        if (rs == null || !rs.getUid().equals(uid)) {
            throw new DefinedException("记录未找到", ResultCode.SYSERR.code());
        }

        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        result.setOrderid(rs.getBookingid());
        result.setStartdate(CalculateDate.dateToString(rs.getArrdate()));
        result.setEnddate(CalculateDate.dateToString(rs.getDeptdate()));
        result.setNum(rs.getAnz());
        result.setTotalAmount(rs.getAmount());
        result.setExpireTime(DateUtil.offset(CalculateDate.asUtilDate(rs.getCreatedate()), DateField.MINUTE,
                ContentCacheTool.getSysOrderExpireMinutue(rs.getProjectid())).getTime() + "");
        result.setGroupCode(ContentCacheTool.getProductGroupInfo(rs.getPtype(), rs.getProduct(), rs.getProjectid(), true));
        result.setGroupDesc(ContentCacheTool.getProductGroupInfo(rs.getPtype(), rs.getProduct(), rs.getProjectid(), false));
        result.setProductDesc(ContentCacheTool.getProductDesc(rs.getPtype(), rs.getProduct(), rs.getProjectid()));
        result.setBooker(rs.getBookername());
        result.setProductType(rs.getPtype());//产品类型
        if (rs.getPtype().equals(ProdType.TAOCAN.val())) {
            result.setIncludeType(ContentCacheTool.getKitItemTypeStr(rs.getBookingid(), rs.getProjectid()));
        }
        result.setStatus(rs.getMainstatus());//状态
        if (ProdType.ITEMS.val().equals(rs.getPtype())) {
            List<Gift_rs> gift_rsList = stdOrderData.getGifts();  //SpringUtil.getBean(GiftrsMapper.class).findByBookingidAndProjectidOrderByCreatetime(rs.getBookingid(), rs.getProjectid());
            if (CollectionUtil.isNotEmpty(gift_rsList)) {
                result.setGoodsStatus(gift_rsList.get(0).getStatus());//伴手礼订单发货状态
            }
        }
        result.setCreatedatetime(rs.getCreatedate().toString().replaceAll("T", " "));
        result.setProductpic(ContentCacheTool.getProductOrderShowPic(rs.getPtype(), rs.getProduct(), rs.getProjectid(), lmobile));
        result.setBookerphone(rs.getTel());
        result.setPoint(rs.getPoint());//评分
        if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY)) { //待出行提示可取消时间
            result.setCancelfreetime(ContentCacheTool.getProductCancelFreeTime(rs.getBookingid(), rs.getPtype(), rs.getProduct(), rs.getProjectid(), rs.getArrdate(), rs.getDeptdate()));//免费取消时间
        }
        //待出行状态 入住须知
        if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY) && ContentCacheTool.orderHasRoomProd(rs.getPtype(), rs.getProduct(), rs.getProjectid())) {
            result.setNotification(ContentCacheTool.getCheckinNoticeStr(rs.getProjectid(), lmobile));//todo 按照平台类型读取menu_content
        }
        result.setRemark(rs.getMemo());
        if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.AUDTING) || rs.getMainstatus().equals(StatusUtil.BookingRsStatus.CANCEL)) { //伴手礼订单可以在没收到货提交申请 被拒绝，主单状态还是PAY
            result.setRefundInfo(ContentCacheTool.getProductRefundInfo(rs.getBookingid(), rs.getProjectid(), PayUtil.ORDER_SCENE, rs.getPtype()));
            if (ProdType.ITEMS.val().equals(rs.getPtype())) {//伴手礼退款审核状态
                List<RefundInfo.RefundDetail> refundDetails = result.getRefundInfo().getDetail();
                if (CollectionUtil.isNotEmpty(refundDetails)) {
                    result.setRefundStatus(refundDetails.get(refundDetails.size() - 1).getProgressNo());//获取退货货款最后一个流程节点
                }
            }
        }
        result.setOrderProductInfo(ContentCacheTool.getProductInfo(stdOrderData));

        if (StrUtil.isNotBlank(rs.getCombineinfo())) {
            CombineInfo combineInfo = getGiftOrderExpressInfo(rs);
            result.setCombineInfo(combineInfo);

        }
        result.setUsetips(ProdFactory.getProd(ProdType.getProdType(rs.getPtype())).getUseTips(rs.getProduct(), rs.getProjectid())); //关联一个使用提示

        //TODO 套餐含票的也要出码
        if (ProdType.TICKET.val().equals(rs.getPtype()) || ContentCacheTool.orderHasTicketProd(rs.getPtype(), rs.getProduct(), rs.getProjectid())) {
            List<Ticket_rs> ticketRsList = stdOrderData.getTickets();    //ticket_rsMapper.findTicket_rsByBookingidAndProjectid(rs.getBookingid(), rs.getProjectid());
            if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY)
                    || rs.getMainstatus().equals(StatusUtil.BookingRsStatus.CHECKIN)
                    || (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.FINISH) && CalculateDate.beforeEqual(CalculateDate.getSystemDate(), rs.getDeptdate()))) {//完结状态的门票订单.未过期前都显示二维码
                //显示二维码  已支付成功   或者完成根据需要做二维码显示  2022.12.16 修改为完成状态下当天也显示.门票系统检票次数问题
                if (ticketRsList.size() > 0) {//并且显示二维码,有可能是套餐
                    result.setQrcode(ticketRsList.get(0).getQrcode());
                    result.setAssistCode(ticketRsList.get(0).getAssistcode());
                }
            }
            if (ticketRsList != null && ticketRsList.size() > 0) {
                Ticket_rs ticketRs = ticketRsList.get(0);
                if (StringUtils.isNotBlank(ticketRs.getIdinfo())) {
                    result.setTicketGuestInfo(JSON.parseArray(ticketRs.getIdinfo(), OrderGuestInfo.class));
                }
                if (!ticketRs.getExtrano().isEmpty()) {
                    String[] rows = ticketRs.getExtrano().split("\\" + SystemUtil.PARAMSIGNAL);
                    if (rows.length > 1) {
                        result.setExtranodesc(rows[0]);
                        result.setExtrano(rows[1]);
                    }
                }
                if (CollectionUtil.isNotEmpty(result.getTicketGuestInfo())) {
                    List<String> guestNames = result.getTicketGuestInfo().stream().map(r -> {
                        return r.getGuestname();
                    }).collect(Collectors.toList());
                    result.setTicketIdInfos(Joiner.on(",").join(guestNames));
                }
            }
            Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                    getRecord(rs.getProjectid(), rs.getProduct());
            if (ticket != null) {
                TicketgroupCache ticketgroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
                Ticketgroup ticketgroup = ticketgroupCache.getRecord(rs.getProjectid(), ticket.getGroupid());
                if (ticketgroup != null) {
                    result.setT_opentime(ticketgroup.getOpeningtime() + "-" + ticketgroup.getClosetime());
                    result.setSingals(ticketgroup.getCoordinate());
                    result.setT_telno(ticketgroup.getTel());
                    result.setT_address(ticketgroup.getAddress());
                    result.setT_lneedcard(ticket.getLreal());
                }
            }
        } else if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY) && (ProdType.WARES.val().equals(rs.getPtype()))) {//目前除了票类,只有卡券类有可能显示二维码
            if (ProdFactory.getProd(ProdType.getProdType(rs.getPtype())).lneedQrDisplay(rs.getProduct(), rs.getProjectid())) {
                //显示核销二维码
                ProdWareInfo wareInfo = ProdFactory.getProd(ProdType.WARES);
                Spugroup spugroup = wareInfo.getProdGroup(rs.getProduct(), rs.getProjectid(), "");
                Spusitem spusitem = wareInfo.getProdRecord(rs.getProduct(), rs.getProjectid(), "");

                result.setQrcode(SysFuncLibTool.generateEncodeQr(rs.getPtype(), rs.getBookingid(), rs.getProjectid()));
                if (!spusitem.getLpack()) {
                    result.setSingals(spugroup.getCoordinate());
                    result.setR_address(spugroup.getAddress());
                    result.setR_telno(spugroup.getTel());
                } else if (CollectionUtil.isNotEmpty(stdOrderData.getSpus())) {
                    Spu_rs spuRs = stdOrderData.getSpus().get(0);
                    result.setPackCheckInfo(wareInfo.updPackShopDesc(JSON.parseArray(spuRs.getPackinfo(), SpuPackCheckInfo.class), rs.getProjectid()));//读取写入的信息
                }
                result.setAssistCode(rs.getPtype() + rs.getBookingid());
            }
        }

        if (ProdType.ROOM.val().equals(rs.getPtype())) {//预订景区酒店
            Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).
                    getRecord(rs.getProjectid(), rs.getProduct());
            if (roomtype != null) {
                Hotel hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).
                        getRecord(rs.getProjectid(), roomtype.getHotelcode());
                if (hotel != null) {
                    result.setSingals(hotel.getCoordinate());//酒店坐标
                    result.setR_address(hotel.getAddress());//酒店 地址
                    result.setR_telno(hotel.getTel());//酒店电话
                }
            }
        }

        if (ProdType.WARES.val().equals(rs.getPtype()) && rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY)) {
            result.setAudiourl(ContentCacheTool.getAudioUrl(rs.getProduct(), rs.getProjectid()));
            result.setAudioExpireHour(ContentCacheTool.getAudioExpireHour(rs.getProduct(), rs.getProjectid()));
        }

        return result;
    }

    /**
     * @param rs 主订单
     * @return 返回伴手礼包含快递信息
     */
    private CombineInfo getGiftOrderExpressInfo(Booking_rs rs) {
        CombineInfo combineInfo = new CombineInfo();
        if (StringUtils.isNotBlank(rs.getCombineinfo())) {
            combineInfo = JSON.parseObject(rs.getCombineinfo(), CombineInfo.class);
            for (CombineInfo.CombineNode node : combineInfo.getNodes()) {
                node.setSpecpic(ContentCacheTool.getGiftitemShowPic(node.getGroupid(), node.getProductcode().split(":")[1], rs.getProjectid()));
            }
        }
        return combineInfo;
    }


    @Override
    public AppCancelValidateRes cancelValidate(AppOrderOpReq req) throws DefinedException {
        AppCancelValidateRes result = new AppCancelValidateRes();
        Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getOrderid());
//        Rules rules = SysFuncLibTool.getProductRule(rs.getPtype(), rs.getProduct(), rs.getProjectid());

        Prepay prepay = prepayMapper.findPrepayByBookingid(req.getOrderid());
        CancelOrderRuleValidData data = SysFuncLibTool.judgeRuleCanCancel(rs.getArrdate(), rs.getDeptdate(),
                new Date(), rs.getPtype(), rs.getProduct(), rs.getProjectid(), prepay);


        if (data.getLallow()) {
            result.setCanceloption(CancelOptions.ALLOW);
            if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY)) {
                //计算扣款提示信息
                CancelOrderRuleValidData validData = SysFuncLibTool.calcCancelPay(data, prepay, rs.getArrdate(), new Date(), rs.getPtype(), rs.getProduct(), rs.getProjectid(), null);

                if (CalculateNumber.isGreaterThanZero(validData.getDebit())) {
                    result.setRefundmsg(StrUtil.format("订单取消:扣款{}元,退款{}元", validData.getDebit(), validData.getRefund()));
                } else {
                    result.setRefundmsg("");
                }
            }
        } else {
            result.setCanceloption(CancelOptions.NOTALLOW);
        }
        return result;
    }


    @Override
    public AppBatchLoadOrderRes batchLoadOrder(AppBatchLoadOrderReq req) throws DefinedException {
        List<Booking_rs> bookingRsList = bookingrsMapper.findAllByBookingids(req.getOrderids());
        String uid = WebAppGlobalContext.getCurrentAppUserId();
        for (Booking_rs bookingRs : bookingRsList) {
            if (bookingRs == null || !bookingRs.getUid().equals(uid)) {
                throw new DefinedException("记录未找到", ResultCode.SYSERR.code());
            }
        }
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        AppBatchLoadOrderRes res = new AppBatchLoadOrderRes();
        List<AppOrderDetailResult> orderDetailResults = bookingRsList.stream().map(rs -> {
            AppOrderDetailResult result = new AppOrderDetailResult();
            result.setOrderid(rs.getBookingid());
            result.setStartdate(CalculateDate.dateToString(rs.getArrdate()));
            result.setEnddate(CalculateDate.dateToString(rs.getDeptdate()));
            result.setNum(rs.getAnz());
            result.setTotalAmount(rs.getAmount());
            result.setProductType(rs.getPtype());
            long payexpire = DateUtil.offset(CalculateDate.asUtilDate(rs.getCreatedate()), DateField.MINUTE,
                    ContentCacheTool.getSysOrderExpireMinutue(rs.getProjectid())).getTime();
            result.setExpireTime(payexpire + "");

            if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.CONFIRM)) {//待支付
                if (res.getExpireTime().isEmpty()) {
                    res.setExpireTime(payexpire + "");
                } else if (Long.valueOf(res.getExpireTime()) < payexpire) {   //取一个最小值给到客户端.一般是做合并支付
                    res.setExpireTime(payexpire + "");
                }
            }
            result.setGroupCode(ContentCacheTool.getProductGroupInfo(rs.getPtype(), rs.getProduct(), rs.getProjectid(), true));
            result.setGroupDesc(ContentCacheTool.getProductGroupInfo(rs.getPtype(), rs.getProduct(), rs.getProjectid(), false));
            result.setProductDesc(ContentCacheTool.getProductDesc(rs.getPtype(), rs.getProduct(), rs.getProjectid()));
            result.setBooker(rs.getBookername());
            result.setCreatedatetime(rs.getCreatedate().toString().replaceAll("T", " "));
            result.setProductpic(ContentCacheTool.getProductOrderShowPic(rs.getPtype(), rs.getProduct(), rs.getProjectid(), lmobile));
            result.setBookerphone(rs.getTel());

            if (StrUtil.isNotBlank(rs.getCombineinfo())) {
                CombineInfo combineInfo = getGiftOrderExpressInfo(rs);
                result.setCombineInfo(combineInfo);
            }
            return result;
        }).collect(Collectors.toList());
        BigDecimal totalAmount = bookingRsList.stream().map(Booking_rs::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        res.setOrders(orderDetailResults);
        res.setTotalAmount(totalAmount);
        return res;
    }

    @Override
    public void commentOrder(AppCommentOpReq req) throws DefinedException {
        Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getOrderid());
        rs.setPoint(req.getPoint());
        bookingrsMapper.saveAndFlush(rs);
    }

    public Common_response createAdvice(AppAdviceOpReq req) {
        Feedback feedback = new Feedback();
        String appuserid = WebAppGlobalContext.getCurrentAppUserId();
        App_user appUser = appuserMapper.findApp_usersByUserid(appuserid);
        Common_response response = new Common_response();
        if (appUser != null) {
            int count = daoLocal.
                    getCountOption("select count(*) from Feedback where uid=?1 and createtime>=?2",
                            appUser.getUserid(), CalculateDate.reckonDay(new Date(), 5, -1));
            if (count < 30) {//限制一下.一个用户10天内最多反馈5条
                feedback.setProjectid(appUser.getProjectid());
                feedback.setName(appUser.getNickname());
                feedback.setTel(appUser.getMobileno().isEmpty() ? req.getTelno() : appUser.getMobileno());
                feedback.setCreatetime(new Date());
                feedback.setStatus(0);
                feedback.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
                feedback.setFeedbackinfo(req.getComments());
                feedback.setPicurl(req.getPicurl());
                feedback.setPlace(req.getPlace());
                feedback.setUid(WebAppGlobalContext.getCurrentAppUserId());
                feedback.setType(req.getType());
                feedback.setArea(req.getArea());
                feedback.setAudiourl(req.getAudiourl());
                if (StrUtil.isNotBlank(req.getAudiourl()) && StrUtil.isBlank(req.getComments())) {
                    feedback.setFeedbackinfo("语音反馈");
                }

                String openid = WebAppGlobalContext.getWxAppOpenid();
                if (StrUtil.isNotBlank(openid)) {
                    WxTemplateDataHandler handler = (WxTemplateDataHandler) CustomData.getFactory().getHandler(SystemUtil.CustomDataKey.wxtemplate);
                    Wxtemplate template = handler.getTemplate("", MsgTriggerEnum.FEEDBACKOK, appUser.getProjectid());
                    if (template != null && StrUtil.isNotBlank(template.getOutid())) {
                        feedback.setWxopenid(openid);  //留给通知时使用
                        response.setSubmodelid(template.getOutid());   //交给小程序.做弹窗提示
                    }
                }
                feedback.setWxopenid(WebAppGlobalContext.getWxAppOpenid());
                daoLocal.merge(feedback);
//                log.info("意见反馈新增成功");

                SysPushEvent sysPushEvent = new SysPushEvent(feedback, MsgTriggerEnum.FEEDBACKAUDITING, feedback.getTel(), "", feedback.getRegno(), feedback.getProjectid());
                SpringUtil.getApplicationContext().publishEvent(sysPushEvent);//异步通知后台用户


            }
        }
        return response;
    }

    @Override
    public AppOrderStatusResult queryOrderPayStatus(AppOrderOpReq req) throws DefinedException {
        AppOrderStatusResult result = new AppOrderStatusResult();
        result.setStatus(StatusUtil.BookingRsStatus.INITIAL);
        Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getOrderid());
        if (rs != null) {
            if (ProdType.TICKET.val().equals(rs.getPtype())) {
                long qrcount = ticket_rsMapper.getAlreadyGetQrTicket(req.getOrderid());
                boolean getQrCode = qrcount > 0;
                if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY) && getQrCode) {
                    result.setStatus(StatusUtil.BookingRsStatus.PAY);
                }
            } else {
                result.setStatus(rs.getMainstatus());
            }
        }
        return result;
    }


    @Override
    public AppUserOrderListRes getMyOrderList(AppOrderQueryListReq req) {
//        log.info("{} {}查询订单", WebAppGlobalContext.getCurrentAppProjectId(), WebAppGlobalContext.getCurrentAppUserId());

        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());

        AppUserOrderListRes res = new AppUserOrderListRes();

        Page<Booking_rs> queryPage = bookingrsMapper.findAll((Root<Booking_rs> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            list.add(cb.equal(root.get("uid"), userid));
            list.add(cb.equal(root.get("projectid"), projectid));
            list.add(cb.equal(root.get("lhide"), false));
            if (req.getFromdate() == null) {
                req.setFromdate(CalculateDate.reckonDay(new Date(), 5, -30));
            }
            if (req.getEnddate() == null) {
                req.setEnddate(CalculateDate.reckonDay(new Date(), 5, 1)); //往前加一.防止今天买的找不到
            }
//            list.add(cb.greaterThanOrEqualTo(root.get("arrdate"), req.getFromdate()));
//            list.add(cb.lessThanOrEqualTo(root.get("deptdate"), req.getEnddate()));
            list.add(cb.between(root.get("createdate"), Convert.toLocalDateTime(req.getFromdate()), Convert.toLocalDateTime(req.getEnddate())));

            if (StrUtil.isNotEmpty(req.getStatus())) {
                list.add(cb.equal(root.get("mainstatus"), req.getStatus()));
            }
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p)));
            return query.getRestriction();
        }, PageRequest.of(req.getPages().getQueryStartPage(), req.getPages().getPagesize(), Sort.by(req.getPages().getDirection(),
                req.getPages().getSortname().split(","))));
        List<AppUserOrderListRes.AppOrderListNode> ls = queryPage.getContent().stream().map(
                row -> {
                    AppUserOrderListRes.AppOrderListNode node = new AppUserOrderListRes.AppOrderListNode();
                    node.setType(row.getPtype());
                    node.setProductCode(row.getProduct());
                    node.setOrderid(row.getBookingid());
                    node.setBooker(row.getBookername());
                    node.setGroupid(ContentCacheTool.getProductGroupInfo(row.getPtype(), row.getProduct(), projectid, true));
                    node.setNum(row.getAnz());
                    node.setDescription(ContentCacheTool.getProductDesc(row.getPtype(), row.getProduct(), projectid));
                    node.setGroupdesc(ContentCacheTool.getProductGroupInfo(row.getPtype(), row.getProduct(), projectid, false));
                    node.setPrice(row.getAmount().doubleValue());
                    node.setUsedateinfo(getUseDateInfo(row));
                    node.setStatus(row.getMainstatus());
                    node.setOrderdatetime(row.getCreatedate().toString().replaceAll("T", " "));
                    node.setTitlepicurl(ContentCacheTool.getProductOrderShowPic(row.getPtype(), row.getProduct(), projectid, lmobile));
                    return node;
                }
        ).collect(Collectors.toList());
        res.setRecords(ls);
        res.directFillPageCount((int) queryPage.getTotalElements(), queryPage.getPageable().getPageSize(),
                queryPage.getPageable().getPageNumber() + 1, queryPage.getTotalPages());

        if (req.getPages().getCurrentpage() == 1) {//如果查询在第一页.也去查一下有没有预约记录
            AppActServiceImpl actService = SpringUtil.getBean(AppActServiceImpl.class);
            AppUserActListRes actListRes = actService.getMyOrderList(req);
            List<AppUserOrderListRes.AppOrderListNode> actls = actListRes.getRecords().stream().map(r -> {
                AppUserOrderListRes.AppOrderListNode node = new AppUserOrderListRes.AppOrderListNode();
                node.setType(r.getType());
                node.setProductCode(r.getProductCode());
                node.setOrderid(r.getOrderid());
                node.setBooker(r.getBooker());
                node.setGroupid(r.getGroupid());
                node.setNum(r.getNum());
                node.setDescription(r.getGroupdesc() + r.getSitedesc());
                node.setPrice(r.getPrice());
                node.setUsedateinfo(r.getUsedateinfo());
                node.setStatus(r.getStatus());
                node.setOrderdatetime(r.getOrderdatetime());
                node.setTitlepicurl(r.getTitlepicurl());
                return node;
            }).collect(Collectors.toList());
            res.getRecords().addAll(actls);
        }
        return res;
    }

    /**
     * @param regno     反馈单号
     * @param projectId 项目id
     * @return 获取意见反馈
     */
    @Override
    public AppFeedbackRes getFeedback(String regno, String projectId) throws DefinedException {
        AppFeedbackRes feedbackRes = new AppFeedbackRes();
        Feedback feedback = feedbackMapper.findByRegnoAndProjectid(regno, projectId);
        if (feedback == null) {
            throw new DefinedException("记录未找到", ResultCode.SYSERR.code());
        }
        BeanUtil.copyProperties(feedback, feedbackRes);
        return feedbackRes;
    }

    /**
     * @param projectId 项目ID
     * @param file      上传文件
     * @param dirName   上传的OSS目录
     * @return 上传一件反馈OSS图片返回图片地址
     */
    @Override
    public OSSUploadFileRes uploadFile(String projectId, MultipartFile file, String dirName) {
        if (StringUtils.isBlank(dirName)) {
            dirName = OSSFileTypeEnum.feedback.name();
        }
        OSSUploadFileRes ossUploadFileRes = ossService.upLoadFile(file, dirName, "0", projectId, null);
        return ossUploadFileRes;
    }

    /**
     * @param projectId
     * @param file
     * @return 上传头像
     */
    @Override
    public OSSUploadFileRes uploadOSSProfilePicture(String projectId, MultipartFile file) {
        OSSUploadFileRes ossUploadFileRes = ossService.upLoadFile(file, OSSFileTypeEnum.profilepicture.name(), "0", projectId, null);
        return ossUploadFileRes;
    }

    /**
     * 伴手礼订单申请售后
     *
     * @param req
     */
    @Override
    public void GiftOrderAMS(AppWxCancelGiftOrderReq req) throws DefinedException {
        if (req.getRefund().compareTo(BigDecimal.ZERO) <= 0) { //退款金额不能为零 ，不能为负数
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("检查申请退款金额，退款金额只能为正数"));
        }
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RLock orderLock = redissonClient.getLock(RedisKey.getOrderLockKey(projectId, req.getOrderid()));
        boolean llock = false;
        try {
            llock = orderLock.tryLock(10, 10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new DefinedException("请稍后重试", SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);
        }
        if (llock) {//如果获取到了锁
            StdOrderData stdOrderData = coreRs.getOrderDetail(req.getOrderid(), projectId);
            Booking_rs rs = stdOrderData.getBookingRs();
            String uid = WebAppGlobalContext.getCurrentAppUserId();
            if (!rs.getUid().equals(uid)) {
                throw new DefinedException("记录未找到", ResultCode.SYSERR.code());
            }
            if (!ProdType.ITEMS.val().equals(rs.getPtype())) { //仅伴手礼申请售后
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单类型不支持售后退款"));
            }

            //先加锁.判断订单当前状态.以及查询相关子系统中的订单状态是否可以取消
            if (rs.getMainstatus().equals(StatusUtil.BookingRsStatus.CANCEL)) {
                orderLock.unlock();
                return;
            }
            if (stdOrderData.getGifts().size() > 0) {
                List<Gift_rs> gift_rsList = stdOrderData.getGifts();
                if (!StatusUtil.SendStatusEnum.RECEIVED.getCode().equals(gift_rsList.get(0).getStatus())) {//确定收货后才能申请售后

                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("伴手礼订单确认收货后方可申请售后"));
                } else {  // 收货14天后不可提交申请
                    LocalDateTime receiveTime = gift_rsList.get(0).getReceivetime();
                    if (receiveTime.isAfter(SystemUtil.EMPTY_LOCALTIME)) { //确定收货时间
                        ZoneId zoneId = ZoneId.systemDefault();
                        ZonedDateTime zdt = receiveTime.atZone(zoneId);
                        Date receiveDate = Date.from(zdt.toInstant());
                        long days = CalculateDate.compareDates(new Date(), receiveDate);//计算收货时间时间差
                        if (days > 14) {
                            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("伴手礼订单确认收货14天后不可申请售后"));
                        }
                    } else {
                        //没有确认收货更新收货时间
                    }
                }

            }

            coreRs.checkOrderCanCancel(stdOrderData);//需要在退款之前.做人工审核的订单..是否允许申请人工审核.
            PrepayMapper prepayMapper = SpringUtil.getBean(PrepayMapper.class);
            Prepay prepay = prepayMapper.findPrepayByBookingid(req.getOrderid());
            if (prepay == null) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("未支付伴手礼订单不可申请售后"));
            }
            CancelOrderRuleValidData data = SysFuncLibTool.judgeRuleCanCancel(rs.getArrdate(), rs.getDeptdate(),
                    new Date(), rs.getPtype(), rs.getProduct(), rs.getProjectid(), prepay);
            if (req.isLreturn()) {//退货需要扣去运费
                CombineInfo combineInfo = stdOrderData.getCombineInfo();
                BigDecimal postage = BigDecimal.ZERO;
                if (combineInfo != null) {
                    postage = combineInfo.getPostage();//额外邮费
                }
                BigDecimal canRefund = prepay.getAmount().subtract(postage);//可退金额=订单支付金额-运费
                if (canRefund.compareTo(req.getRefund()) < 0) {//申请退款金额大于可退金额
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("退款金额最多¥" + canRefund + ",含发货邮费¥" + postage));
                }
            } else if (prepay.getAmount().compareTo(req.getRefund()) < 0) {//申请退款金额大于订单支付金额
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("退款金额最多¥" + (data.getRefund())));
            }

            if (data.getLallow()) {
                UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
                CancelOrderRuleValidData feeData = SysFuncLibTool.calcCancelPay(data, prepay, rs.getArrdate(), new Date(),
                        rs.getPtype(), rs.getProduct(), rs.getProjectid(), null);
                stdOrderData.setCancelOrderRuleValidData(feeData);// 根据付款信息.将违约金,扣款金额费用信息注入
                if (data.getLNeedAudit()) {//有退款就需要人工审核
                    coreRs.excuteLockBeforeAudit(stdOrderData);//如果退款需要人工审核.就提前释放订单资源.付款等财务审核再退
                    //todo 退款审核将订单信息和付款信息补充完整
                    //检查本地是否未审核的退款申请记录 有则不创建审核订单
                    AuditingMapper auditingMapper = SpringUtil.getBean(AuditingMapper.class);
                    //判断如果是含有需要审核的门票订单.先调用门票接口发起人工审核 .不让检票通过
                    if (auditingMapper.countAuditing(prepay.getBookingid(), projectId) <= 0) {
                        Auditing auditing = new Auditing();
                        auditing.setBookingid(req.getOrderid());
                        auditing.setCreatetime(LocalDateTime.now());
                        auditing.setUid(WebAppGlobalContext.getCurrentAppUserId());
                        auditing.setAstatus(StatusUtil.AuditingStatus.EXPECTED);//初始化审核状态
                        //订单信息
                        auditing.setAmount(rs.getAmount());
                        auditing.setBookername(rs.getBookername());
                        auditing.setTel(rs.getTel());
                        auditing.setMainstatus(rs.getMainstatus());
                        auditing.setPtype(rs.getPtype());
                        auditing.setRefund(req.getRefund());
                        auditing.setDebit(data.getDebit());
                        auditing.setProjectid(projectId);
                        //支付信息
                        auditing.setStatus(prepay.getPstatus() + prepay.getStatus());//付款状态+预付款状态
                        auditing.setSerialno(prepay.getSerialno());
                        auditing.setPaymethod(prepay.getPaymethod());
                        auditing.setPayno(prepay.getPayno());
                        auditing.setPaytime(prepay.getCreatetime());
                        auditing.setReason(req.getReason());
                        auditing.setReturndesc(req.getDesc());//退款申请补充描述
                        auditing.setRapic(req.getPics());//退款凭证照片，逗号分割
                        if (rs.getPtype().equals(ProdType.ITEMS.val()) && req.isLreturn()) {
                            auditing.setLreceived(true);//是否退货
                        }
                        daoLocal.merge(auditing);
                        rs.setMainstatus(StatusUtil.BookingRsStatus.AUDTING);//将订单状态更新为等待审核中
                        daoLocal.merge(rs);
                        //todo 发送短信通知提交退款申请
                        SysPushEvent sysPushEvent = new SysPushEvent(rs, MsgTriggerEnum.REFUNDAUDITING, rs.getTel(), "", rs.getBookingid(), rs.getProjectid());
                        SpringUtil.getApplicationContext().publishEvent(sysPushEvent);//异步发送退款申请短信
                        String content = req.isLreturn() ? StrUtil.format("订单申请退货退款，订单号：{}。", rs.getBookingid()) :
                                StrUtil.format("订单申请退款，订单号：{}。", rs.getBookingid());
                        //操作日志
                        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.REFUND, SystemUtil.DEFAULTUSERID,
                                rs.getBookingid(), content, projectId);

//                        orderLock.unlock();
                    } else {
                        return;//重复提交
                    }

                } else {
                    //todo 看后续时候添加伴手礼规则，目前默认设置伴手礼规则需要审核不走此判断
                    //先取消所有子系统中的订单
                    coreRs.appCancelOrder_now(stdOrderData);//如果所有子订单取消成功
                    //如果所有子系统中的所有订单都可取消成功. 判断是否已经支付.支付就直接调用退款
                    corePay.refundPay(prepay, data);

                }
            } else {
                throw new DefinedException(data.getReason(), SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);//不能取消订单
            }

            log.info("{} {} 用户伴手礼订单申请售后成功", WebAppGlobalContext.getCurrentAppProjectId(), WebAppGlobalContext.getCurrentAppUserId());
            orderLock.unlock();

        } else {
            return;
        }

    }

    /**
     * @param req
     * @return 获取伴手礼订单可退金额信息
     */
    @Override
    public AppGiftOrderCanRefund getGiftOrderRefund(AppWxGiftOrderRefundReq req) {
        AppGiftOrderCanRefund refund = new AppGiftOrderCanRefund();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        StdOrderData stdOrderData = coreRs.getOrderDetail(req.getOrderid(), projectId);
        Booking_rs rs = stdOrderData.getBookingRs();
        if (req.isLreturn()) {//退货需要扣去邮费
            BigDecimal postage = BigDecimal.ZERO;
            CombineInfo combineInfo = stdOrderData.getCombineInfo();
            if (combineInfo != null) {
                postage = combineInfo.getPostage();
            }
            refund.setExtraprice(postage);
            BigDecimal canRefund = rs.getAmount().subtract(postage);
            refund.setRefund(canRefund);
        } else {
            refund.setRefund(rs.getAmount());
        }
        return refund;
    }

    /**
     * 前端删除订单操作，实际隐藏用户不可见
     *
     * @param req
     */
    @Override
    public void hideOrder(AppOrderOpReq req) {
        Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getOrderid());
        String uid = WebAppGlobalContext.getCurrentAppUserId();
        if (rs == null || !rs.getUid().equals(uid)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
        }
        rs.setLhide(true);
        bookingrsMapper.saveAndFlush(rs);
    }

    /**
     * @param req
     * @return 更新用户发票抬头信息
     */
    @Override
    public Long updInvoiceTitle(AppUpdInvoiceTitleReq req) {
        String userId = WebAppGlobalContext.getCurrentAppUserId();
        Usertitle usertitle = null;
        UsertitleMapper usertitleMapper = SpringUtil.getBean(UsertitleMapper.class);

        boolean lneedactive = false;
        if (req.getSqlid() <= 0L) {
            Integer num = usertitleMapper.countUsertitleByAppuserid(userId);
            if (num >= 5) {//收货地址不能超过5个
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("个人发票抬头不可超过5个"));
            }
            usertitle = new Usertitle();
            usertitle.setProjectid(WebAppGlobalContext.getCurrentAppProjectId());
            usertitle.setAppuserid(userId);
            usertitle.setActive(num == 0 ? true : req.getActive());
            num++;
            lneedactive = num > 1 && req.getActive();//原来有多条.现在激活了.要设置其他的为非激活
        } else {
            Optional<Usertitle> optional = usertitleMapper.findById(req.getSqlid());
            if (optional.isPresent()) {
                usertitle = optional.get();
                lneedactive = !usertitle.getActive().equals(req.getActive()) && req.getActive();//原来非激活的.现在激活了.要设置其他地址非激活
                usertitle.setActive(req.getActive());
            } else {
                return -1L;
            }
        }
        usertitle.setTitletype(req.getTitletype());
        usertitle.setTitle(req.getTitle());
        usertitle.setTaxno(req.getTaxno());
        usertitle.setBankname(req.getBankname());
        usertitle.setBankno(req.getBankno());
        usertitle.setAddress(req.getAddress());
        usertitle.setTel(req.getTel());
        checkUserTitleSave(usertitle);
        usertitle = usertitleMapper.saveAndFlush(usertitle);
        if (lneedactive) {
            usertitleMapper.inactiveOthers(usertitle.getSqlid(), usertitle.getAppuserid());
        }
        return usertitle.getSqlid();
    }

    /**
     * 检查参数
     *
     * @param usertitle
     */
    private void checkUserTitleSave(Usertitle usertitle) {
        if (StringUtils.isBlank(usertitle.getTitle())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票抬头不能为空"));
        }

        if (usertitle.getTitletype() == 2) {
            if (StringUtils.isBlank(usertitle.getTaxno())) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票税号不能为空"));
            } else if (!usertitle.getTaxno().matches("^[a-zA-Z0-9]{6,20}$")) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("纳税人识别号仅能输入6-20的数字和字母组合"));
            }
            if (StringUtils.isNotBlank(usertitle.getBankname()) && usertitle.getBankname().length() > 80) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("开户银行名称长度过长"));
            }
            //银行账号非空判断参数
            if (StringUtils.isNotBlank(usertitle.getBankno()) && !usertitle.getBankno().matches("^[A-Z0-9]{12,20}$")) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("银行开户行账号只支持12-20位数字和大写英文字母"));
            }

        }

    }

    /**
     * 删除用户发票抬头
     *
     * @param req
     */
    @Override
    public void delInvoiceTitle(Common_Del_Req req) {
        UsertitleMapper usertitleMapper = SpringUtil.getBean(UsertitleMapper.class);
        usertitleMapper.deleteBySqlid(req.getSqlid());
    }

    /**
     * @return 获取用户发票抬头信息列表
     */
    @Override
    public List<AppInvoiceTitleListNode> getInvoiceTitleList() {
        UsertitleMapper usertitleMapper = SpringUtil.getBean(UsertitleMapper.class);
        List<Usertitle> usertitles = usertitleMapper.findAllByAppuseridAndProjectid(WebAppGlobalContext.getCurrentAppUserId(),
                WebAppGlobalContext.getCurrentAppProjectId());
        if (CollectionUtil.isNotEmpty(usertitles)) {
            usertitles.sort(Comparator.comparing(Usertitle::getActive).reversed());//默认激活地址第一个显示
            List<AppInvoiceTitleListNode> listNodes = usertitles.stream().map(
                    r -> {
                        AppInvoiceTitleListNode node = new AppInvoiceTitleListNode();
                        BeanUtil.copyProperties(r, node);
                        return node;
                    }
            ).collect(Collectors.toList());

            return listNodes;
        }
        return new ArrayList<AppInvoiceTitleListNode>();
    }

    /**
     * @return 获取用户默认发票抬头
     */
    @Override
    public AppInvoiceTitleListNode getActiveInvoiceTitleNode() {
        AppInvoiceTitleListNode node = null;
        List<AppInvoiceTitleListNode> nodes = getInvoiceTitleList();
        for (AppInvoiceTitleListNode titleNode : nodes) {
            if (titleNode.getActive()) {
                return titleNode;
            }
        }
        if (node == null && !nodes.isEmpty()) {//有几个地址没有激活.返回最新一条
            return nodes.get(nodes.size() - 1);
        }
        return new AppInvoiceTitleListNode();
    }

    /**
     * 客户端发票开具申请
     *
     * @param req
     */
    @Override
    public void invoiceCreate(AppInvoiceCreateReq req) throws DefinedException {
        //格式化非空字段携带表情包
        req.setTitle(EmojiUtil.removeAllEmojis(req.getTitle().replaceAll(" ", "")));
        req.setTaxno(EmojiUtil.removeAllEmojis(req.getTaxno().replaceAll(" ", "")));
        req.setBankname(EmojiUtil.removeAllEmojis(req.getBankname().replaceAll(" ", "")));
        req.setAddress(EmojiUtil.removeAllEmojis(req.getAddress().replaceAll(" ", "")));
        req.setBankno(EmojiUtil.removeAllEmojis(req.getBankno().replaceAll(" ", "")));
        req.setTel(EmojiUtil.removeAllEmojis(req.getTel().replaceAll(" ", "")));
        if (StringUtils.isNotBlank(req.getEmail())) {
            req.setEmail(EmojiUtil.removeAllEmojis(req.getEmail().replaceAll(" ", "")));
        }
        if (StringUtils.isNotBlank(req.getPhone())) {
            req.setPhone(EmojiUtil.removeAllEmojis(req.getPhone().replaceAll(" ", "")));
        }
        if (StringUtils.isBlank(req.getEmail()) && StringUtils.isBlank(req.getPhone())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("电子发票接收方式至少需要邮箱或手机号码其中一个"));
        }
        //申请发票开具 重复提交，状态没改
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String orderStatus = null;
        String type = null;
        // 判断订单号是否是预约订单
        if (!req.getBookingid().startsWith("Y")) {//正常主单
            Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getBookingid());
            if (rs == null || !rs.getUid().equals(userid)) {
                throw new DefinedException("记录未找到", SystemUtil.SystemerrorCode.ERR015_FORMERR);
            }
            orderStatus = rs.getMainstatus();
            type = rs.getPtype();
        } else { //预约订单
            Act_rs rs = actrsMapper.findAct_rsByBookingid(req.getBookingid());
            if (rs == null || !rs.getUid().equals(userid)) {
                throw new DefinedException("记录未找到", SystemUtil.SystemerrorCode.ERR015_FORMERR);
            }
            orderStatus = rs.getStatus();
            type = ProdType.ACTGROUP.val();//预约先固定票务类型通过发票配置校验
        }
        List<Invoice> invoices = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(req.getBookingid(), projectId);
        int invoiceNum = 0;
        if (CollectionUtil.isNotEmpty(invoices)) {//判断是否重复提交
            List<Invoice> redInvoices = invoices.stream().filter(I -> StatusUtil.InvoiceStatusEnum.REDED.getCode().equals(I.getIstatus())).collect(Collectors.toList());
            invoiceNum = invoices.size();//订单红冲次数，修改抬头次数
            Invoice dbInvoice = invoices.get(0);//获取第一条最新数据 如果抬头信息相同，则按照邮箱重发， 否则红冲电子发票再重新申请发票
            if (!StatusUtil.InvoiceStatusEnum.REDED.getCode().equals(dbInvoice.getIstatus())) {
                if ((StatusUtil.InvoiceStatusEnum.INIT.getCode().equals(dbInvoice.getIstatus()) ||
                        StatusUtil.InvoiceStatusEnum.REFUSE.getCode().equals(dbInvoice.getIstatus()) ||
                        StatusUtil.InvoiceStatusEnum.NEEDRED.getCode().equals(dbInvoice.getIstatus()))) {
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("电子发票未开具成功，请等待审核"));
                }
                if (StatusUtil.InvoiceStatusEnum.REPUSH.getCode().equals(dbInvoice.getIstatus())) {
                    return;
                }

                checkInvoicePushOrRed(req, dbInvoice, userid, redInvoices.size());
                return;
            }

        }

        if (StatusUtil.BookingRsStatus.CANCEL.equals(orderStatus)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("取消订单不可申请开票"));
        }
        if (!StatusUtil.BookingRsStatus.FINISH.equals(orderStatus)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("未完结订单不可申请开票"));
        }
        if (!InvoiceUtil.checkProdTypeCanInvoice(projectId, type)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单类型不支持线上开票"));
        }
        //校验订单是否可以开具
        if (judgeInvoiceCreate(req.getBookingid(), projectId)) {
            LocalDateTime now = LocalDateTime.now();
            //如果是本地发票模式 收集发票信息
            if (checkLocalInvoiceType(projectId)) {
                //如果是本地发票收集，直接填充
                collectInvoiceInfo(projectId, userid, req, now);
                return;
            }
            InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);

            StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
            request.setTitle(req.getTitle());//抬头
            if (StringUtils.isNotBlank(req.getEmail())) {
                request.setEmail(req.getEmail());//接收电子发票的邮箱
            }
            if (StringUtils.isNotBlank(req.getPhone())) {
                request.setPhone(req.getPhone());
            }
            String orderNo = req.getBookingid() + invoiceNum; //主订单号 + 发票记录list.size() 标识唯一订单号发送给
            request.setBookingid(orderNo);
            if (req.getTitletype() == 2) { //企业类型选填
                request.setTitletype(2);//企业类型
                request.setTaxno(req.getTaxno());//企业类型抬头必填
                if (StringUtils.isNotBlank(req.getAddress())) {
                    request.setAddress(req.getAddress());
                }
                if (StringUtils.isNotBlank(req.getTel())) {
                    request.setTel(req.getTel());
                }
                if (StringUtils.isNotBlank(req.getBankname())) {
                    request.setBankname(req.getBankname());
                }
                if (StringUtils.isNotBlank(req.getBankno())) {
                    request.setBankno(req.getBankno());
                }
            }
            //发票明细 按照订单
            List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
            details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, req.getBookingid(), false));//订单信息转换发票明细
            request.setDetail(details);

            //todo 参数转换
            request.setProjectId(projectId);
            StdInvoiceCreateResponse response = invoiceVendorHandler.InvoiceCreate(request);//todo 诺诺发票开票失败重开接口问题
            //todo 判断是否直接生成记录 不用请求 或者对接
            if (response.getStd_flag()) {//请求成功 保存本地发票记录
                Invoice dbInvoice = new Invoice();
                dbInvoice.setProjectid(projectId);
                dbInvoice.setBookingid(req.getBookingid());
                dbInvoice.setOrderno(orderNo);
                //发票抬头信息
                dbInvoice.setType(1);//默认只有增值税电子普票
                dbInvoice.setTitle(req.getTitle());
                dbInvoice.setTitletype(req.getTitletype());
                dbInvoice.setTaxno(req.getTaxno());
                dbInvoice.setBankname(req.getBankname());
                dbInvoice.setBankno(req.getBankno());
                dbInvoice.setAddress(req.getAddress());
                dbInvoice.setTel(req.getTel());
                dbInvoice.setEmail(req.getEmail());
                dbInvoice.setMemo(JSON.toJSONString(details));//发票明细
                dbInvoice.setCreatetime(now);//发票申请时间
                //发票返回信息
                dbInvoice.setCreatedate(response.getDate());//开票成功日期
                dbInvoice.setInvoiceno(response.getInvoiceno());
                dbInvoice.setInvoiceid(response.getInvoiceid());//发票流水号
                dbInvoice.setInvoicecode(response.getInvoicecode());//发票代码
                dbInvoice.setCheckcode(response.getCheckcode());//发票检查码
                dbInvoice.setPfdurl(response.getPfdurl());
                dbInvoice.setQrurl(response.getQrurl());
                if (StringUtils.isNotBlank(response.getJpgurl())) {
                    dbInvoice.setJpgurl(response.getJpgurl());
                }
                dbInvoice.setExtaxamount(response.getExtaxamount());//不含税金额
                dbInvoice.setIncltaxamount(response.getIncltaxamount());//含税金额
                dbInvoice.setUid(userid);
                //调用发票查询接口后再更改状态

                if (CheckInvoiceType(projectId)) {
                    //判断接口调用发票情况，JL发票直接开票完成
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.FINISH.getCode());//JL发票直接开票完成
                } else {
                    dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());//初始化发票开票成功状态
                }
                invoiceMapper.saveAndFlush(dbInvoice);
                String content = StrUtil.format("【发票开具】申请开票，主订单号：{}, 发票流水号：{}",
                        dbInvoice.getBookingid(), dbInvoice.getInvoiceid());
                UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
                userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
                        dbInvoice.getBookingid(), content, projectId);
            } else {
                throw new DefinedException(response.getStd_sub_msg());
            }

        }

    }

    /**
     * 2025-07-10 收集发票信息本地提交
     *
     * @param projectId
     * @param userid
     * @param req
     * @param now
     */
    private void collectInvoiceInfo(String projectId, String userid, AppInvoiceCreateReq req, LocalDateTime now) {
        Invoice dbInvoice = new Invoice();
        dbInvoice.setProjectid(projectId);
        dbInvoice.setBookingid(req.getBookingid());
        // dbInvoice.setOrderno(orderNo);
        //发票抬头信息
        dbInvoice.setType(req.getType());//本地化手机发票按前端传的
        dbInvoice.setTitle(req.getTitle());
        dbInvoice.setTitletype(req.getTitletype());
        dbInvoice.setTaxno(req.getTaxno());
        dbInvoice.setBankname(req.getBankname());
        dbInvoice.setBankno(req.getBankno());
        dbInvoice.setAddress(req.getAddress());
        dbInvoice.setTel(req.getTel());
        dbInvoice.setEmail(req.getEmail());
        dbInvoice.setPhone(req.getPhone());
        List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
        details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, req.getBookingid(), false));//订单信息转换发票明细

        dbInvoice.setMemo(JSON.toJSONString(details));//发票明细
        dbInvoice.setCreatetime(now);//发票申请时间
        //发票返回信息
        // dbInvoice.setCreatedate(response.getDate());//开票成功日期
        // dbInvoice.setInvoiceno(response.getInvoiceno());
        // dbInvoice.setInvoiceid(response.getInvoiceid());//发票流水号
        // dbInvoice.setInvoicecode(response.getInvoicecode());//发票代码
        // dbInvoice.setCheckcode(response.getCheckcode());//发票检查码
        // dbInvoice.setPfdurl(response.getPfdurl());
        // dbInvoice.setQrurl(response.getQrurl());
        // if (StringUtils.isNotBlank(response.getJpgurl())) {
        //     dbInvoice.setJpgurl(response.getJpgurl());
        // }
        // dbInvoice.setExtaxamount(response.getExtaxamount());//不含税金额
        // dbInvoice.setIncltaxamount(response.getIncltaxamount());//含税金额
        dbInvoice.setUid(userid);
        //调用发票查询接口后再更改状态
        dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());//初始化发票开票成功状态
        invoiceMapper.saveAndFlush(dbInvoice);
        String content = StrUtil.format("【发票开具】申请开票，主订单号：{}",
                dbInvoice.getBookingid());
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
                dbInvoice.getBookingid(), content, projectId);
    }

    /**
     * 检查系统启用发票类型,JL发票类型直接开票完成
     *
     * @param projectId
     * @return
     */
    private boolean CheckInvoiceType(String projectId) {
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysconf = sysConfCache.getOne(projectId);
        if (sysconf != null && VendorType.JL_INVOICE.name().equals(sysconf.getInvoicevendor())) {
            return true;
        }
        return false;
    }

    /**
     * 检查是否本地发票，直接提交
     *
     * @param projectId
     * @return
     */
    private boolean checkLocalInvoiceType(String projectId) {
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysconf = sysConfCache.getOne(projectId);
        if (sysconf != null && VendorType.LOCAL_INVOICE.name().equals(sysconf.getInvoicevendor())) {
            return true;
        }
        return false;
    }

    /**
     * @param postReq       开具发票请求参数
     * @param dbInvoice     本地最新发票记录
     * @param userid        用户id
     * @param invoiceRedNum 已红冲开发票记录数
     */
    private void checkInvoicePushOrRed(AppInvoiceCreateReq postReq, Invoice dbInvoice, String userid, int invoiceRedNum) throws DefinedException {
        String projectId = dbInvoice.getProjectid();
        String bookingId = dbInvoice.getBookingid();
        //todo 判断postReq的 type和 dbInvoice的type类型是否修改，修改则红冲
        boolean isModified = false;
        // 1. 判断发票类型是否修改（1=电子增值税普通发票，2=电子增值税专用发票）
        if (postReq.getType() != null && !postReq.getType().equals(dbInvoice.getType())) {
            // 如果请求中有type字段，则比较类型
            isModified = true;
            log.info("订单{}发票类型已修改：原类型{} → 新类型{}", bookingId, dbInvoice.getType(), postReq.getType());
        }
        // 2. 判断抬头类型和相关信息是否修改
        if (!isModified) {
            if (postReq.getTitletype() == 1) { // 个人类型
                isModified = !dbInvoice.getTitletype().equals(postReq.getTitletype())
                        || !dbInvoice.getTitle().equals(postReq.getTitle());
            } else if (postReq.getTitletype() == 2) { // 企业类型
                isModified = isEnterpriseInfoModified(postReq, dbInvoice);
            } else {
                isModified = true; // 未知类型按修改处理
            }
        }

        if (isModified) {
            // 执行红冲操作
            reverseInvoice(dbInvoice, projectId, userid, postReq, invoiceRedNum);
        } else {
            // 重发逻辑（未修改时执行）
            AppInvoicePushReq pushReq = new AppInvoicePushReq();
            pushReq.setBookingid(postReq.getBookingid());
            // 更新接收方式
            if (StringUtils.isNotBlank(postReq.getEmail())) {
                pushReq.setEmail(postReq.getEmail());
                dbInvoice.setEmail(postReq.getEmail());
                log.info("订单{}发票信息重发到邮箱：{}", bookingId, postReq.getEmail());
            }
            if (StringUtils.isNotBlank(postReq.getPhone())) {
                pushReq.setPhone(postReq.getPhone());
                dbInvoice.setPhone(postReq.getPhone());
                log.info("订单{}发票信息重发到手机：{}", bookingId, postReq.getPhone());
            }

            // 处理发票推送
            if (checkLocalInvoiceType(projectId)) {
                dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REPUSH.getCode());
            } else {
                invoicePush(pushReq, dbInvoice.getProjectid());
            }
            invoiceMapper.saveAndFlush(dbInvoice);
        }

        //
        // if (postReq.getTitletype() == 1 && dbInvoice.getTitletype().equals(postReq.getTitletype()) &&
        //         dbInvoice.getTitle().equals(postReq.getTitle())) {//个人类型抬头信息 没修改直接重发发票到提交有效
        //     AppInvoicePushReq pushReq = new AppInvoicePushReq();
        //     pushReq.setBookingid(postReq.getBookingid());
        //     if (StringUtils.isNotBlank(postReq.getEmail())) {//接收邮箱
        //         pushReq.setEmail(postReq.getEmail());
        //         dbInvoice.setEmail(postReq.getEmail());
        //         log.info("订单" + bookingId + "发票信息重发到邮箱：" + postReq.getEmail());
        //     }
        //     if (StringUtils.isNotBlank(postReq.getPhone())) { //接收电话号码
        //         pushReq.setPhone(postReq.getPhone());
        //         dbInvoice.setPhone(postReq.getPhone());
        //         log.info("订单" + bookingId + "发票信息重发到手机：" + postReq.getPhone());
        //     }
        //     if (checkLocalInvoiceType(projectId)) {
        //       dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REPUSH.getCode());
        //     }else {
        //         invoicePush(pushReq, dbInvoice.getProjectid());
        //     }
        //     invoiceMapper.saveAndFlush(dbInvoice);
        // } else if (postReq.getTitletype() == 2 && dbInvoice.getTitletype().equals(postReq.getTitletype()) && dbInvoice.getTitle().equals(postReq.getTitle()) &&
        //         postReq.getTaxno().equals(dbInvoice.getTaxno()) && postReq.getBankname().equals(dbInvoice.getBankname()) &&
        //         postReq.getBankno().equals(dbInvoice.getBankno()) && postReq.getTel().equals(dbInvoice.getTel()) &&
        //         postReq.getAddress().equals(dbInvoice.getAddress())) { //企业类型抬头没修改 判断选填内容是否一致
        //     AppInvoicePushReq pushReq = new AppInvoicePushReq();
        //     pushReq.setBookingid(postReq.getBookingid());
        //     if (StringUtils.isNotBlank(postReq.getEmail())) {//接收邮箱
        //         pushReq.setEmail(postReq.getEmail());
        //         dbInvoice.setEmail(postReq.getEmail());
        //         log.info("订单" + bookingId + "发票信息重发到邮箱：" + postReq.getEmail());
        //     }
        //     if (StringUtils.isNotBlank(postReq.getPhone())) { //接收电话号码
        //         pushReq.setPhone(postReq.getPhone());
        //         dbInvoice.setPhone(postReq.getPhone());
        //         log.info("订单" + bookingId + "发票信息重发到手机：" + postReq.getPhone());
        //     }
        //     if (checkLocalInvoiceType(projectId)) {
        //         dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.REPUSH.getCode());
        //     }else {
        //         invoicePush(pushReq, dbInvoice.getProjectid());
        //     }
        //     invoiceMapper.saveAndFlush(dbInvoice);
        // } else {
        //     Integer lred = SystemUtil.invoiceUpdNum; //系统默认可红冲次数
        //     SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        //     ConfYaml confYaml = sysConfCache.getConfYaml(projectId);
        //     if (confYaml != null && confYaml.getInvoice() != null && confYaml.getInvoice().getLred() != null) {
        //         lred = confYaml.getInvoice().getLred();//发票配置限制红冲次数
        //     }
        //     if (lred == 0) {
        //         throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单已开发票，如需修改抬头请联系客服"));
        //     }
        //     if (invoiceRedNum >= lred) {//冲红次数超过配置许可次数
        //         throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("修改抬头信息达到上限，无法修改"));
        //     }
        //     //如果是本地半自动话 修改
        //     if (checkLocalInvoiceType(projectId)) {
        //         dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.NEEDRED.getCode());
        //         dbInvoice.setHandletime(new Date());//操作时间
        //         dbInvoice.setHandler(SystemUtil.DEFAULTUSERID);//操作人
        //         invoiceMapper.saveAndFlush(dbInvoice);
        //
        //         collectInvoiceInfo(projectId, userid, postReq, LocalDateTime.now());
        //         //保存提交申请记录
        //         return;
        //     }
        //     //修改了抬头信息，需要红冲发票 并重新创建发票记录
        //     //判断发票厂商，如果是诺诺发票则使用新的发票冲红接口
        //
        //     InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
        //     if (invoiceVendorHandler instanceof NNInvoiceVendor) {
        //         //诺诺发票红冲
        //         NNInvoiceRedHttp(postReq, invoiceVendorHandler, dbInvoice, projectId, userid);
        //     } else {
        //         StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
        //         request.setTitle(dbInvoice.getTitle());
        //         if (postReq.getTitletype() == 2) { //企业类型选填
        //             request.setTaxno(postReq.getTaxno());//企业类型抬头必填
        //             if (StringUtils.isNotBlank(postReq.getAddress())) {
        //                 request.setAddress(postReq.getAddress());
        //             }
        //             if (StringUtils.isNotBlank(postReq.getTel())) {
        //                 request.setTel(postReq.getTel());
        //             }
        //             if (StringUtils.isNotBlank(postReq.getBankname())) {
        //                 request.setBankname(postReq.getBankname());
        //             }
        //             if (StringUtils.isNotBlank(postReq.getBankno())) {
        //                 request.setBankno(postReq.getBankno());
        //             }
        //         }
        //         request.setType(1);//标记红冲类型 发票代码和发票号码必填
        //         request.setInvoicecode(dbInvoice.getInvoicecode());
        //         request.setInvoiceno(dbInvoice.getInvoiceno());
        //         //request.setEmail(postReq.getEmail());//红冲不需要发送发票
        //         List<Invoice> invoices = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(dbInvoice.getBookingid(), projectId);
        //         Integer redCode = 10;//bookingid + redcode 标识唯一红冲记录
        //         if (CollectionUtil.isNotEmpty(invoices)) {
        //             redCode = redCode + invoices.size();
        //         }
        //         request.setBookingid(dbInvoice.getBookingid() + redCode);//红冲订单编号
        //         //发票明细 按照订单
        //         List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
        //         //红冲发票明细
        //         details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), true));//订单信息转换发票明细
        //         request.setDetail(details);
        //
        //         request.setProjectId(projectId);
        //         StdInvoiceCreateResponse response = invoiceVendorHandler.InvoiceCreate(request);
        //         if (response.getStd_flag()) {//请求成功  保存本地发票记录
        //             log.info("订单" + bookingId + "修改抬头信息红冲");
        //             dbInvoice.setRedinvoiceid(response.getInvoiceid());//红冲返回的发票流水号
        //             dbInvoice.setHandletime(new Date());//操作时间
        //             dbInvoice.setHandler(SystemUtil.DEFAULTUSERID);//操作人
        //             //}
        //             invoiceMapper.saveAndFlush(dbInvoice);
        //             UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        //             String content = StrUtil.format("【发票红冲】发票流水号：{},操作人:{}", response.getInvoiceid(), SystemUtil.DEFAULTUSERID);
        //             userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
        //                     dbInvoice.getBookingid(), content, projectId);
        //
        //             //红冲流程新建发票记录，等红冲流程结束再提交发票开具请求
        //             List<Invoice> dbList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(bookingId, projectId);
        //             Invoice newInvoice = new Invoice();
        //             newInvoice.setProjectid(projectId);
        //             newInvoice.setBookingid(bookingId);
        //             newInvoice.setOrderno(bookingId + dbList.size());//标识唯一订单号发送给诺诺发票
        //             //发票抬头信息
        //             newInvoice.setType(1);//默认只有增值税电子普票
        //             newInvoice.setTitle(postReq.getTitle());
        //             newInvoice.setTitletype(postReq.getTitletype());
        //             newInvoice.setTaxno(postReq.getTaxno());
        //             newInvoice.setBankname(postReq.getBankname());
        //             newInvoice.setBankno(postReq.getBankno());
        //             newInvoice.setAddress(postReq.getAddress());
        //             newInvoice.setTel(postReq.getTel());
        //             newInvoice.setEmail(postReq.getEmail());
        //             newInvoice.setPhone(postReq.getPhone());
        //             List<StdInvoiceCreateRequest.InvoiceInfoDetail> newDetails = new ArrayList<>();
        //             //发票明细
        //             newDetails.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), false));//订单信息转换发票明细
        //             newInvoice.setMemo(JSON.toJSONString(newDetails));//发票明细
        //             newInvoice.setCreatetime(LocalDateTime.now());//发票申请时间
        //             newInvoice.setUid(userid);//用户
        //             newInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());
        //             invoiceMapper.saveAndFlush(newInvoice);
        //         }
        //     }
        // }
    }

    /**
     * 发票红冲
     *
     * @param dbInvoice
     */
    private void reverseInvoice(Invoice dbInvoice, String projectId, String userid, AppInvoiceCreateReq postReq, int invoiceRedNum) throws DefinedException {
        Integer lred = SystemUtil.invoiceUpdNum; //系统默认可红冲次数
        String bookingId = dbInvoice.getBookingid();
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        ConfYaml confYaml = sysConfCache.getConfYaml(projectId);
        if (confYaml != null && confYaml.getInvoice() != null && confYaml.getInvoice().getLred() != null) {
            lred = confYaml.getInvoice().getLred();//发票配置限制红冲次数
        }
        if (lred == 0) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单已开发票，如需修改抬头请联系客服"));
        }
        if (invoiceRedNum >= lred) {//冲红次数超过配置许可次数
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("修改抬头信息达到上限，无法修改"));
        }
        //如果是本地半自动话 修改
        if (checkLocalInvoiceType(projectId)) {
            dbInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.NEEDRED.getCode());
            dbInvoice.setHandletime(new Date());//操作时间
            dbInvoice.setHandler(SystemUtil.DEFAULTUSERID);//操作人
            invoiceMapper.saveAndFlush(dbInvoice);

            collectInvoiceInfo(projectId, userid, postReq, LocalDateTime.now());
            //保存提交申请记录
            return;
        }
        //修改了抬头信息，需要红冲发票 并重新创建发票记录
        //判断发票厂商，如果是诺诺发票则使用新的发票冲红接口

        InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
        if (invoiceVendorHandler instanceof NNInvoiceVendor) {
            //诺诺发票红冲
            NNInvoiceRedHttp(postReq, invoiceVendorHandler, dbInvoice, projectId, userid);
        } else {
            StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
            request.setTitle(dbInvoice.getTitle());
            if (postReq.getTitletype() == 2) { //企业类型选填
                request.setTaxno(postReq.getTaxno());//企业类型抬头必填
                if (StringUtils.isNotBlank(postReq.getAddress())) {
                    request.setAddress(postReq.getAddress());
                }
                if (StringUtils.isNotBlank(postReq.getTel())) {
                    request.setTel(postReq.getTel());
                }
                if (StringUtils.isNotBlank(postReq.getBankname())) {
                    request.setBankname(postReq.getBankname());
                }
                if (StringUtils.isNotBlank(postReq.getBankno())) {
                    request.setBankno(postReq.getBankno());
                }
            }
            request.setType(1);//标记红冲类型 发票代码和发票号码必填
            request.setInvoicecode(dbInvoice.getInvoicecode());
            request.setInvoiceno(dbInvoice.getInvoiceno());
            //request.setEmail(postReq.getEmail());//红冲不需要发送发票
            List<Invoice> invoices = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(dbInvoice.getBookingid(), projectId);
            Integer redCode = 10;//bookingid + redcode 标识唯一红冲记录
            if (CollectionUtil.isNotEmpty(invoices)) {
                redCode = redCode + invoices.size();
            }
            request.setBookingid(dbInvoice.getBookingid() + redCode);//红冲订单编号
            //发票明细 按照订单
            List<StdInvoiceCreateRequest.InvoiceInfoDetail> details = new ArrayList<>();
            //红冲发票明细
            details.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), true));//订单信息转换发票明细
            request.setDetail(details);

            request.setProjectId(projectId);
            StdInvoiceCreateResponse response = invoiceVendorHandler.InvoiceCreate(request);
            if (response.getStd_flag()) {//请求成功  保存本地发票记录
                log.info("订单" + bookingId + "修改抬头信息红冲");
                dbInvoice.setRedinvoiceid(response.getInvoiceid());//红冲返回的发票流水号
                dbInvoice.setHandletime(new Date());//操作时间
                dbInvoice.setHandler(SystemUtil.DEFAULTUSERID);//操作人
                //}
                invoiceMapper.saveAndFlush(dbInvoice);
                UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
                String content = StrUtil.format("【发票红冲】发票流水号：{},操作人:{}", response.getInvoiceid(), SystemUtil.DEFAULTUSERID);
                userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                        dbInvoice.getBookingid(), content, projectId);

                //红冲流程新建发票记录，等红冲流程结束再提交发票开具请求
                List<Invoice> dbList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(bookingId, projectId);
                Invoice newInvoice = new Invoice();
                newInvoice.setProjectid(projectId);
                newInvoice.setBookingid(bookingId);
                newInvoice.setOrderno(bookingId + dbList.size());//标识唯一订单号发送给诺诺发票
                //发票抬头信息
                newInvoice.setType(1);//默认只有增值税电子普票
                newInvoice.setTitle(postReq.getTitle());
                newInvoice.setTitletype(postReq.getTitletype());
                newInvoice.setTaxno(postReq.getTaxno());
                newInvoice.setBankname(postReq.getBankname());
                newInvoice.setBankno(postReq.getBankno());
                newInvoice.setAddress(postReq.getAddress());
                newInvoice.setTel(postReq.getTel());
                newInvoice.setEmail(postReq.getEmail());
                newInvoice.setPhone(postReq.getPhone());
                List<StdInvoiceCreateRequest.InvoiceInfoDetail> newDetails = new ArrayList<>();
                //发票明细
                newDetails.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), false));//订单信息转换发票明细
                newInvoice.setMemo(JSON.toJSONString(newDetails));//发票明细
                newInvoice.setCreatetime(LocalDateTime.now());//发票申请时间
                newInvoice.setUid(userid);//用户
                newInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());
                invoiceMapper.saveAndFlush(newInvoice);
            }
        }
    }

    /**
     * 判断
     *
     * @param postReq
     * @param dbInvoice
     * @return
     */
    private boolean isEnterpriseInfoModified(AppInvoiceCreateReq postReq, Invoice dbInvoice) {
        // 类型不匹配直接返回修改
        if (!dbInvoice.getTitletype().equals(postReq.getTitletype())) {
            return true;
        }
        // 使用安全比较方法检查关键字段
        return !Objects.equals(dbInvoice.getTitle(), postReq.getTitle()) ||
                !Objects.equals(dbInvoice.getTaxno(), postReq.getTaxno()) ||
                !Objects.equals(dbInvoice.getBankname(), postReq.getBankname()) ||
                !Objects.equals(dbInvoice.getBankno(), postReq.getBankno()) ||
                !Objects.equals(dbInvoice.getTel(), postReq.getTel()) ||
                !Objects.equals(dbInvoice.getAddress(), postReq.getAddress());
    }

    /**
     * 诺诺发票升级后 PC数电发票票种另外额度冲红接口
     *
     * @param postReq
     * @param invoiceVendorHandler
     * @param dbInvoice
     * @param projectId
     * @param userid
     */
    private void NNInvoiceRedHttp(AppInvoiceCreateReq postReq, InvoiceVendorHandler invoiceVendorHandler, Invoice dbInvoice, String projectId, String userid) throws DefinedException {
        //需要先申请红字确认单

        String bookingId = dbInvoice.getBookingid();
        StdInvoiceRedConfirmRequest redConfirmRequest = new StdInvoiceRedConfirmRequest();
        redConfirmRequest.setProjectId(projectId);
        redConfirmRequest.setTitle(dbInvoice.getTitle());
        redConfirmRequest.setInvoiceNo(dbInvoice.getInvoiceno());
        //冲红申请红字确定单编号
        StdInvoiceRedConfirmResponse response = invoiceVendorHandler.NNInvoiceRedCreateConfirm((redConfirmRequest));
        if (response.getStd_flag()) {//请求成功  保存本地发票记录
            log.info("订单" + bookingId + "申请冲红确定号：" + response.getBillNo());
            dbInvoice.setHandletime(new Date());//操作时间
            dbInvoice.setHandler(SystemUtil.DEFAULTUSERID);//操作人
            dbInvoice.setRedconfirm(response.getBillNo());
            invoiceMapper.saveAndFlush(dbInvoice);
            UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
            String content = StrUtil.format("【发票红冲】发票红字确认单：{},操作人:{}", response.getBillNo(), SystemUtil.DEFAULTUSERID);
            userLogService.writeLog(SystemUtil.UserLogType.INVOICE, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                    dbInvoice.getBookingid(), content, projectId);

            //红冲流程新建发票记录，等红冲流程结束再提交发票开具请求
            List<Invoice> dbList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(bookingId, projectId);
            Invoice newInvoice = new Invoice();
            newInvoice.setProjectid(projectId);
            newInvoice.setBookingid(bookingId);
            newInvoice.setOrderno(bookingId + dbList.size());//标识唯一订单号发送给诺诺发票
            //发票抬头信息
            newInvoice.setType(1);//默认只有增值税电子普票
            newInvoice.setTitle(postReq.getTitle());
            newInvoice.setTitletype(postReq.getTitletype());
            newInvoice.setTaxno(postReq.getTaxno());
            newInvoice.setBankname(postReq.getBankname());
            newInvoice.setBankno(postReq.getBankno());
            newInvoice.setAddress(postReq.getAddress());
            newInvoice.setTel(postReq.getTel());
            newInvoice.setEmail(postReq.getEmail());
            newInvoice.setPhone(postReq.getPhone());
            List<StdInvoiceCreateRequest.InvoiceInfoDetail> newDetails = new ArrayList<>();
            //发票明细
            newDetails.addAll(ContentCacheTool.transInvoiceInfoDetail(projectId, dbInvoice.getBookingid(), false));//订单信息转换发票明细
            newInvoice.setMemo(JSON.toJSONString(newDetails));//发票明细
            newInvoice.setCreatetime(LocalDateTime.now());//发票申请时间
            newInvoice.setUid(userid);//用户
            newInvoice.setIstatus(StatusUtil.InvoiceStatusEnum.INIT.getCode());
            invoiceMapper.saveAndFlush(newInvoice);


        }
    }

    /**
     * 电子发票重发
     *
     * @param req
     * @param projectId
     */
    @Override
    public void invoicePush(AppInvoicePushReq req, String projectId) throws DefinedException {
        //如果是本地发票类型直接返回
        if (checkLocalInvoiceType(projectId)) {
            //tudo 本地收集不支持重发
            return;
        }
        List<Invoice> invoices = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(req.getBookingid(), projectId);
        if (CollectionUtil.isNotEmpty(invoices)) {
            Invoice dbInvoice = invoices.get(0);//本地发票记录d
            if (StringUtils.isBlank(dbInvoice.getInvoiceno())) {//没有发票号码 未开具成功
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("电子发票未开具成功，请等待审核"));
            }
            if (StatusUtil.InvoiceStatusEnum.INIT.getCode().equals(dbInvoice.getIstatus())) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("电子发票未开具成功，请等待审核"));
            }
            InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
            StdInvoicePushRequest request = new StdInvoicePushRequest();
            request.setProjectId(projectId);
            if (StringUtils.isNotBlank(req.getEmail())) {
                request.setEmail(req.getEmail());//接收发票邮箱
            }
            if (StringUtils.isNotBlank(req.getPhone())) {
                request.setTel(req.getPhone());//接收发票手机号码
            }
            List<StdInvoicePushRequest.InvoicePushList> pushInfoList = new ArrayList<>();
            StdInvoicePushRequest.InvoicePushList pushInfo = new StdInvoicePushRequest.InvoicePushList();
            pushInfo.setInvoiceid(dbInvoice.getInvoiceid());//发票流水号
            pushInfo.setInvoicecode(dbInvoice.getInvoicecode());//发票代码
            pushInfo.setInvoiceno(dbInvoice.getInvoiceno());//发票号码
            pushInfoList.add(pushInfo);
            //todo 判断是否有红冲发票信息，发送红冲发票
            request.setInfos(pushInfoList);
            StdInvoicePushResponse response = invoiceVendorHandler.InvoicePush(request);
            if (!response.getStd_flag()) {//重发失败
                //String msg = StrUtil.format("发票推送失败，发票流水号：{}", JSON.toJSONString(Joiner.on(",").join(response.getFailPush())));
                //throw new CustomException(ResultJson.failure(ResultCode.SYSERR).msg(response.getStd_sub_msg()));
                throw new DefinedException("发票重发失败，" + response.getStd_sub_msg());
            }
        }
    }

    /**
     * @param req
     * @return 订单发票查询
     */
    @Override
    public AppOrderInvoiceResult invoiceQuery(AppOrderOpReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        AppOrderInvoiceResult res = new AppOrderInvoiceResult();
        String status = null;
        String ptype = null;
        String productcode = null;
        BigDecimal amount = BigDecimal.ZERO;
        Integer anz = 0;
        String creatTime;
        if (!req.getOrderid().startsWith("Y")) { //正常主订单订单
            Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(req.getOrderid());
            if (rs == null) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单记录不存在"));
            }
            status = rs.getMainstatus();
            ptype = rs.getPtype();
            productcode = rs.getProduct();
            amount = rs.getAmount();
            anz = rs.getAnz();
            creatTime = rs.getCreatedate().toString().replaceAll("T", " ");

            if (!InvoiceUtil.checkProdTypeCanInvoice(projectId, ptype)) { //检查订单类型有无配置
                res.setLcreate(false);
                return res;
            }
        } else {//预约订单 默认走配置
            Act_rs rs = actrsMapper.findAct_rsByBookingid(req.getOrderid());
            status = rs.getStatus();
            ptype = ProdType.ACTGROUP.val();
            productcode = rs.getSitecode();
            amount = rs.getAmount();
            anz = 1;//默认数量
            creatTime = rs.getCreatetime().toString().replaceAll("T", " ");
        }
        if (!StatusUtil.BookingRsStatus.FINISH.equals(status)) {//订单不是完结状态不可开票
            res.setLcreate(false);
            return res;
        }

        //todo 查询申请修改订单次数
        List<Invoice> dbInvoiceList = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(req.getOrderid(), projectId);
        if (CollectionUtil.isNotEmpty(dbInvoiceList)) {
            List<Invoice> redInvoices = dbInvoiceList.stream().filter(I -> StatusUtil.InvoiceStatusEnum.REDED.getCode().equals(I.getIstatus())).collect(Collectors.toList());
            if (redInvoices.size() <= SystemUtil.invoiceUpdNum) { //todo 红冲修改次数大于配置数量 不可修改
                res.setLmodify(true);
            }
            Invoice dbInvoice = dbInvoiceList.get(0);
            res.setOrderid(dbInvoice.getBookingid());
            res.setStatus(dbInvoice.getIstatus());
            res.setPdfUrl(dbInvoice.getPfdurl());
            res.setJpgUrl(dbInvoice.getJpgurl());
            res.setQrcode(dbInvoice.getQrurl());
            //发票详情
            AppOrderInvoiceDetail invoiceDetail = new AppOrderInvoiceDetail();
            //Usertitle usertitle = usertitleMapper.findByTitletypeAndTitleAndProjectid(dbInvoice.getTitletype(), dbInvoice.getTitle(), dbInvoice.getProjectid());
            //if (usertitle != null) {
            //    invoiceDetail.setSqlid((usertitle.getSqlid()));
            //}
            //todo 如果红冲未新建新的正票记录，显示的是已红冲的正票记录
            invoiceDetail.setSqlid(dbInvoice.getSqlid());
            invoiceDetail.setTitle(dbInvoice.getTitle());
            invoiceDetail.setTitletype(dbInvoice.getTitletype());
            invoiceDetail.setType(dbInvoice.getType());
            //todo 手动确认开票的没有发票流水号信息
            invoiceDetail.setInvoiceId(dbInvoice.getInvoiceid());
            if (StringUtils.isNotBlank(dbInvoice.getEmail())) {
                invoiceDetail.setEmail(dbInvoice.getEmail());//接收方式 电子邮箱
            }
            if (StringUtils.isNotBlank(dbInvoice.getPhone())) {
                //截取手机邮箱
                invoiceDetail.setPhone(dbInvoice.getPhone());
            }
            invoiceDetail.setCreatTime(dbInvoice.getCreatetime().toString().replaceAll("T", " "));
            invoiceDetail.setTaxno(dbInvoice.getTaxno());
            invoiceDetail.setBankname(dbInvoice.getBankname());
            invoiceDetail.setBankno(dbInvoice.getBankno());
            invoiceDetail.setAddress(dbInvoice.getAddress());
            invoiceDetail.setTel(dbInvoice.getTel());
            res.setDetails(invoiceDetail);
            //填充订单信息
            res.setTotalAmount(amount);
            res.setNum(anz);
            res.setGroupCode(ContentCacheTool.getProductGroupInfo(ptype, productcode, projectId, true));
            res.setGroupDesc(ContentCacheTool.getProductGroupInfo(ptype, productcode, projectId, false));
            res.setProductDesc(ContentCacheTool.getProductDesc(ptype, productcode, projectId));
            res.setCreatedatetime(creatTime);
            res.setProductpic(ContentCacheTool.getProductOrderShowPic(ptype, productcode, projectId, lmobile));
            res.setProductType(ptype);
            return res;
        } else {
            //没有发票记录查询是否可以开票
            res.setLcreate(true);

        }
        return res;
    }

    /**
     * @return 用户待开发票订单列表
     */
    @Override
    public AppUserInvoiceOrderListRes unInvoiceOrderListQuery() {
        int currentPage = 0;
        int pageSize = 100;
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        List<String> pTypes = new ArrayList<>();
        //获取发票配置 查看支持发票类型过滤订单
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        ConfYaml confYaml = sysConfCache.getConfYaml(projectId);
        if (confYaml != null) { //如果没配置直接返回空数据
            Conf_Invoicenode invoicenode = confYaml.getInvoice();
            if (invoicenode != null) {
                pTypes = Arrays.asList(invoicenode.getTypes().split(","));
            } else {
                return new AppUserInvoiceOrderListRes();
            }
        } else {
            return new AppUserInvoiceOrderListRes();
        }

        List<Invoice> invoiceList = invoiceMapper.findMyInvoiceOrderList(userid, projectId);
        List<String> bookingIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(invoiceList)) {
            bookingIdList = invoiceList.stream().map(Invoice::getBookingid).distinct().collect(Collectors.toList());
        }

        List<String> finalBookingIdList = bookingIdList;
        List<String> finalTypes = pTypes;
        //todo  预约订单查询合并
        Page<Booking_rs> queryPage = bookingrsMapper.findAll((Root<Booking_rs> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            list.add(cb.equal(root.get("uid"), userid));
            list.add(cb.equal(root.get("projectid"), projectId));
            list.add(cb.equal(root.get("mainstatus"), StatusUtil.BookingRsStatus.FINISH)); //已完结订单才可申请发票
            list.add(cb.greaterThan(root.get("amount"), BigDecimal.ZERO));//订单金额>0
            if (CollectionUtil.isNotEmpty(finalTypes)) { //in条件查询分组代码产品类型
                CriteriaBuilder.In<Object> pTypeIn = cb.in(root.get("ptype"));
                for (String pType : finalTypes) {
                    pTypeIn.value(pType);
                }
                list.add(pTypeIn);
            }
            if (CollectionUtil.isNotEmpty(finalBookingIdList)) { //in条件查询分组代码
                CriteriaBuilder.In<Object> bookingidIn = cb.in(root.get("bookingid"));
                for (String bookingid : finalBookingIdList) {
                    bookingidIn.value(bookingid);
                }
                list.add(bookingidIn.not());
            }
            Predicate[] p = new Predicate[list.size()];
            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("createdate")));
            query.where(cb.and(list.toArray(p))).orderBy(orderList);
            return query.getRestriction();
        }, org.springframework.data.domain.PageRequest.of(currentPage, pageSize));
        List<AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode> ls = queryPage.getContent().stream().map(
                row -> {
                    AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode node = new AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode();
                    node.setType(row.getPtype());
                    node.setProductCode(row.getProduct());
                    String iStatus = invoiceList.stream().filter(I -> I.getBookingid().equals(row.getBookingid())).map(Invoice::getIstatus).collect(Collectors.joining());
                    if (StringUtils.isNotBlank(iStatus)) {//发票状态
                        node.setIstatus(iStatus);
                    }
                    node.setOrderid(row.getBookingid());
                    node.setBooker(row.getBookername());
                    node.setGroupid(ContentCacheTool.getProductGroupInfo(row.getPtype(), row.getProduct(), projectId, false));
                    node.setNum(row.getAnz());
                    node.setDescription(ContentCacheTool.getProductDesc(row.getPtype(), row.getProduct(), projectId));
                    node.setPrice(row.getAmount().doubleValue());
                    node.setUsedateinfo(getUseDateInfo(row));
                    node.setStatus(row.getMainstatus());
                    node.setOrderdatetime(row.getCreatedate().toString().replaceAll("T", " "));
                    node.setTitlepicurl(ContentCacheTool.getProductOrderShowPic(row.getPtype(), row.getProduct(), projectId, lmobile));
                    return node;
                }
        ).collect(Collectors.toList());

        Page<Act_rs> queryActRsPage = actrsMapper.findAll((Root<Act_rs> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            list.add(cb.equal(root.get("uid"), userid));
            list.add(cb.equal(root.get("projectid"), projectId));
            list.add(cb.equal(root.get("status"), StatusUtil.BookingRsStatus.FINISH)); //已完结订单才可申请发票
            list.add(cb.greaterThan(root.get("amount"), BigDecimal.ZERO));//订单金额>0
            if (CollectionUtil.isNotEmpty(finalBookingIdList)) { //in条件查询分组代码
                CriteriaBuilder.In<Object> bookingidIn = cb.in(root.get("bookingid"));
                for (String bookingid : finalBookingIdList) {
                    bookingidIn.value(bookingid);
                }
                list.add(bookingidIn.not());
            }
            Predicate[] p = new Predicate[list.size()];
            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("createtime")));
            query.where(cb.and(list.toArray(p))).orderBy(orderList);
            return query.getRestriction();
        }, org.springframework.data.domain.PageRequest.of(currentPage, pageSize));
        List<AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode> actRsList = queryActRsPage.getContent().stream().map(
                row -> {
                    AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode node = new AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode();
                    node.setType(ProdType.ACTGROUP.val());//预约先默认餐饮
                    node.setProductCode(row.getSitecode());
                    String iStatus = invoiceList.stream().filter(I -> I.getBookingid().equals(row.getBookingid())).map(Invoice::getIstatus).collect(Collectors.joining());
                    if (StringUtils.isNotBlank(iStatus)) {//发票状态
                        node.setIstatus(iStatus);
                    }
                    node.setOrderid(row.getBookingid());
                    node.setBooker(row.getBookername());
                    node.setGroupid(ContentCacheTool.getProductGroupInfo(ProdType.ACTGROUP.val(), row.getSitecode(), projectId, false));
                    node.setNum(1);//预约默认以
                    node.setDescription(ContentCacheTool.getProductDesc(ProdType.ACTGROUP.val(), row.getSitecode(), projectId));
                    node.setPrice(row.getAmount().doubleValue());
                    node.setUsedateinfo(row.getUsedate().toString().replaceAll("T", " "));
                    node.setStatus(row.getStatus());
                    node.setOrderdatetime(row.getCreatetime().toString().replaceAll("T", " "));
                    node.setTitlepicurl(ContentCacheTool.getProductOrderShowPic(ProdType.ACTGROUP.val(), row.getSitecode(), projectId, lmobile));
                    return node;
                }
        ).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(actRsList)) {
            ls.addAll(actRsList);//合并list
            ls.sort(Comparator.comparing(AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode::getOrderdatetime));//按照下单时间排序
        }
        AppUserInvoiceOrderListRes res = new AppUserInvoiceOrderListRes();
        res.setRecords(ls);
        res.directFillPageCount((int) queryPage.getTotalElements(), queryPage.getPageable().getPageSize(),
                queryPage.getPageable().getPageNumber() + 1, queryPage.getTotalPages());
        return res;
    }

    /**
     * @return 用户已开发票订单列表
     */
    @Override
    public AppUserInvoiceOrderListRes invoiceOrderListQuery() {
        int currentPage = 0;
        int pageSize = 100;
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());
        List<Invoice> invoiceList = invoiceMapper.findInvoiceOrderList(userid, projectId);
        ////todo 查询发票历史
        //List<Invoice_his> invoiceHisList = invoiceHisMapping.findInvoiceHisOrderList(userid, projectId);
        List<String> bookingIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(invoiceList)) {
            bookingIdList = invoiceList.stream().map(Invoice::getBookingid).distinct().collect(Collectors.toList());
        }

        List<String> finalBookingIdList = bookingIdList;
        Page<Booking_rs> queryPage = bookingrsMapper.findAll((Root<Booking_rs> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            list.add(cb.equal(root.get("uid"), userid));
            list.add(cb.equal(root.get("projectid"), projectId));
            CriteriaBuilder.In<Object> bookingidIn = cb.in(root.get("bookingid"));
            if (CollectionUtil.isNotEmpty(finalBookingIdList)) { //in条件查询分组代码
                for (String bookingid : finalBookingIdList) {
                    bookingidIn.value(bookingid);
                }
                list.add(cb.and(bookingidIn));
            }
            //list.add(cb.equal(root.get("lhide"), false));
            Predicate[] p = new Predicate[list.size()];
            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("createdate")));
            query.where(cb.and(list.toArray(p))).orderBy(orderList);
            return query.getRestriction();
        }, org.springframework.data.domain.PageRequest.of(currentPage, pageSize));
        List<AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode> ls = queryPage.getContent().stream().map(
                row -> {
                    AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode node = new AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode();
                    node.setType(row.getPtype());
                    node.setProductCode(row.getProduct());
                    String iStatus = invoiceList.stream().filter(I -> I.getBookingid().equals(row.getBookingid())).map(Invoice::getIstatus).collect(Collectors.joining());
                    if (StringUtils.isNotBlank(iStatus)) {//发票状态
                        node.setIstatus(iStatus);
                    }
                    node.setOrderid(row.getBookingid());
                    node.setBooker(row.getBookername());
                    node.setGroupid(ContentCacheTool.getProductGroupInfo(row.getPtype(), row.getProduct(), projectId, false));
                    node.setNum(row.getAnz());
                    node.setDescription(ContentCacheTool.getProductDesc(row.getPtype(), row.getProduct(), projectId));
                    node.setPrice(row.getAmount().doubleValue());
                    node.setUsedateinfo(getUseDateInfo(row));
                    node.setStatus(row.getMainstatus());
                    node.setOrderdatetime(row.getCreatedate().toString().replaceAll("T", " "));
                    node.setTitlepicurl(ContentCacheTool.getProductOrderShowPic(row.getPtype(), row.getProduct(), projectId, lmobile));
                    return node;
                }
        ).collect(Collectors.toList());
        //todo 预约订单查询合
        Page<Act_rs> queryActRsPage = actrsMapper.findAll((Root<Act_rs> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            list.add(cb.equal(root.get("uid"), userid));
            list.add(cb.equal(root.get("projectid"), projectId));
            if (CollectionUtil.isNotEmpty(finalBookingIdList)) { //in条件查询分组代码
                CriteriaBuilder.In<Object> bookingidIn = cb.in(root.get("bookingid"));
                for (String bookingid : finalBookingIdList) {
                    bookingidIn.value(bookingid);
                }
                list.add(cb.and(bookingidIn));
            }
            Predicate[] p = new Predicate[list.size()];
            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("createtime")));
            query.where(cb.and(list.toArray(p))).orderBy(orderList);
            return query.getRestriction();
        }, org.springframework.data.domain.PageRequest.of(currentPage, pageSize));
        List<AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode> actRsList = queryActRsPage.getContent().stream().map(
                row -> {
                    AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode node = new AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode();
                    node.setType(ProdType.ACTGROUP.val());//预约先默认餐饮
                    node.setProductCode(row.getSitecode());
                    String iStatus = invoiceList.stream().filter(I -> I.getBookingid().equals(row.getBookingid())).map(Invoice::getIstatus).collect(Collectors.joining());
                    if (StringUtils.isNotBlank(iStatus)) {//发票状态
                        node.setIstatus(iStatus);
                    }
                    node.setOrderid(row.getBookingid());
                    node.setBooker(row.getBookername());
                    node.setGroupid(ContentCacheTool.getProductGroupInfo(ProdType.ACTGROUP.val(), row.getSitecode(), projectId, false));
                    node.setNum(1);//预约默认以
                    node.setDescription(ContentCacheTool.getProductDesc(ProdType.ACTGROUP.val(), row.getSitecode(), projectId));
                    node.setPrice(row.getAmount().doubleValue());
                    node.setUsedateinfo(row.getUsedate().toString().replaceAll("T", " "));
                    node.setStatus(row.getStatus());
                    node.setOrderdatetime(row.getCreatetime().toString().replaceAll("T", " "));
                    node.setTitlepicurl(ContentCacheTool.getProductOrderShowPic(ProdType.ACTGROUP.val(), row.getSitecode(), projectId, lmobile));
                    return node;
                }
        ).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(actRsList)) {
            ls.addAll(actRsList);
            ls.sort(Comparator.comparing(AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode::getOrderdatetime));//下单时间排序
        }
        AppUserInvoiceOrderListRes res = new AppUserInvoiceOrderListRes();
        res.setRecords(ls);
        res.directFillPageCount((int) queryPage.getTotalElements(), queryPage.getPageable().getPageSize(),
                queryPage.getPageable().getPageNumber() + 1, queryPage.getTotalPages());
        return res;
    }

    /**
     * @param req
     * @return 获取用户优惠票详情
     */
    @Override
    public AppUsercouponRes getUserCoupon(AppUsercouponQueryReq req) {
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        Usercoupon usercoupon = null;
        if (StringUtils.isNotBlank(req.getCouponid())) {
            usercoupon = userCouponMapping.findByCouponid(req.getCouponid());
        } else if (StringUtils.isNotBlank(req.getCouponcode())) {
            usercoupon = userCouponMapping.findByCouponcodeAndAppuserid(req.getCouponcode(), userid);
        }
        if (usercoupon == null || !userid.equals(usercoupon.getAppuserid())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
        }
        AppUsercouponRes res = new AppUsercouponRes();
        BeanUtils.copyPropertiesIgnoreNull(usercoupon, res);
        CouponCache couponCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPON);
        Coupon coupon = couponCache.getRecord(usercoupon.getProjectid(), usercoupon.getCouponcode());
        if (coupon != null && coupon.getThreshold().compareTo(BigDecimal.ZERO) > 0) { //有门槛使用条件
            res.setThreshold(coupon.getThreshold());
        }
        CoupongroupCache coupongroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP);
        Coupongroup coupongroup = coupongroupCache.getRecord(usercoupon.getProjectid(), usercoupon.getGroupid());
        //显示二维码
        // 以优惠券自身当时领取的大组使用时间为准
        // res.setStartdate(coupongroup.getStartdate()); //以大组为准
        // res.setEnddate(coupongroup.getEnddate());
        res.setGroupdesc(coupongroup.getDescription());
        res.setQrcode(SysFuncLibTool.generateEncodeQr(StrUtil.EMPTY, usercoupon.getCouponid(), projectId));//生成二维码  SysFuncLibTool.generateCouponEncodeQr(usercoupon.getCouponid(), projectId)
        if (coupongroup != null) {
            res.setRichtext(coupongroup.getRichtext());
        }
        if (res.getCheckdate().before(SystemUtil.EMPTY_DATETIME)) {
            res.setCheckdate(null);
        }
        //如果是折扣取百分比
        if (res.getUnit() == 1) {
            BigDecimal newPrice = res.getPrice().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            newPrice = newPrice.multiply(new BigDecimal(10));
            //判断小数点是否为0
            res.setPrice(newPrice.stripTrailingZeros());
        }
        return res;
    }

    /**
     * @return 获取用户已领取优惠票列表
     */
    @Override
    public List<AppUsercouponItemListNode> getUserCouponList() {
        UserCouponMapping userCouponMapping = SpringUtil.getBean(UserCouponMapping.class);
        List<Usercoupon> usercoupons = userCouponMapping.findAllByAppuseridAndProjectid(WebAppGlobalContext.getCurrentAppUserId(),
                WebAppGlobalContext.getCurrentAppProjectId());
        Date now = new Date();
        usercoupons.sort(Comparator.comparing(Usercoupon::getLcheck).thenComparing(Usercoupon::getCheckdate));//按核销状态 和时间
        //todo 检查优惠券关闭状态 如果关闭的优惠券过滤 或者夜审迁移过期优惠券
        CoupongroupCache coupongroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP);
        List<AppUsercouponItemListNode> listNodes = usercoupons.stream().filter(r -> {
            Coupongroup group = coupongroupCache.getRecord(r.getProjectid(), r.getGroupid());
            if (group != null && !group.getLoffline() && now.before(DateUtil.endOfDay(r.getEnddate()))) { //过滤线下优惠券类型 没过期
                return true;
            }
            return false;
        }).map(r -> {
                    Coupongroup group = coupongroupCache.getRecord(r.getProjectid(), r.getGroupid());
                    AppUsercouponItemListNode node = new AppUsercouponItemListNode();
                    BeanUtil.copyProperties(r, node);
                    //node.setStartdate(group.getStartdate()); //防止更新 取大组设置的最新时间
                    //node.setEnddate(group.getEnddate());
                    if (DateUtil.compare(now, DateUtil.endOfDay(node.getEnddate())) > 0) { //判断是否过期
                        node.setLexpire(true);
                    }
                    //如果是折扣取百分比
                    if (node.getUnit() == 1) {
                        BigDecimal newPrice = node.getPrice().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                        newPrice = newPrice.multiply(new BigDecimal(10));
                        node.setPrice(newPrice.stripTrailingZeros());
                    }
                    return node;
                }
        ).collect(Collectors.toList());
        //未核销 未过期排序
        listNodes.sort(Comparator.comparing(AppUsercouponItemListNode::getLexpire).thenComparing(AppUsercouponItemListNode::getLexpire));
        return listNodes;
    }

    /**
     * @param req
     * @return 领取优惠券
     */
    @Override
    public AppUsercouonGetRes getCoupon(AppUsercouponReq req) {
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        AppUsercouonGetRes res = new AppUsercouonGetRes();
        String couponCode = req.getCouponcode();
        Date now = new Date();
        if (StringUtils.isBlank(couponCode)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("代码不能为空")); //aaa分割返回前端
        }
        App_user app_user = appuserMapper.findApp_usersByUserid(userid);
        if (app_user == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("用户不存在"));
        }
        CouponCache couponCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPON);
        Coupon coupon = couponCache.getRecord(projectId, couponCode);
        if (coupon == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("记录不存在"));
        } else if (!coupon.getLsell()) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(coupon.getDescription() + "已领完"));
        }
        String description = coupon.getDescription();
        CoupongroupCache coupongroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP);
        Coupongroup coupongroup = coupongroupCache.getRecord(projectId, coupon.getGroupid());
        if (!coupongroup.getLsell()) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(description + "已领完"));
        }
        Integer limitTotalNum = coupongroup.getLaccountnum();//限制用户领取总数
        Integer limitDailyNum = coupongroup.getLdailynum();//限制用户每日领取


        if (DateUtil.compare(now, DateUtil.beginOfDay(coupongroup.getStartget())) < 0 || DateUtil.compare(now, DateUtil.endOfDay(coupongroup.getEndget())) > 0) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期范围内领取"));
            //} else if (StringUtils.isNotBlank(coupongroup.getStarttime())) { //如果设置领取时段 判断当前时间时间是否在时段范围内
            //    //todo 多时段判断
            //    if (StringUtils.isNotBlank(coupongroup.getEndtime()) &&
            //            !checkCouponGetTime(now, coupongroup.getStarttime(), coupongroup.getEndtime())) {
            //        throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期时段范围内领取"));
            //    } else {
            //        if (!checkCouponGetTime(now, coupongroup.getStarttime(), "23:59")) {
            //            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期时段范围内领取"));
            //        }
            //    }
            //}
        } else if (StringUtils.isNotBlank(coupongroup.getTimelimit())) { //如果设置领取时段 判断当前时间时间是否在时段范围内
            //判断有无设置时段截止时间
            if (!checkCouponLimitTimGet(now, coupongroup.getTimelimit())) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期时段范围内领取"));
            }

        }
        //缓存用户点击redis 防止同一用户重复点击
        if (coreCache.IsDoubleClickProduct(coupon.getCode(), userid, projectId)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("您的操作过快，请刷新重试"));
        }

        // 查看缓存库存
        Integer avl = coreCache.getCouponAvlCache(couponCode, projectId);
        if (avl < 1) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(description + "已领完"));
        }

        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RLock couponAvlLock = redissonClient.getLock(RedisKey.COUPONLOCK + couponCode);//防止并发扣库存
        boolean lock = false;
        try {
            lock = couponAvlLock.tryLock(5, TimeUnit.SECONDS);
            if (lock) {
                //检查预扣内存
                coreCache.checkCouponAvlCache(coupon.getCode(), projectId);
                if (checkGroupLimitPrice(coupongroup)) { //查看限额
                    coreCache.releaseCouponAvlCache(coupon.getCode(), projectId);
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(description + "已领完"));
                }
                //检查限制领取数量  用领取日期区间判断  先不考虑时段 以领取日期开始0点到结束日期23点
                if (hasUsercouponOrOutLimit(coupongroup.getStartget(), coupongroup.getEndget(), userid, coupon.getGroupid(), limitTotalNum, limitDailyNum, projectId)) {
                    coreCache.releaseCouponAvlCache(coupon.getCode(), projectId);
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(description + "领取次数已达上限"));
                }
                //查询库存
                Rcoupon rcoupon = rcouponMapper.findByProjectidAndCouponcode(projectId, couponCode);
                if (rcoupon.getAvl() <= 0) {
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(description + "已领完"));
                } else {
                    //生成优惠券
                    Usercoupon usercoupon = new Usercoupon();
                    long timestamp = System.currentTimeMillis();
                    // 生成UUID作为乱序字符串
                    String randomString = UUID.randomUUID().toString().replace("-", "");
                    Integer unit = coupongroup.getType() != null ? coupongroup.getType() : 0;
                    usercoupon.setUnit(unit);
                    usercoupon.setCouponid((ProdType.DISCOUNT.val() + randomString).substring(0, 11));//优惠券码 唯一
                    usercoupon.setCouponcode(couponCode);
                    usercoupon.setDescription(coupon.getDescription());
                    usercoupon.setGroupid(coupon.getGroupid());
                    usercoupon.setAppuserid(userid);
                    usercoupon.setProjectid(projectId);
                    usercoupon.setPrice(coupon.getPrice());
                    if (CalculateDate.beforeEqual(coupongroup.getStartdate(), CalculateDate.NULLDATE)) {//数据库有时候出现1899-12-31 差几分钟
                        usercoupon.setStartdate(DateUtil.beginOfDay(new Date()));
                        Date enddate = DateUtil.endOfDay(CalculateDate.reckonDay(usercoupon.getStartdate(), 5, coupongroup.getExpireday()));
                        usercoupon.setEnddate(CalculateDate.reckonDay(enddate, Calendar.MINUTE, -1)); // endofday 是第二天的00:00:00 所以需要手动减少一分钟
                    } else {
                        usercoupon.setStartdate(DateUtil.beginOfDay(coupongroup.getStartdate()));
                        Date enddate = DateUtil.endOfDay(coupongroup.getEnddate());
                        usercoupon.setEnddate(CalculateDate.reckonDay(enddate, Calendar.MINUTE, -1)); // endofday 是第二天的00:00:00 所以需要手动减少一分钟
                    }

                    usercoupon.setCreatetime(new Date());
                    userCouponMapping.saveAndFlush(usercoupon);
                    //优惠券库存变更
                    rcoupon.setAvl(rcoupon.getAvl() - 1);
                    rcoupon.setPickup(rcoupon.getPickup() + 1);
                    rcouponMapper.saveAndFlush(rcoupon);
                    //coreCache.updCouponAvlCache(couponCode, projectId, rcoupon.getAvl()); //更新缓存
                    coreCache.updCouponGroupTodayTotal(coupon.getGroupid(), projectId, coupon.getPrice()); //大组领取更新缓存
                    res.setCouponid(usercoupon.getCouponid());
                    res.setPrice(coupon.getPrice());
                    res.setCouponcode(couponCode);

                    //判断优惠券代码是否是火把节couponcode ,点亮火把节头像
              /*      if ("LOGO".equals(couponCode)) {
                        log.info("领取勋章");
                        app_user.setLogourl("https://ynossfile.lzdjq.com/006/logo.png");
                        appuserMapper.saveAndFlush(app_user);
                        RedisTemplate redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
                        String peopleNumStr = (String) redisTemplate.opsForValue().get(RedisKey.auto_increase_people_num);
                        if (peopleNumStr != null) {
                            int peopleNum = Integer.parseInt(peopleNumStr);
                            peopleNum = peopleNum + 1;
                            redisTemplate.opsForValue().set(RedisKey.auto_increase_people_num, peopleNum + "");
                        }
                    }*/
                    return res;
                }
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("请退出重试"));
            }
        } catch (InterruptedException e) {
            coreCache.releaseCouponAvlCache(coupon.getCode(), projectId);
            e.printStackTrace();
        } finally {
            couponAvlLock.unlock();
        }


        return res;
    }

    /**
     * @return 线下优惠券详情，没有或者不满足条件则返回null
     */
    @Override
    public AppCouponVoucherRes getCouponVoucher() {
        AppCouponVoucherRes res = new AppCouponVoucherRes();
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        Usercoupon usercoupon = null;
        Date now = new Date();
        CoupongroupCache coupongroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP);
        List<Coupongroup> coupongroups = coupongroupCache.getDataListWithCondition(projectId, r -> r.getLsell() && r.getLoffline());// 获取线下优惠券大类
        if (CollectionUtil.isEmpty(coupongroups)) {
            return null;
        }
        Coupongroup offlineGroup = coupongroups.get(0);
        //重复无效点击直接返回空 避免查询库压力
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RMapCache<String, Integer> errorCache = redissonClient.getMapCache(RedisKey.COUPONERRORCACHE);
        Integer errorCount = errorCache.get(projectId + userid); //用用户ID和项目作为标识，
        if (errorCount != null && errorCount > 1) { //当天已核销优惠券不可领取，直接返回 缓存5分钟
            return null;
        }
        // //检查限制领取是否超出限制
        //boolean loutNum = hasUsercouponOrOutLimit(userid, offlineGroup.getCode(), offlineGroup.getLaccountnum(), offlineGroup.getLdailynum(), projectId);
        // if (loutNum) { //超出限额，查看最新一张线下优惠券，如果没核销显示，已核销不显示
        //
        // }
        //todo 可以缓存线下类型记录避免查库
        usercoupon = userCouponMapping.findLastCouponAppuseridAndGroupid(userid, offlineGroup.getCode(), projectId);//查看线下券类型记录
        if (usercoupon == null || (usercoupon.getLcheck() && usercoupon.getCheckdate().after(SystemUtil.EMPTY_DATETIME)
                && !DateUtil.isSameDay(now, usercoupon.getCheckdate()))) { ///没有记录 或者当前记录非当天核销 判断领取条件
            if (DateUtil.compare(now, DateUtil.beginOfDay(offlineGroup.getStartget())) < 0 || DateUtil.compare(now, DateUtil.endOfDay(offlineGroup.getEndget())) > 0) {
                //throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期范围内领取"));
                return null;
                //} else if (StringUtils.isNotBlank(offlineGroup.getStarttime())) { //如果设置领取时段 判断当前时间时间是否在时段范围内
            } else if (StringUtils.isNotBlank(offlineGroup.getTimelimit())) { //如果设置领取时段 判断当前时间时间是否在时段范围内
                //判断有无设置时段截止时间
                if (!checkCouponLimitTimGet(now, offlineGroup.getTimelimit())) {
                    return null;
                }
                ////todo 多时段判断
                //if (StringUtils.isNotBlank(offlineGroup.getEndtime()) &&
                //        !checkCouponGetTime(now, offlineGroup.getStarttime(), offlineGroup.getEndtime())) {
                //    return null;
                //    //throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期时段范围内领取"));
                //} else {
                //    if (!checkCouponGetTime(now, offlineGroup.getStarttime(), "23:59")) {
                //        return null;
                //        //throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请在领取日期时段范围内领取"));
                //    }
                //}
            }
            if (!checkGroupLimitPrice(offlineGroup)) { //检查是否限额创建
                int price = new CouponUtil().drawLottery();
                CouponCache couponCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPON);
                List<Coupon> dbCouponList = couponCache.getDataListWithCondition(projectId, r -> r.getLsell() &&
                        r.getGroupid().equals(offlineGroup.getCode()));// 获取线下优惠券大类 默认就一个
                if (CollectionUtil.isEmpty(dbCouponList)) {
                    return null;//如果线下优惠券全都关闭了不给领取
                }
                dbCouponList.sort(Comparator.comparing(Coupon::getPrice));
                if (checkCouponAvlCache(dbCouponList, price) == 0) {
                    return null;
                }
                BigDecimal couponPrice = new BigDecimal(price);//抽奖的金额，后面再次库存比较

                Coupon copyCoupon = new Coupon();
                BeanUtil.copyProperties(dbCouponList.get(0), copyCoupon);//复制2元券后续库存查看
                RLock couponAvlLock = redissonClient.getLock(RedisKey.COUPONLOCK + price);//防止并发扣库存
                boolean lock = false;
                try {
                    lock = couponAvlLock.tryLock(5, TimeUnit.SECONDS);
                    if (lock) {
                        //查询库存
                        Rcoupon rcoupon = checkCouponAvl(couponPrice, dbCouponList, copyCoupon, projectId);
                        //没有库存直接返回空
                        if (rcoupon == null || rcoupon.getAvl() <= 0) {
                            return null;
                        } else {
                            //生成线下优惠券
                            Usercoupon newUserCoupon = new Usercoupon();
                            int unit = offlineGroup.getType() != null ? offlineGroup.getType() : 0;
                            newUserCoupon.setUnit(unit);
                            // 生成UUID作为乱序字符串
                            String randomString = UUID.randomUUID().toString().replace("-", "");
                            newUserCoupon.setCouponid(ProdType.DISCOUNT.val() + "C" + (randomString).substring(0, 10));//优惠券码 区分线上
                            newUserCoupon.setCouponcode(copyCoupon.getCode());
                            newUserCoupon.setDescription(copyCoupon.getDescription());
                            newUserCoupon.setGroupid(copyCoupon.getGroupid());
                            newUserCoupon.setAppuserid(userid);
                            newUserCoupon.setProjectid(projectId);
                            newUserCoupon.setPrice(copyCoupon.getPrice());
                            newUserCoupon.setStartdate(offlineGroup.getStartdate());
                            newUserCoupon.setEnddate(offlineGroup.getEnddate());
                            newUserCoupon.setCreatetime(new Date());
                            userCouponMapping.saveAndFlush(newUserCoupon);
                            //优惠券库存变更
                            rcoupon.setAvl(rcoupon.getAvl() - 1);
                            rcoupon.setPickup(rcoupon.getPickup() + 1);
                            rcouponMapper.saveAndFlush(rcoupon);
                            coreCache.updCouponAvlCache(rcoupon.getCouponcode(), projectId, rcoupon.getAvl()); //更新缓存
                            coreCache.updCouponGroupTodayTotal(copyCoupon.getGroupid(), projectId, copyCoupon.getPrice()); //更新优惠券大组领取总金额缓存
                            usercoupon = newUserCoupon; //记录变更
                        }
                    } else {
                        throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("请退出重试"));
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } finally {
                    couponAvlLock.unlock();
                }
            } else {
                return null; //限额不允许领取
            }

        }
        if (usercoupon != null) {
            if (usercoupon.getLcheck()) {
                long expire = TimeUnit.MINUTES.toSeconds(5);
                errorCount = errorCount == null ? 1 : errorCount + 1;
                errorCache.put(projectId + userid, errorCount, expire, TimeUnit.SECONDS);//5分钟失效
                return null;//如果当天券已经领取并核销，不显示
            }
            BeanUtils.copyPropertiesIgnoreNull(usercoupon, res);
            res.setGroupdesc(offlineGroup.getDescription());
            res.setStartdate(offlineGroup.getStartdate());//以最新大组为准
            res.setEnddate(offlineGroup.getEnddate());
            //显示二维码
            res.setQrcode(SysFuncLibTool.generateCouponEncodeQr(usercoupon.getCouponid(), projectId));//生成二维码
            res.setRichtext(offlineGroup.getRichtext());
            if (res.getCheckdate().before(SystemUtil.EMPTY_DATETIME)) {
                res.setCheckdate(null);
            }

        }
        return res;
    }

    @Override
    public OSSUploadFileRes uploadOSSProfilePictureOpen(String projectId, MultipartFile file, String sign, long timestamp, String nonce) {
        if (file.isEmpty()) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("文件不能为空"));
        }
        // 2. 时间有效性校验（允许5分钟内的请求）
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - timestamp) > TimeUnit.MINUTES.toMillis(5)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("请求已过期"));
        }
        //校验签名
        // 3. 生成服务端签名
        String checkbody = nonce + timestamp;
        String localSign = SysFuncLibTool.encodeAesContent(checkbody, "");
        // 4. 签名比对
        if (!localSign.equals(sign)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("签名验证失败"));
        }
        // 5. 防重放攻击校验 判断缓存是否存在
        String replayKey = "AIUpload:nonce:" + nonce;
        RedisTemplate redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
        if (redisTemplate.hasKey(replayKey)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("该签名已经上传图片，已经失效"));
        }
        // 设置30分钟过期
        redisTemplate.opsForValue().set(replayKey, "1", 30, TimeUnit.MINUTES);
        String dirName = "aizhu/" + projectId;
        // OSSUploadFileRes ossUploadFileRes = ossService.upLoadFile(file, dirName, projectId, null);

        OSSUploadFileRes ossUploadFileRes = ossService.upLoadFile(file, dirName, "0", projectId, null);

        return ossUploadFileRes;
    }


    /**
     * @param now       当前日期
     * @param timeLimit 领取时段区间
     * @return 检查当前时间是否是在领取时段范围内，包含边界点
     */
    private boolean checkCouponLimitTimGet(Date now, String timeLimit) {
        boolean limit = false;
        if (StringUtils.isBlank(timeLimit)) {
            return true;
        }
        List<String> periods = Arrays.asList(timeLimit.split(","));
        for (String period : periods) {
            period = period.replaceAll(" ", "");//过滤空格
            if (period.contains("~")) {
                String[] getTime = period.split("~");
                String starTime = getTime[0];
                String endTime = getTime[1];
                //如果有符合时段的 直接跳出返回
                if (checkCouponGetTime(now, starTime, endTime)) {
                    limit = true;
                    break;
                }
            }
        }

        return limit;

    }

    /**
     * @param now       当前日期
     * @param starttime 起始时段
     * @param endtime   结束时段
     * @return 检查当前是否是否在时间范围内，包含边界点
     */
    private boolean checkCouponGetTime(Date now, String starttime, String endtime) {
        long currentTime = now.getTime();
        // 将当前日期和开始、结束时间拼接成完整的日期时间字符串
        String currentDateStr = DateUtil.today() + " ";//获取当天日期
        String startDateTimeStr = currentDateStr + starttime; //获取当天日期拼接
        String endDateTimeStr = currentDateStr + endtime;
        // 将日期时间字符串转换为对应的毫秒数
        long startDateTime = DateUtil.parse(startDateTimeStr).getTime();
        long endDateTime = DateUtil.parse(endDateTimeStr).getTime();
        if (currentTime >= startDateTime && currentTime <= endDateTime) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 检查缓存
     *
     * @param dbCouponList
     * @param price
     * @return
     */
    private int checkCouponAvlCache(List<Coupon> dbCouponList, int price) {
        List<Coupon> avlCouponList = new ArrayList<>();
        for (Coupon coupon : dbCouponList) {
            Integer avl = coreCache.getCouponAvlCache(coupon.getCode(), coupon.getProjectid());
            if (avl >= 1) {
                if (coupon.getPrice().compareTo(new BigDecimal(price)) == 0) {
                    return price;
                } else {
                    avlCouponList.add(coupon);
                }
            }

        }
        if (CollectionUtil.isNotEmpty(avlCouponList)) {
            avlCouponList.sort(Comparator.comparing(Coupon::getPrice));
            price = avlCouponList.get(0).getPrice().intValue();
            return price;
        }
        return 0;
    }

    /**
     * @param dbData
     * @return 检查领取日期范围内的每天优惠券记录金额有无超过限额
     */
    private boolean checkGroupLimitPrice(Coupongroup dbData) {
        if (dbData.getLimitprice().compareTo(BigDecimal.ZERO) > 0) {
            //BigDecimal total = userCouponMapping.countTodayTotalPriceByGroupid(dbData.getProjectid(), DateUtil.beginOfDay(new Date()),
            //        DateUtil.endOfDay(new Date()), dbData.getCode());
            //读取缓存优惠券大组领取总金额
            BigDecimal total = coreCache.getCouponGroupTodayTotal(dbData.getCode(), dbData.getProjectid());
            if (total == null) {
                total = new BigDecimal(BigInteger.ZERO);
            }
            return total.compareTo(dbData.getLimitprice()) >= 0;
        }
        return false;
    }

    /**
     * @param couponPrice
     * @param dbCouponList
     * @param dbCoupon
     * @param projectId
     * @return 检查 库存 ，更新dbCoupon
     */
    private Rcoupon checkCouponAvl(BigDecimal couponPrice, List<Coupon> dbCouponList, Coupon dbCoupon, String projectId) {
        Rcoupon rcoupon = null;
        List<Rcoupon> rcouponList = new ArrayList<>(); //有效库存记录
        dbCouponList.sort(Comparator.comparing(Coupon::getPrice));
        for (Coupon coupon : dbCouponList) {
            Rcoupon dbRcoupon = rcouponMapper.findByProjectidAndCouponcode(projectId, coupon.getCode());
            if (dbRcoupon.getAvl() > 1 && coupon.getPrice().compareTo(couponPrice) == 0) { //如果对应价格优惠券的库存 直接返回对应优惠券信息
                BeanUtil.copyProperties(coupon, dbCoupon); //更新dbCoupon
                return dbRcoupon;
            } else if (dbRcoupon.getAvl() > 1) { //其他有库存的数据添加
                rcouponList.add(dbRcoupon);
            }
        }
        if (CollectionUtil.isNotEmpty(rcouponList)) {
            //已经按价格低到高排序，取第一个记录
            List<Coupon> list = dbCouponList.stream().filter(coupon -> coupon.getCode().equals(rcouponList.get(0).getCouponcode()))
                    .collect(Collectors.toList());
            BeanUtil.copyProperties(list.get(0), dbCoupon);//更新dbCoupon
            return rcouponList.get(0);
        }
        return rcoupon;
    }


    /**
     * @param startDate     大组领取日期开始时间
     * @param endDate       大组领取日期结束时间
     * @param userid
     * @param groupid
     * @param limitTotalNum
     * @param limitDailyNum
     * @param projectId
     * @return 检查大类限制领取数量
     */
    private boolean hasUsercouponOrOutLimit(Date startDate, Date endDate, String userid, String groupid, Integer limitTotalNum, Integer limitDailyNum, String projectId) {
        //todo  检查用户领取
        //查看是否限制用户总领取数
        if (limitTotalNum != null && limitTotalNum > 0) {
            //检查领取总数和当天领取数量，判断当前用户领取数量是否超过限制
            //修改统计活动日期范围内的用户领取数量
            Long totalNum = userCouponMapping.countByAppuseridAndGroupidAndCreatetimeBetween(userid, groupid, DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate));
            if (totalNum == null || totalNum == 0) {
                return false;
            } else if (totalNum >= limitTotalNum) {
                return true;//用户领取总数超过限制
            }
        }
        //用户是否限制每天领取数
        if (limitDailyNum > 0) {
            //检查最新日期领取总数 判断是否当天领取数量超出限制
            //todo 检查当天用户领取缓存
            List<CouponUserData> couponUserData = userCouponMapping.findTodayNumByAppuseridAndGroupid(userid, groupid, projectId);
            //查询用户领取优惠券 按最新领取时间
            if (CollectionUtil.isNotEmpty(couponUserData) && DateUtil.isSameDay(new Date(), couponUserData.get(0).getCreatetime())
                    && couponUserData.get(0).getTotalNum() >= limitDailyNum) {
                return true;//每日领取超过限制
            }
        }
        return false;
    }

    /**
     * @param bookingId
     * @param projectId
     * @return 判断订单是否可以开发票。判断订单是否支付，订单是否开具发票
     */
    private boolean judgeInvoiceCreate(String bookingId, String projectId) {
        InvoiceMapper invoiceMapper = SpringUtil.getBean(InvoiceMapper.class);
        List<Invoice> dbInovices = invoiceMapper.findAllByBookingidAndProjectidOrderByCreatetimeDesc(bookingId, projectId);
        if (CollectionUtil.isEmpty(dbInovices)) { //没有记录直接开票
            return true;
        } else if (StatusUtil.InvoiceStatusEnum.INIT.getCode().equals(dbInovices.get(0).getIstatus())) {//如果存在记录 发票状态是未开
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单申请开具发票审核中，审核通过将发送发票信息到您提交的接收方式，请耐心等待"));
        } else if (StatusUtil.InvoiceStatusEnum.REFUSE.getCode().equals(dbInovices.get(0).getIstatus())) {//发票申请开具失败
            //如果是本地开发票状态，可以重新开票
            if (checkLocalInvoiceType(projectId)) {
                return true;
            }
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单申请开具发票失败"));
        } else if (StatusUtil.InvoiceStatusEnum.FINISH.getCode().equals(dbInovices.get(0).getIstatus())) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单申请开具发票已开具"));
        } else if (StatusUtil.InvoiceStatusEnum.REDED.getCode().equals(dbInovices.get(0).getIstatus())) {//红冲可重新开票 自动红冲第一条新数据是INIT初始状态
            return true;
        }
        return false;
    }


    private String getUseDateInfo(Booking_rs rs) {
        if (rs.getPtype().equals(ProdType.TICKET.val())) {
            Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                    getRecord(rs.getProjectid(), rs.getProduct());
            if (ticket != null) {
                Ticketgroup ticketgroup = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                        getRecord(rs.getProjectid(), ticket.getGroupid());
                if (ticketgroup != null) {
                    return StrUtil.format("{} {}-{}", CalculateDate.dateToString(rs.getArrdate()), ticketgroup.getOpeningtime(), ticketgroup.getClosetime());
                }
            }
        } else {
            return CalculateDate.dateFormat(rs.getArrdate(), DateStyle.YYYY_MM_DD_POINT.getValue())
                    + "-" + CalculateDate.dateFormat(rs.getDeptdate(), DateStyle.YYYY_MM_DD_POINT.getValue());
        }
        return "";
    }
}
