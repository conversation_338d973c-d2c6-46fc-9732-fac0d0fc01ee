package com.cw.service.statistic.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.config.websocket.PayCompleteWebsocket;
import com.cw.config.websocket.WebSocketServer;
import com.cw.entity.Dailystat;
import com.cw.entity.Hotel;
import com.cw.entity.Roomtype;
import com.cw.mapper.*;
import com.cw.pojo.dto.statistic.daily.*;
import com.cw.pojo.dto.statistic.req.Statistic_IndexData_Req;
import com.cw.pojo.dto.statistic.res.ProdAndOrderStatRes;
import com.cw.pojo.dto.statistic.res.Statistic_DailyRmTypePrice;
import com.cw.pojo.dto.statistic.res.Statistic_IndexData_Res;
import com.cw.pojo.dto.statistic.res.WaitAndAuditingOrderRes;
import com.cw.service.statistic.StatisticService;
import com.cw.utils.*;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.OnlinePayType;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.SocketException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2022/1/17 0017
 */
@Service
public class StatisticServiceImpl implements StatisticService {
    @Autowired
    BookingrsMapper bookingrsMapper;
    @Autowired
    RoomrsMapper room_rsMapper;
    @Autowired
    Ticket_rsMapper ticket_rsMapper;
    @Autowired
    PrepayMapper prepayMapper;

    @Autowired
    AuditingMapper auditingMapper;

    @Autowired
    AppuserMapper appuserMapper;

    @Autowired
    PassrsMapper passrsMapper;

    @Autowired
    RedissonClient redissonClient;


    @Autowired
    ContestentryMapper contestentryMapper;

    @Resource
    RedisTemplate redisTemplate;
    @Autowired
    private InvoiceMapper invoiceMapper;

    /**
     * @param projectId
     * @return 统计等待审核的创建订单数量 和审核退款的订单数量  线下支付未发起订单数量  首页统计数据 ,发票记录
     */
    @Override
    public WaitAndAuditingOrderRes getWaitAndAuditingOrder(String projectId) {
        Integer waitOrder = bookingrsMapper.countWaitOrder(projectId);
        Integer auditingOrder = auditingMapper.countAuditingOrder(projectId);
        Integer passOrder = passrsMapper.countPassOrder(projectId);
        long auditingContest = contestentryMapper.countUnCheckContestEntry(projectId);
        Date nowDay = DateUtil.beginOfDay(new Date());
        Integer spuRsOrder = bookingrsMapper.countSpuOrder(projectId, nowDay);


        Integer invoiceOrder = invoiceMapper.countInvoiceOrder(projectId);

        WaitAndAuditingOrderRes res = new WaitAndAuditingOrderRes();
        res.setWaitOrder(waitOrder);
        res.setAuditingOrder(auditingOrder);
        res.setPassOrder(passOrder);
        res.setAuditingContest((int) auditingContest);
        res.setSpuRsOrder(spuRsOrder);
        res.setInvoiceOrder(invoiceOrder);
        Statistic_IndexData_Req statReq = new Statistic_IndexData_Req();
        SystemUtil.StatisticType[] statisticTypes = new SystemUtil.StatisticType[]{
                SystemUtil.StatisticType.new_res, SystemUtil.StatisticType.new_user, SystemUtil.StatisticType.new_amount
        };
        statReq.setTypes(Joiner.on(",").join(statisticTypes));
        Statistic_IndexData_Res statRes = getIndexStatisticData(statReq, projectId);
        if (statRes != null) {
            res.setIndexData(statRes);
        }
        return res;
    }

    /**
     * 推送前端待审核订单和待审核退款订单数量
     *
     * @throws SocketException
     */
    @Override
    public void executeStatisticsOrder() throws SocketException {
        Map<String, WaitAndAuditingOrderRes> sendMap = new HashMap<>();
        //map<ip,projectid>
        Map<String, String> userMap = redisTemplate.opsForHash().entries(RedisKey.webUserIdMaP);
        if (CollectionUtil.isNotEmpty(userMap)) {
            String localIp = WebSocketUtil.getServerIp();
            for (Map.Entry<String, String> entry : userMap.entrySet()) {
                //判断标识是否是当前服务器ip  负载均衡可能不是一个websocket链接session
                if (entry.getKey().contains(localIp)) {
                    WaitAndAuditingOrderRes msg;
                    if (sendMap.containsKey(entry.getKey())) {
                        msg = sendMap.get(entry.getKey());
                    } else {
                        msg = getWaitAndAuditingOrder(entry.getValue());
                        sendMap.put(entry.getKey(), msg);
                    }
                    WebSocketServer webSocketServer = SpringUtil.getBean(WebSocketServer.class);
                    String remoteIp = entry.getKey().split("-")[0];//去除-
                    webSocketServer.sendInfo(remoteIp, JSON.toJSONString(msg));
                }
            }
        }
    }

    @Override
    public void executePayWebsocket() {
        try {
            Map<String, String> payInfoMap = redisTemplate.opsForHash().entries(RedisKey.webPayInfoMaP);
            if (CollectionUtil.isNotEmpty(payInfoMap)) {
                String localIp = WebSocketUtil.getServerIp();
                for (Map.Entry<String, String> entry : payInfoMap.entrySet()) {
                    //判断标识是否是当前服务器ip  负载均衡可能不是一个websocket链接session
                    if (entry.getKey().contains(localIp)) {
                        //发送消息
                        PayCompleteWebsocket socketServer = SpringUtil.getBean(PayCompleteWebsocket.class);
                        String remoteIp = entry.getKey().split("-")[0];//去除-
                        socketServer.sendPayInfo(remoteIp, "");
                    }
                }
                //System.out.println("定时30S发送websocket支付信息前端保活");
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }

    }

    /**
     * @param projectId
     * @return 统计统计景区商品销售
     */
    @Override
    public List<ProdAndOrderStatRes> statisticProdSell(String projectId, Date starDate, Date endDate) {
        List<ProdAndOrderStatRes> res = new ArrayList<>();
        res = bookingrsMapper.statisticsBookingRsProdSell(projectId, DateUtil.beginOfDay(starDate), DateUtil.endOfDay(endDate));
        return res;
    }

    /**
     * 定时统计每日营收数据
     *
     * @param projectId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Dailystat executeStatisticsDaily(String projectId, Date startTime, Date endTime) {
        Dailystat dailystat = null;
        //LocalDateTime startDate, endDate;
        //按照<project+date,Dailystat>节点分层
        if (startTime == null) {
            startTime = DateUtil.beginOfDay(new Date());
            endTime = new Date();
            //startDate = DateUtil.toLocalDateTime(DateUtil.beginOfDay(new Date()));
            //endDate = DateUtil.toLocalDateTime(new Date());
        } else {
            //startDate = DateUtil.toLocalDateTime(startTime);
            //endDate = DateUtil.toLocalDateTime(endTime);
            //todo
        }
        try {
            String key = projectId + "~" + DateUtil.format(startTime, "yyyy-MM-dd");
            RMapCache<String, String> dailyStatCacheMap = redissonClient.getMapCache(RedisKey.PRODSTATMAP);
            String dailyStatJson = dailyStatCacheMap.get(key);
            if (StringUtils.isBlank(dailyStatJson)) {
                dailystat = new Dailystat();
                dailystat.setProjectid(projectId);
                dailystat.setDatum(startTime);
            } else {
                dailystat = JSON.parseObject(dailyStatJson, Dailystat.class);
                if (dailystat == null) {
                    dailystat = new Dailystat();
                    dailystat.setProjectid(projectId);
                    dailystat.setDatum(startTime);
                }
            }
            //产品类型统计
            //List<ProdRevenueStat> revenueStatList = bookingrsMapper.statisticsBookingRsAmountByPtype(projectId, startDate, endDate);
            List<ProdRevenueStat> revenueStatList = bookingrsMapper.statisticsBookingRsNetIncomeByPtype(projectId, startTime, endTime);
            if (CollectionUtil.isNotEmpty(revenueStatList)) {
                for (ProdRevenueStat item : revenueStatList) {
                    if (ProdType.ROOM.val().equals(item.getPtype())) {
                        dailystat.setRoomres(item.getOrdernum());
                        dailystat.setRoomrev(item.getAmount());
                    } else if (ProdType.TICKET.val().equals(item.getPtype())) {
                        dailystat.setTicketres(item.getOrdernum());
                        dailystat.setTicketrev(item.getAmount());
                    } else if (ProdType.TAOCAN.val().equals(item.getPtype())) {
                        dailystat.setKitres(item.getOrdernum());
                        dailystat.setKitrev(item.getAmount());
                    } else if (ProdType.ITEMS.val().equals(item.getPtype())) {
                        dailystat.setGiftres(item.getOrdernum());
                        dailystat.setGiftrev(item.getAmount());
                    } else if (ProdType.WARES.val().equals(item.getPtype())) {
                        dailystat.setSpures(item.getOrdernum());
                        dailystat.setSpurev(item.getAmount());
                    }
                }
            }
            //取消订单统计
            //List<OrderCancelStat> cancelStatList = bookingrsMapper.statisticsCancel(projectId, startDate, endDate);
            List<OrderCancelStat> cancelStatList = bookingrsMapper.statisticsCancelByArrDate(projectId, startTime, endTime);
            if (CollectionUtil.isNotEmpty(cancelStatList)) {
                for (OrderCancelStat item : cancelStatList) {
                    dailystat.setCxl(item.getNum());
                    dailystat.setCxlamount(item.getAmount());
                }
            }
            //订单来源统计
            //List<AgentTypeOrderStat> agentTypeOrderStatList = bookingrsMapper.statisticsBookingRsAgentType(projectId, startDate, endDate);
            List<AgentTypeOrderStat> agentTypeOrderStatList = bookingrsMapper.statisticsBookingRsAgentTypeByArrDate(projectId, startTime, endTime);
            if (CollectionUtil.isNotEmpty(agentTypeOrderStatList)) {
                for (AgentTypeOrderStat item : agentTypeOrderStatList) {
                    if (AgentType.PCH5.name().equals(item.getType())) {
                        dailystat.setPcappres(item.getNum());
                    } else if (AgentType.WXAPP.name().equals(item.getType())) {
                        dailystat.setWxappres(dailystat.getWxappres() + item.getNum());
                    } else if (AgentType.MH5.name().equals(item.getType())) {
                        dailystat.setWxappres(dailystat.getWxappres() + item.getNum());
                    }
                }
            }
            //收入统计
            //todo 历史订单日期合并统计
            //List<PrepayStat> prepayStatList = bookingrsMapper.statisticsBookingRsAmountByPayment(projectId, startDate, endDate);
            List<PrepayStat> prepayStatList = bookingrsMapper.statisticsBookingRsNetIncomeByPayment(projectId, startTime, endTime);
            if (CollectionUtil.isNotEmpty(prepayStatList)) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (PrepayStat item : prepayStatList) {
                    if (OnlinePayType.TB.name().equals(item.getPayment())) {
                        dailystat.setAlirev(item.getAmount());
                    } else if (OnlinePayType.WX.name().equals(item.getPayment())) {
                        dailystat.setWxrev(item.getAmount());
                    } else if (OnlinePayType.YL.name().equals(item.getPayment())) {
                        dailystat.setYlrev(item.getAmount());
                    }
                    totalAmount = totalAmount.add(item.getAmount());
                }
                dailystat.setRevenue(totalAmount);
            }
            //当天新增用户数
            //long userAddNum = appuserMapper.countByProjectidAndRegdateBetween(projectId, startDate, endDate);//用户新增数 //后续可以分平台统计
            long userAddNum = appuserMapper.countByProjectidAndRegdateBetween(projectId, DateUtil.toLocalDateTime(startTime), DateUtil.toLocalDateTime(endTime));//用户新增数 //后续可以分平台统计
            dailystat.setNusernum((int) userAddNum);
            dailyStatCacheMap.put(key, JSON.toJSONString(dailystat), 24, TimeUnit.HOURS);//缓存统计数据

        } catch (Exception e) {
            e.printStackTrace();
        }

        return dailystat;
    }

    /**
     * @param req
     * @param projectId
     * @return 获取统计数据
     */
    @Override
    public Statistic_IndexData_Res getIndexStatisticData(Statistic_IndexData_Req req, String projectId) {
        Date nowDate = new Date();
        Date startTime = DateUtil.beginOfDay(nowDate);

        Statistic_IndexData_Res res = new Statistic_IndexData_Res();
        String type = req.getTypes();
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.new_user.name())) {
            res.setNew_user(calcNew_user(projectId, startTime, nowDate));
        }
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.new_res.name())) {
            res.setNew_res(calcNew_res(projectId, startTime, nowDate));
        }
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.new_amount.name())) {
            res.setNew_amount(calcNew_amount(projectId, startTime, nowDate));
        }
        //if (SystemUtil.StatisticType.all.name().equals(type) ||
        //        type.contains(SystemUtil.StatisticType.occupancy.name())) {
        //    res.setOccupancy(calcOccupancy(projectId));
        //}
        //if (SystemUtil.StatisticType.all.name().equals(type) ||
        //        type.contains(SystemUtil.StatisticType.passenger_flow.name())) {
        //    res.setPassenger_flow(calcPassenger_flow(projectId));
        //}
        //if (SystemUtil.StatisticType.all.name().equals(type) ||
        //        type.contains(SystemUtil.StatisticType.room_price.name())) {
        //    res.setRoom_price(calcRoom_price(projectId, startTime, CalculateDate.reckonDay(startTime, 5, 1)));
        //}
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.revenues_statistic.name())) {
            res.setRevenues_statistic(calcRevenues_statistic(projectId, req.getRevenues_statistic()));
        }
        //if (SystemUtil.StatisticType.all.name().equals(type) ||
        //        type.contains(SystemUtil.StatisticType.channel_res.name())) {
        //    res.setChannel_res(calcChannel_res(projectId));
        //}
        //if (SystemUtil.StatisticType.all.name().equals(type) ||
        //        type.contains(SystemUtil.StatisticType.channel_price.name())) {
        //    res.setChannel_price(calcChannel_price(projectId));
        //}
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.res_top10.name())) {
            res.setRes_top10(calcRes_top10(projectId, startTime, nowDate));
        }
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.price_top10.name())) {
            res.setPrice_top10(calcPrice_top10(projectId, startTime, nowDate));
        }
        if (SystemUtil.StatisticType.all.name().equals(type) ||
                type.contains(SystemUtil.StatisticType.revenues_compare.name())) {
            res.setRevenues_compare(calcRevenues_compare(projectId, req.getRevenues_compare()));
        }
        return res;
    }

    /**
     * @param projectId
     * @param startTime
     * @param endTime
     * @return 统计今日销售金额
     */
    private Statistic_IndexData_Res.New_amount calcNew_amount(String projectId, Date startTime, Date endTime) {
        Statistic_IndexData_Res.New_amount res = new Statistic_IndexData_Res.New_amount();
        //收入按支付类型统计
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<PrepayStat> prepayStatList = bookingrsMapper.statisticsBookingRsAmountByCreateDate(projectId,
                DateUtil.toLocalDateTime(startTime), DateUtil.toLocalDateTime(endTime));
        if (CollectionUtil.isNotEmpty(prepayStatList)) {
            for (PrepayStat item : prepayStatList) {
                totalAmount = totalAmount.add(item.getAmount());
            }
        }
        res.setAmount(totalAmount);

        return res;
    }


    private Statistic_IndexData_Res.Res_top10 calcRes_top10(String projectId, Date startTime, Date endTime) {
        Statistic_IndexData_Res.Res_top10 res_top10 = new Statistic_IndexData_Res.Res_top10();
        List<Object[]> dataList = room_rsMapper.todayResTop10();
        List<String> hotelArray = new ArrayList<>();
        List<Long> resArray = new ArrayList<>();
        Set<String> hotelSet = new HashSet<>();
        if (dataList != null && !dataList.isEmpty()) { //当天排行
            Hotel hotel;
            String hotelCode;
            String rmtype;
            Roomtype roomtype;
            for (Object[] data : dataList) {
                rmtype = (String) data[0];
                roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).getRecord(projectId, rmtype);
                hotelCode = roomtype.getHotelcode();//通过房型代码获取酒店代码
                hotelSet.add(hotelCode);
                hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).getRecord(projectId, hotelCode);
                hotelArray.add(hotel != null ? hotel.getDescription() : hotelCode);
                resArray.add(((BigInteger) data[1]).longValue());
            }
        }
        if (hotelArray.size() < 10) {
            List<Hotel> hotelList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).getDataList(projectId);
            //排除假房
            for (Hotel tempHotel : hotelList) {
                if (tempHotel.getLsell() && !hotelSet.contains(tempHotel.getCode())) {
                    hotelArray.add(tempHotel.getDescription());
                    resArray.add(0L);
                }
                if (hotelArray.size() > 9) {
                    break;
                }
            }
        }
        res_top10.setHotels(hotelArray.toArray(new String[hotelArray.size()]));
        res_top10.setRes_nums(resArray.toArray(new Long[resArray.size()]));


        return res_top10;
    }

    private Statistic_IndexData_Res.Price_top10 calcPrice_top10(String projectId, Date startTime, Date endTime) {
        Statistic_IndexData_Res.Price_top10 price_top10 = new Statistic_IndexData_Res.Price_top10();
        List<Object[]> dataList = room_rsMapper.todayPriceTop10();
        List<String> hotelArray = new ArrayList<>();
        List<Double> priceArray = new ArrayList<>();
        Set<String> hotelSet = new HashSet<>();
        if (dataList != null && !dataList.isEmpty()) {
            Hotel hotel;
            String hotelCode;
            String rmtype;
            Roomtype roomtype;
            for (Object[] data : dataList) {
                rmtype = (String) data[0];
                roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).getRecord(projectId, rmtype);
                hotelCode = roomtype.getHotelcode();//通过房型代码获取酒店代码
                hotelSet.add(hotelCode);
                hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).getRecord(projectId, hotelCode);
                hotelArray.add(hotel != null ? hotel.getDescription() : hotelCode);
                priceArray.add(((BigDecimal) data[1]).setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }
        if (hotelArray.size() < 10) { //不够10位补充
            List<Hotel> hotelList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).getDataList(projectId);
            for (Hotel tempHotel : hotelList) {
                if (tempHotel.getLsell() && !hotelSet.contains(tempHotel.getCode())) {
                    hotelArray.add(tempHotel.getDescription());
                    priceArray.add(0.00);
                }
                if (hotelArray.size() > 9) {
                    break;
                }
            }
        }
        price_top10.setHotels(hotelArray.toArray(new String[hotelArray.size()]));
        price_top10.setPrices(priceArray.toArray(new Double[priceArray.size()]));

        return price_top10;
    }

    /**
     * @param projectId
     * @param req
     * @return 统计比较营业收入数据
     */
    public Statistic_IndexData_Res.Revenues_compare calcRevenues_compare(String projectId, Statistic_IndexData_Req.Revenues_compare_req req) {
        Statistic_IndexData_Res.Revenues_compare revenuesCompare = new Statistic_IndexData_Res.Revenues_compare();
        List<String> dateArray = new ArrayList<>();
        List<Double> revenueArray = new ArrayList<>();
        List<Double> compareRevenueArray = new ArrayList<>();
        List<String> increatmentArray = new ArrayList<>();
        if (req == null) {
            req = new Statistic_IndexData_Req.Revenues_compare_req();
        }
        Date fromDate = req.getStartDate() == null ? CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, -6) : req.getStartDate();
        Date toDate = req.getEndDate() == null ? new Date() : req.getEndDate();
        if (fromDate.equals(toDate)) {
            toDate = DateUtil.endOfDay(fromDate);
        }
        Date compareFrom = req.getCompare_start();//比较收入起始日期
        Date compareTo = req.getCompare_end();//比较收入结束日期
        String type = req.getType();
        Date tempDate;
        Date compareDate = null;
        Double revenue; //收入
        Double compareRevenue; //比较收入
        int compareDates = CalculateDate.compareDates(toDate, fromDate).intValue() + 1;
        for (int i = 0; i < compareDates; i++) {
            tempDate = CalculateDate.reckonDay(fromDate, 5, i);
            if (compareFrom != null) {
                compareDate = CalculateDate.reckonDay(compareFrom, 5, i);
            }
            dateArray.add(CalculateDate.dateToString(tempDate) + (compareDate != null ? "/" + CalculateDate.dateToString(compareDate) : ""));
            revenue = calcRevenues_statisticByType(projectId, type, tempDate);//各类型订单线下统计数据   营业收入
            revenueArray.add(revenue);
            if (compareDate != null) {
                compareRevenue = calcRevenues_statisticByType(projectId, type, compareDate);//各类型订单线下统计数据
                compareRevenueArray.add(compareRevenue);
                //计算增幅
                increatmentArray.add(calcIncreatment(BigDecimal.valueOf(revenue), BigDecimal.valueOf(compareRevenue)));
            }
        }
        revenuesCompare.setDates(dateArray.toArray(new String[dateArray.size()]));
        revenuesCompare.setRevenues(revenueArray.toArray(new Double[revenueArray.size()]));
        revenuesCompare.setCompare_revenues(compareRevenueArray.toArray(new Double[compareRevenueArray.size()]));
        revenuesCompare.setIncreatment(increatmentArray.toArray(new String[increatmentArray.size()]));

        return revenuesCompare;
    }

    /**
     * @param projectId 项目ID
     * @param type      收入类型 all-所有 room-房型 ticket-票务 productkit-套餐
     * @param tempDate  yyyy-MM-dd
     * @return 统计日期内的规定类型的收入
     */
    private double calcRevenues_statisticByType(String projectId, String type, Date tempDate) {
        LocalDateTime startDate = DateUtil.toLocalDateTime(tempDate);
        LocalDateTime endDate = DateUtil.toLocalDateTime(DateUtil.endOfDay(tempDate));
        List<ProdRevenueStat> list = bookingrsMapper.statisticsBookingRsAmountByPtype(projectId,
                startDate, endDate);
        BigDecimal amount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(list)) {
            for (ProdRevenueStat item : list) {
                //根据类型统计
                if (SystemUtil.StatisticType2.total.name().equals(type)) {
                    amount = amount.add(item.getAmount());
                } else if (SystemUtil.StatisticType2.room.name().equals(type) && ProdType.ROOM.val().equals(item.getPtype())) {
                    amount = amount.add(item.getAmount());
                } else if (SystemUtil.StatisticType2.ticket.name().equals(type) && ProdType.TICKET.val().equals(item.getPtype())) {
                    amount = amount.add(item.getAmount());
                } else if (SystemUtil.StatisticType2.productkit.name().equals(type) && ProdType.TAOCAN.val().equals(item.getPtype())) {
                    amount = amount.add(item.getAmount());
                }
            }

        }
        return amount.doubleValue();
    }

    /**
     * @param thisValue
     * @param compareValue
     * @return 计算涨幅
     */
    private String calcIncreatment(BigDecimal thisValue, BigDecimal compareValue) {
        if (compareValue.doubleValue() == 0) {
            if (thisValue.doubleValue() == 0) {
                return "0%";
            } else {
                return "100%";
            }
        } else {
            return thisValue.subtract(compareValue).multiply(BigDecimal.valueOf(100)).divide(compareValue, 2, RoundingMode.HALF_UP) + "%";
        }
    }

    /**
     * @param projectId
     * @param startDate
     * @param endDate
     * @return 计算今日房价
     */
    private Statistic_IndexData_Res.Room_price calcRoom_price(String projectId, Date startDate, Date endDate) {
        Statistic_IndexData_Res.Room_price room_price = new Statistic_IndexData_Res.Room_price();
        Statistic_DailyRmTypePrice dailyRmTypePrice = bookingrsMapper.statisticsBookingRsRmTypePrice(projectId, startDate, endDate);//
        if (dailyRmTypePrice != null && dailyRmTypePrice.getDatum() != null) { //datum为null标识没有记录
            room_price.setAverage_price(dailyRmTypePrice.getAmount().divide(new BigDecimal(dailyRmTypePrice.getAnz()), 2, RoundingMode.HALF_UP));//平均房价
            room_price.setMax_price(dailyRmTypePrice.getMaxPrice().setScale(2, RoundingMode.HALF_UP));//最大房价
            room_price.setMin_price(dailyRmTypePrice.getMinPrice().setScale(2, RoundingMode.HALF_UP));//最小房价
        }
        return room_price;
    }

    /**
     * @param projectId 项目ID
     * @param req       日期区间
     * @return 统计收入数据
     */
    private Statistic_IndexData_Res.Revenues_statistic calcRevenues_statistic(String projectId, Statistic_IndexData_Req.Revenues_statistic_req req) {
        Statistic_IndexData_Res.Revenues_statistic revenues_statistic = new Statistic_IndexData_Res.Revenues_statistic();
        if (req == null) {
            req = new Statistic_IndexData_Req.Revenues_statistic_req();
        }
        Date startTime = req.getStartDate() == null ? CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, -6) : req.getStartDate();
        Date endTime = req.getEndDate() == null ? new Date() : req.getEndDate();
        if (DateUtil.isSameDay(startTime, endTime)) { //区间是同一天 结束日期取23:59
            endTime = DateUtil.endOfDay(startTime);
        }
        LocalDateTime startDate = DateUtil.toLocalDateTime(startTime);
        LocalDateTime endDate = DateUtil.toLocalDateTime(endTime);
        //收入按产品类型分组统计
        //todo 历史订单日期查询统计合并
        List<ProdRevenueStat> revenueStatList = bookingrsMapper.statisticsBookingRsAmountByPtype(projectId, startDate, endDate);
        List<ProdRevenueStat> revenueNetIncomeStatList = bookingrsMapper.statisticsBookingRsNetIncomeByPtype(projectId, startTime, endTime);
        if (CollectionUtil.isNotEmpty(revenueStatList)) {
            List<Revenues_statistic_prodInfo> prodInfoList = new ArrayList<>();
            //按照产品类型代码进行分组map
            boolean exist = false;
            Map<String, List<ProdRevenueStat>> prodMap = revenueStatList.stream().collect(Collectors.groupingBy(ProdRevenueStat::getPtype));
            Map<String, List<ProdRevenueStat>> prodNetIncomeMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(revenueNetIncomeStatList)) {
                prodNetIncomeMap = revenueNetIncomeStatList.stream().collect(Collectors.groupingBy(ProdRevenueStat::getPtype));
                exist = true;
            }
            for (Map.Entry<String, List<ProdRevenueStat>> entry : prodMap.entrySet()) {
                BigDecimal amount = entry.getValue().stream().map(ProdRevenueStat::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer num = entry.getValue().stream().mapToInt(ProdRevenueStat::getOrdernum).sum();

                Revenues_statistic_prodInfo prodInfo = new Revenues_statistic_prodInfo();
                ProdType prodType = ProdType.getProdType(entry.getKey());//获取产品类型
                prodInfo.setType(prodType.getDesc());
                prodInfo.setAmount(amount);
                prodInfo.setNum(num);
                if (exist) {
                    //净收入
                    if (prodNetIncomeMap.containsKey(entry.getKey())) { //实际到店消费日期包含类型
                        BigDecimal netIncome = prodNetIncomeMap.get(entry.getKey()).stream().map(ProdRevenueStat::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        Integer netIncomeNum = prodNetIncomeMap.get(entry.getKey()).stream().mapToInt(ProdRevenueStat::getOrdernum).sum();//净收入
                        prodInfo.setNetIncome(netIncome);
                        prodInfo.setNetIncomeNum(netIncomeNum);
                    }
                }
                prodInfoList.add(prodInfo);

            }
            BigDecimal totalAmount = prodInfoList.stream().map(Revenues_statistic_prodInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer totalNum = prodInfoList.stream().mapToInt(Revenues_statistic_prodInfo::getNum).sum();
            Revenues_statistic_prodInfo totalProdInfo = new Revenues_statistic_prodInfo();
            totalProdInfo.setType("总计");
            totalProdInfo.setAmount(totalAmount);
            totalProdInfo.setNum(totalNum);
            if (exist) {
                BigDecimal totalNetIncome = prodInfoList.stream().map(Revenues_statistic_prodInfo::getNetIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer netIncomeTotalNum = prodInfoList.stream().mapToInt(Revenues_statistic_prodInfo::getNetIncomeNum).sum();
                totalProdInfo.setNetIncome(totalNetIncome);//净收入
                totalProdInfo.setNetIncomeNum(netIncomeTotalNum);//净收入订单数
            }
            prodInfoList.add(totalProdInfo);
            revenues_statistic.setProdInfo(prodInfoList);

        }
        //收入按支付类型分组统计
        List<PrepayStat> prepayStatList = bookingrsMapper.statisticsBookingRsAmountByPayment(projectId, startDate, endDate);//销售金额
        List<PrepayStat> prepayNetIncomeStatList = bookingrsMapper.statisticsBookingRsNetIncomeByPayment(projectId, startTime, endTime); //净收入
        if (CollectionUtil.isNotEmpty(prepayStatList)) {
            List<Revenues_statistic_payInfo> payInfoList = new ArrayList<>();
            //按照支付类型代码进行分组统计
            boolean exist = false;
            Map<String, List<PrepayStat>> paymentMap = prepayStatList.stream().collect(Collectors.groupingBy(PrepayStat::getPayment));
            Map<String, List<PrepayStat>> paymentNetIncomeMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(prepayNetIncomeStatList)) {
                paymentNetIncomeMap = prepayNetIncomeStatList.stream().collect(Collectors.groupingBy(PrepayStat::getPayment));
                exist = true;
            }
            for (Map.Entry<String, List<PrepayStat>> entry : paymentMap.entrySet()) {
                BigDecimal amount = entry.getValue().stream().map(PrepayStat::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer num = entry.getValue().stream().mapToInt(PrepayStat::getNum).sum();

                Revenues_statistic_payInfo payInfo = new Revenues_statistic_payInfo();
                OnlinePayType payType = OnlinePayType.getPayType(entry.getKey());//获取支付类型
                payInfo.setType(payType.getDesc());
                payInfo.setAmount(amount);
                payInfo.setNum(num);
                //净收入
                if (exist) {
                    if (paymentNetIncomeMap.containsKey(entry.getKey())) {//实际到店消费日期包含类型
                        BigDecimal netIncome = paymentNetIncomeMap.get(entry.getKey()).stream().map(PrepayStat::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        Integer netIncomeNum = paymentNetIncomeMap.get(entry.getKey()).stream().mapToInt(PrepayStat::getNum).sum();
                        payInfo.setNetIncome(netIncome);
                        payInfo.setNetIncomeNum(netIncomeNum);
                    }
                }
                payInfoList.add(payInfo);

            }
            BigDecimal totalAmount = payInfoList.stream().map(Revenues_statistic_payInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer totalNum = payInfoList.stream().mapToInt(Revenues_statistic_payInfo::getNum).sum();
            Revenues_statistic_payInfo totalPayInfo = new Revenues_statistic_payInfo();
            totalPayInfo.setType("总计");
            totalPayInfo.setAmount(totalAmount);
            totalPayInfo.setNum(totalNum);
            if (exist) {
                BigDecimal totalNetIncome = payInfoList.stream().map(Revenues_statistic_payInfo::getNetIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer netIncomeTotalNum = payInfoList.stream().mapToInt(Revenues_statistic_payInfo::getNetIncomeNum).sum();
                totalPayInfo.setNetIncome(totalNetIncome);//净收入
                totalPayInfo.setNetIncomeNum(netIncomeTotalNum);//净收入订单数
            }
            payInfoList.add(totalPayInfo);
            revenues_statistic.setPayInfo(payInfoList);

        }
        return revenues_statistic;
    }

    /**
     * @param projectId
     * @param startTime
     * @param endTime
     * @return 统计今日新增订单数据
     */
    private Statistic_IndexData_Res.New_res calcNew_res(String projectId, Date startTime, Date endTime) {
        Statistic_IndexData_Res.New_res new_res = new Statistic_IndexData_Res.New_res();
        Long total = 0L;
        List<ProdRevenueStat> revenueStatList = bookingrsMapper.statisticsBookingRsAmountByPtype(projectId,
                DateUtil.toLocalDateTime(startTime), DateUtil.toLocalDateTime(endTime));
        if (CollectionUtil.isNotEmpty(revenueStatList)) {
            for (ProdRevenueStat prodRevenueStat : revenueStatList) {
                if (ProdType.ROOM.val().equals(prodRevenueStat.getPtype())) {//房型订单
                    new_res.setRoom((prodRevenueStat.getOrdernum().longValue()));
                    total = total + prodRevenueStat.getOrdernum();
                } else if (ProdType.TICKET.val().equals(prodRevenueStat.getPtype())) {//票务订单
                    new_res.setTicket((prodRevenueStat.getOrdernum().longValue()));
                    total = total + prodRevenueStat.getOrdernum();
                } else if (ProdType.TAOCAN.val().equals(prodRevenueStat.getPtype())) {//套餐订单
                    new_res.setProductkit((prodRevenueStat.getOrdernum().longValue()));
                    total = total + prodRevenueStat.getOrdernum();
                } else if (ProdType.ITEMS.val().equals(prodRevenueStat.getPtype())) {//伴手礼
                    new_res.setGift((prodRevenueStat.getOrdernum().longValue()));
                    total = total + prodRevenueStat.getOrdernum();
                } else if (ProdType.WARES.val().equals(prodRevenueStat.getPtype())) {//景区商品
                    new_res.setSpuitem((prodRevenueStat.getOrdernum().longValue()));
                    total = total + prodRevenueStat.getOrdernum();
                }
            }
            new_res.setCol(total);
        }
        return new_res;
    }

    /**
     * @param projectId
     * @param startTime
     * @param endTime
     * @return 统计今日新增用户人数
     */
    private Statistic_IndexData_Res.New_user calcNew_user(String projectId, Date startTime, Date endTime) {
        Statistic_IndexData_Res.New_user new_user = new Statistic_IndexData_Res.New_user();
        long addTotal = appuserMapper.countByProjectidAndRegdateBetween(projectId, DateUtil.toLocalDateTime(startTime), DateUtil.toLocalDateTime(endTime));//用户新增数 //后续可以分平台统计
        new_user.setUser(addTotal);
        return new_user;

    }

}
