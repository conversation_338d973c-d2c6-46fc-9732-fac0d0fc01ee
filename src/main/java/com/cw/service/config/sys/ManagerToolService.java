package com.cw.service.config.sys;

import com.cw.pojo.dto.common.res.Common_Pic_Res;
import com.cw.pojo.dto.conf.res.sys.GenShortLinkRes;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/3/11 11:51
 **/
public interface ManagerToolService {

    Common_Pic_Res generateQrList(String projectId, int pathNo, String type, String param, int qrsize);


    Common_Pic_Res generateSalesQr(String projectId, int pathNo, String appType, String prodType, String groupId, String productId, String salesId);

    Common_Pic_Res generateStampQr(String projectId, int pathNo, String appType, String couponId);

    /**
     * 生成短链接，用于跳转到小程序指定页面
     * @param projectId 项目ID
     * @param targetPath 目标路径
     * @param params 链接参数
     * @param description 链接描述
     * @param expireDays 有效期天数
     * @return 短链接信息
     */
    GenShortLinkRes generateShortLink(String projectId, String targetPath, String params, String description, Integer expireDays);
}
