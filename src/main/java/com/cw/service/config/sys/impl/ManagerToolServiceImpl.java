package com.cw.service.config.sys.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.shortlink.GenerateShortLinkRequest;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.platform.wechat.WxMaConfiguration;
import com.cw.entity.Vendorconfig;
import com.cw.pojo.dto.common.res.Common_Pic_Res;
import com.cw.pojo.dto.conf.res.sys.GenShortLinkRes;
import com.cw.service.config.sys.ManagerToolService;
import com.cw.service.oss.OSSService;
import com.cw.utils.CalculateDate;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.menus.ProductQrcodePathFactory;
import me.chanjar.weixin.common.error.WxErrorException;
import org.redisson.api.RMapCache;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/3/11 11:51
 **/
@Service
public class ManagerToolServiceImpl implements ManagerToolService {

    @Value("${cwconfig.debugmode:false}")
    Boolean debugMode = false;

    @Autowired
    OSSService ossService;

    @Override
    public Common_Pic_Res generateQrList(String projectId, int pathNo, String type, String param, int qrsize) {
        Common_Pic_Res commonPicRes = new Common_Pic_Res();
        AgentType agentType = EnumUtil.fromString(AgentType.class, type, null);
        String path = ProductQrcodePathFactory.getAgentPath(projectId, agentType, pathNo, param);
        String qrStr = "";
        if (StrUtil.isNotBlank(path)) {
            if (agentType.equals(AgentType.WXAPP)) {
                qrStr = getWxappQr(projectId, path, qrsize);
            }
        }
        commonPicRes.setBase64(qrStr);
        return commonPicRes;
    }

    @Override
    public Common_Pic_Res generateSalesQr(String projectId, int pathNo, String appType, String prodType, String groupId, String productId, String salesId) {
        Common_Pic_Res commonPicRes = new Common_Pic_Res();
        AgentType agentType = EnumUtil.fromString(AgentType.class, appType, null);
        String path = "pages-A/scene/fenxiao";
        String qrStr = "";
        if (agentType.equals(AgentType.WXAPP)) {
            qrStr = getWxappSalesQr(projectId, path, 272, groupId, salesId);
        }
        commonPicRes.setBase64(qrStr);
        return commonPicRes;
    }

    @Override
    public Common_Pic_Res generateStampQr(String projectId, int pathNo, String appType, String couponId) {
        Common_Pic_Res commonPicRes = new Common_Pic_Res();
        AgentType agentType = EnumUtil.fromString(AgentType.class, appType, null);
        String path = "pages-A/scene/coupon";
        String qrStr = "";
        if (agentType.equals(AgentType.WXAPP)) {
            qrStr = getWxappStampQr(projectId, path, 272, couponId);
        }
        commonPicRes.setBase64(qrStr);
        return commonPicRes;
    }



    private String getWxappQr(String projectId, String path, int qrsize) {
        Vendorconfig vendorconfig = ((VendorConfigCache) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.VENDORCONFIG)).getVendorConfig(projectId, VendorType.WX_APP);
        if (vendorconfig != null) {
            WxMaService wxMaService = WxMaConfiguration.getMaService(vendorconfig.getAppid());
            String qrbase64 = null;

            String day = CalculateDate.dateToString(new Date(), false, CalculateDate.keyspd);
            String key = day + projectId + (debugMode ? "0" : "1") + path;
            RMapCache<String, String> keymap = RedisTool.getRedissonClient().getMapCache(RedisKey.QrInfo);
            if (keymap.containsKey(key)) {//  ceshi keymap.containsKey(key)
                qrbase64 = keymap.get(key);
                return qrbase64;
            } else {
                try {
                    byte[] bytes = wxMaService.getQrcodeService().createWxaCodeBytes(path, "release", qrsize, true, null, true);   //.createQrcodeBytes(path, 1290);
                    if (debugMode) {
                        bytes = wxMaService.getQrcodeService().createWxaCodeBytes(path, "trial", qrsize, true, null, true); //测试环境生成体验版二维码
                    }
                    qrbase64 = Base64.encode(bytes);

                    keymap.put(key, qrbase64, 6L, TimeUnit.HOURS);

                    return qrbase64;
                } catch (WxErrorException e) {
                    e.printStackTrace();
                }
            }
        }
        return "";
    }

    private String getWxappSalesQr(String projectId, String path, int qrsize, String groupId, String salesId) {
        Vendorconfig vendorconfig = ((VendorConfigCache) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.VENDORCONFIG)).getVendorConfig(projectId, VendorType.WX_APP);
        String qrbase64 = null;
        if (vendorconfig != null) {
            WxMaService wxMaService = WxMaConfiguration.getMaService(vendorconfig.getAppid());
            String sceneVal = StrUtil.format("s={}&t=T&p={}", salesId, groupId);
            String target = debugMode ? "trial" : "release";

            //bytes = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(scene, path,false, "trial", qrsize, true, null, true); //测试环境生成体验版二维码

            File tempFile = null;
            try {
                byte[] bytes = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(sceneVal, path, false, target, qrsize, true, null, true);
                qrbase64 = Base64.encode(bytes);

                tempFile = File.createTempFile(projectId + groupId + salesId, ".jpg");
                FileUtil.writeBytes(bytes, tempFile);
                tempFile = FileUtil.rename(tempFile, projectId + groupId + salesId + ".jpg", true);
                ossService.uploadLocalFile(tempFile, "salesqr", "0", projectId);
                if (tempFile != null && tempFile.exists()) {
                    LoggerFactory.getLogger(this.getClass()).info("删除{},{}", tempFile.getPath(), tempFile.getName());
                    FileUtil.del(tempFile);
                }

                return qrbase64;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return "";
    }

    private String getWxappStampQr(String projectId, String path, int qrsize, String couponId) {
        Vendorconfig vendorconfig = ((VendorConfigCache) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.VENDORCONFIG)).getVendorConfig(projectId, VendorType.WX_APP);
        String qrbase64 = null;
        if (vendorconfig != null) {
            long stamp = System.currentTimeMillis();
            WxMaService wxMaService = WxMaConfiguration.getMaService(vendorconfig.getAppid());
            String sceneVal = StrUtil.format("c={}&t={}", couponId, stamp);
            String target = debugMode ? "trial" : "release";

            try {
                byte[] bytes = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(sceneVal, path, false, target, qrsize, true, null, true);
                qrbase64 = Base64.encode(bytes);

                return qrbase64;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return "";
    }

    @Override
    public GenShortLinkRes generateShortLink(String projectId, String targetPath, String params, String description, Integer expireDays) {
        GenShortLinkRes response = new GenShortLinkRes();

        // 获取微信小程序配置
        Vendorconfig vendorconfig = ((VendorConfigCache) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.VENDORCONFIG)).getVendorConfig(projectId, VendorType.WX_APP);

        if (vendorconfig != null) {
            WxMaService wxMaService = WxMaConfiguration.getMaService(vendorconfig.getAppid());

            try {
                // 构建完整的小程序路径
                String fullPath = buildFullMiniAppPath(targetPath, params);

                // 创建短链接请求对象
                GenerateUrlLinkRequest request = GenerateUrlLinkRequest.builder()
                        .path(fullPath)
                        .query(StrUtil.isBlank(params)?"":params)  //mu'q
                        .expireType(1)
                        .expireInterval(30)
                        .build();




                // 调用微信API生成短链接
                String shortUrl = wxMaService.getLinkService().generateUrlLink(request);

                // 设置响应数据
                response.setShortLinkId(extractShortLinkId(shortUrl));
                response.setShortUrl(shortUrl);
                response.setTargetPath(targetPath);
                response.setParams(params);
                response.setFullMiniAppUrl(fullPath);
                response.setCreateTime(new Date());
                response.setExpireTime(calculateExpireTime(expireDays));
                response.setDescription(description);
                response.setVisitCount(0);


            } catch (Exception e) {
                LoggerFactory.getLogger(this.getClass()).error("生成微信短链接失败: projectId={}, error={}",
                        projectId, e.getMessage(), e);
                throw new RuntimeException("生成微信短链接失败: " + e.getMessage());
            }
        } else {
            throw new RuntimeException("未找到项目对应的微信小程序配置: " + projectId);
        }

        return response;
    }

    /**
     * 从微信短链接URL中提取短链接ID
     */
    private String extractShortLinkId(String shortUrl) {
        if (StrUtil.isBlank(shortUrl)) {
            return "";
        }
        // 从URL中提取最后一部分作为ID
        String[] parts = shortUrl.split("/");
        return parts.length > 0 ? parts[parts.length - 1] : "";
    }

    /**
     * 构建完整的小程序路径
     */
    private String buildFullMiniAppPath(String targetPath, String params) {
          return targetPath;  //暂时不考虑参数的拼接
        //if (StrUtil.isBlank(params)) {
        //    return targetPath;
        //}
        //
        //String separator = targetPath.contains("?") ? "&" : "?";
        //return targetPath + separator + params;
    }

    /**
     * 计算过期时间
     */
    private Date calculateExpireTime(Integer expireDays) {
        if (expireDays == null || expireDays <= 0) {
            expireDays = 30; // 默认30天
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, expireDays);
        return calendar.getTime();
    }


}
