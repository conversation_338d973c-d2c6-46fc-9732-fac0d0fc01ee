package com.cw.pojo.dto.conf.req.sys;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Descripstion 生成短链接请求
 * @Create 2025-08-03
 **/
@Data
@ApiModel("生成短链接请求")
public class GenShortLinkReq {

    @NotBlank(message = "目标路径不能为空")
    @ApiModelProperty(value = "目标路径", required = true, example = "pages/index/index")
    private String targetPath;

    @ApiModelProperty(value = "链接参数", example = "id=123&type=product")
    private String params;

    @ApiModelProperty(value = "链接描述", example = "产品详情页面")
    private String description;

    @ApiModelProperty(value = "有效期天数，默认30天", example = "30")
    private Integer expireDays = 30;
}
