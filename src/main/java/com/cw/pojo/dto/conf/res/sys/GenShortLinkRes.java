package com.cw.pojo.dto.conf.res.sys;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion 生成短链接响应
 * @Create 2025-08-03
 **/
@Data
@ApiModel("生成短链接响应")
public class GenShortLinkRes {

    @ApiModelProperty(value = "短链接ID")
    private String shortLinkId;

    @ApiModelProperty(value = "短链接URL")
    private String shortUrl;

    @ApiModelProperty(value = "原始目标路径")
    private String targetPath;

    @ApiModelProperty(value = "链接参数")
    private String params;

    @ApiModelProperty(value = "完整的小程序跳转链接")
    private String fullMiniAppUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "过期时间")
    private Date expireTime;

    @ApiModelProperty(value = "链接描述")
    private String description;

    @ApiModelProperty(value = "访问次数")
    private Integer visitCount = 0;
}
