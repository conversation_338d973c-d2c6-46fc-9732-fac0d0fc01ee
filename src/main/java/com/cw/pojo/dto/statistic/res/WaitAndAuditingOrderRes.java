package com.cw.pojo.dto.statistic.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/17 0017
 */
@Data
public class WaitAndAuditingOrderRes {
    @ApiModelProperty(value = "等待审核订单数量")
    Integer waitOrder;
    @ApiModelProperty(value = "等待退款审核订单数量")
    Integer auditingOrder;

    @ApiModelProperty(value = "当天景区产品订单数量")
    Integer spuRsOrder;

    @ApiModelProperty(value = "等待审核投稿作品数量")
    Integer auditingContest;
    @ApiModelProperty(value = "线下支付订单数量")
    Integer passOrder;
    @ApiModelProperty(value = "首页统计数据")
    Object indexData;



    @ApiModelProperty(value = "待处理发票数量")
    Integer invoiceOrder;
}
