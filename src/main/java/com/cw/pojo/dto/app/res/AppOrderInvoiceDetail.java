package com.cw.pojo.dto.app.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-20
 */
@Data
public class AppOrderInvoiceDetail {
    @ApiModelProperty(value = "发票记录唯一标识")
    Long sqlid = 0L;
    @ApiModelProperty(value = "发票抬头")
    String title;
    @ApiModelProperty(value = "抬头类型,1-个人或事业单位，2-企业", example = "2")
    Integer titletype = 1;
    @ApiModelProperty(value = "接收发票电子邮箱", example = "456464**<EMAIL>")
    String email;
    @ApiModelProperty(value = "接收发票手机号码", example = "15278917894")
    String phone;
    @ApiModelProperty(value = "发票类型，1-电子增值税普通发票，2-电子增值税专用发票", example = "1")
    Integer type = 1;
    @ApiModelProperty(value = "发票流水号")
    String invoiceId;
    @ApiModelProperty(value = "发票申请时间", example = "2023-04-01 12:40:20")
    String creatTime;
    @ApiModelProperty(value = "发票凭证数量")
    Integer num = 1;
    @ApiModelProperty(value = "纳税人识别号，企业必填", notes = "纳税人识别号，企业必填", required = true, example = "110000")
    private String taxno = "";

    @ApiModelProperty(value = "开户银行,企业选填", notes = "开户银行,企业选填", required = true, example = "110000")
    private String bankname = "";

    @ApiModelProperty(value = "银行账号，企业选填", notes = "银行账号，企业选填", required = true, example = "110000")
    private String bankno = "";

    @ApiModelProperty(value = "企业地址，企业选填", notes = "企业地址，企业选填", required = true, example = "广西南宁青秀区青山")
    private String address = "";

    @ApiModelProperty(value = "企业电话，企业选填", notes = "企业电话，企业选填", required = true, example = "***********")
    private String tel = "";
}
