package com.cw.pojo.dto.app.res;

import com.cw.pojo.dto.common.res.PageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-05-31
 */
@Data
public class AppUserInvoiceOrderListRes extends PageResponse<AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode,
        AppUserInvoiceOrderListRes.AppUnInvoiceOrderListNode> {

    @Data
    @ApiModel("H5应用端OR发票订单列表元素")
    public static class AppUnInvoiceOrderListNode {
        @NotBlank
        @ApiModelProperty(value = "产品类型", notes = "R(酒店),T(票务大组),C(餐厅),Z(套餐大组),M(会议室)", required = true, example = "R")
        String type;

        @ApiModelProperty(value = "产品代码", required = true, example = "001")
        private String productCode = "";

        @ApiModelProperty(value = "发票状态, N-未开，S,P-已开,Z-待红冲,R-已红冲,X-开票失败", required = true, example = "S")
        private String istatus = "N";

        @ApiModelProperty(value = "订单号", required = true, example = "111111")
        private String orderid = "";

        @ApiModelProperty(value = "商品封面图")
        private String titlepicurl = "";


        @ApiModelProperty(value = " 预订人")
        private String booker = "";

        @ApiModelProperty(value = "商品对应的大组名称", required = true, example = "酒店")
        private String groupid = "";

        @ApiModelProperty(value = "商品数量", required = true, example = "0")
        private Integer num = 0;

        @ApiModelProperty(value = "购买商品描述", required = true, example = "商品描述")
        private String description = "";

        @ApiModelProperty(value = "价格信息", required = true, example = "100.00")
        private Double price = 0.00;

        @ApiModelProperty(value = "使用日期描述", required = true, example = "2021.11.10-2021.11.11")
        private String usedateinfo = "";


        @ApiModelProperty(value = "订单状态", required = true, example = "待支付C,待出行P,取消X,完成 F")
        private String status = "";


        @ApiModelProperty(value = "下单时间", required = true, example = "2021-11-10 22:22:22")
        private String orderdatetime = "";

    }
}
