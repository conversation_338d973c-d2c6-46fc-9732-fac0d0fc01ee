package com.cw.pojo.dto.app.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Describe 订单发票记录
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-20
 */
@Data
@ApiModel(description = "订单发票详情")
public class AppOrderInvoiceResult {
    @ApiModelProperty(value = "订单号，订单号不为空显示详情")
    String orderid = "";
    @ApiModelProperty(value = "是否允许开票")
    boolean lcreate = false;
    @ApiModelProperty(value = "是否允许修改抬头")
    boolean lmodify = false;
    @ApiModelProperty(value = "总金额", example = "0.00")
    BigDecimal totalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "购买数量")
    Integer num = 1;

    @ApiModelProperty(value = "产品归属的大组代码.")
    String groupCode = "";

    @ApiModelProperty(value = "产品大组描述")
    String groupDesc = "";


    @ApiModelProperty(value = "购买产品描述")
    String productDesc = "";


    @ApiModelProperty(value = "下单时间 yyyy-MM-dd HH:mm")
    String createdatetime = "";

    @ApiModelProperty(value = "产品图片")
    String productpic = "";

    @ApiModelProperty(value = "产品类型")
    String productType = "";

    @ApiModelProperty(value = "订单发票状态 N-未开，S,P-已开,Z-待红冲,R-已红冲,X-开票失败")
    String status = "";

    @ApiModelProperty(value = "PDF格式发票图片地址")
    String pdfUrl = "";

    @ApiModelProperty(value = "jpg格式发票图片地址")
    String jpgUrl = "";

    @ApiModelProperty(value = "门票订单二维码.待出行状态下才有")
    String qrcode = "";

    @ApiModelProperty(value = "发票详情")
    AppOrderInvoiceDetail details;


}
