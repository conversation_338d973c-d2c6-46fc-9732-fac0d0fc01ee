package com.cw.pojo.dto.app.req;

import com.cw.pojo.dto.app.req.node.InvoiceNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-03-31
 */
@Data
@ApiModel(description = "发票开具申请")
public class AppInvoiceCreateReq extends InvoiceNode {
    @NotBlank(message = "订单号不可为空")
    @ApiModelProperty(value = "订单号")
    String bookingid;
    @Max(value = 2)
    @Min(value = 1)
    @ApiModelProperty(value = "抬头类型，1-个人或事业单位，2-企业", required = true)
    Integer titletype = 1;
    @NotBlank(message = "发票抬头不能为空")
    @Size(max = 100, message = "发票抬头长度不超过100位")
    @ApiModelProperty(value = "发票抬头", required = true)
    String title = "";

    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "接收电子发票邮箱", required = true)
    String email = "";

    @ApiModelProperty(value = "接收电子发票手机号码", required = true)
    String phone = "";

    @ApiModelProperty(value = "发票类型，1-电子增值税普通发票，2-电子增值税专用发票", required = true)
    Integer type = 1;
}
