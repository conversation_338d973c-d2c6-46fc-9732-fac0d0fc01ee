package com.cw.pojo.dto.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-19
 */
@Data
@ApiModel(description = "手动确认发票")
public class InvoiceConfirmReq {
    @ApiModelProperty(value = "发票记录标识ID",required = true)
    @NotNull(message = "发票标识记录ID不能为空")
    Long sqlid;
    @ApiModelProperty(value = "发票流水号",required = true)
    String incoideid;

    @ApiModelProperty(value = "发票流号码")
    String incoideno;
    @ApiModelProperty(value = "发票代码")
    String incoidecode;
    @ApiModelProperty(value = "发票检查码")
    String checkcode;
    @ApiModelProperty(value = "发票照片地址",required = true)
    String jpgurl;

    // @ApiModelProperty(value = "不含税金额")
    // BigDecimal extaxamount;
    // @ApiModelProperty(value = "含税金额,不填默认订单金额")
    // BigDecimal incltaxamount;



    @ApiModelProperty(value = "开票成功日期,不填默认当天")
    Date createDate;

}
