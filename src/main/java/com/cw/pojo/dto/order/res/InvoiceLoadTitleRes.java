package com.cw.pojo.dto.order.res;

import com.cw.pojo.dto.app.req.node.InvoiceNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-24
 */
@Data
public class InvoiceLoadTitleRes extends InvoiceNode {
    @ApiModelProperty(value = "公共数据对象唯一id,新建为0", example = "0", required = true)
    private Long sqlid = 0L;

    @ApiModelProperty(value = "订单号", required = true)
    String bookingid;

    @ApiModelProperty(value = "抬头类型，1-个人或事业单位，2-企业", required = true)
    Integer titletype = 1;
    @ApiModelProperty(value = "发票抬头", required = true)
    String title = "";

    @ApiModelProperty(value = "接收电子发票邮箱", required = true)
    String email;

    @ApiModelProperty(value = "接收电子发票手机号码", required = true)
    String phone;

    @ApiModelProperty(value = "发票类型，1-电子增值税普通发票，2-电子增值税专用发票", required = true)
    Integer type = 1;






}
