package com.cw.pojo.dto.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-24
 */
@Data
@ApiModel(value = "发票订单详情请求")
public class InvoiceLoadReq {
    @ApiModelProperty(value = "实体对象唯一id", example = "1")
    long sqlid = 0L;

    @ApiModelProperty(value = "主订单号")
    String bookingid;

}
