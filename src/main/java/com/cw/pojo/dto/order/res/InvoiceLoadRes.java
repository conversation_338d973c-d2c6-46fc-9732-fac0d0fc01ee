package com.cw.pojo.dto.order.res;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-24
 */
@Data
public class InvoiceLoadRes {
    @ApiModelProperty(value = "公共数据对象唯一id,新建为0", example = "0", required = true)
    private Long sqlid = 0L;
    @ApiModelProperty(value = "发票申请创建日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;
    @ApiModelProperty(value = "订单取消日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonIgnore
    private LocalDateTime canceldate;
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal amount;
    @ApiModelProperty(value = "主单订单号")
    private String bookingid;
    //@ApiModelProperty(value = "外部订单号")
    //private String outid;
    //@ApiModelProperty(value = "主单状态")
    //private String mainstatus;
    //@ApiModelProperty(value = "主单状态描述")
    //private String mainstatusdesc;
    //@ApiModelProperty(value = "线下订单状态")
    //private String ostatus;
    //@ApiModelProperty(value = "购买数量")
    //private Integer anz = 0;
    //@ApiModelProperty(value = "购买的产品代码")
    //private String product;
    @ApiModelProperty(value = "购买产品类型")
    private String ptype;
    @ApiModelProperty(value = "购买产品类型描述")
    private String ptypedesc;
    @ApiModelProperty(value = "预订人姓名")
    private String bookername;
    @ApiModelProperty(value = "联系电话")
    private String tel;

    //@ApiModelProperty(value = "付款方式微信,淘宝等")
    //private String payment;
    @ApiModelProperty(value = "支付订单号")
    private String payno;
    @ApiModelProperty(value = "支付流水号")
    private String payid;
    //@ApiModelProperty(value = "支付时间")
    //@JSONField(format = "yyyy-MM-dd HH:mm:ss")
    //private LocalDateTime paytime;
    //@ApiModelProperty(value = "退款时间")
    //@JSONField(format = "yyyy-MM-dd HH:mm:ss")
    //private LocalDateTime refundtime;
    //@ApiModelProperty(value = "支付金额")
    //private BigDecimal payamount;
    //@ApiModelProperty(value = "退款金额")
    //private BigDecimal refund;


    //@ApiModelProperty(value = "更新时间")
    //@JSONField(format = "yyyy-MM-dd HH:mm:ss")
    //@ExcelProperty(value = "更新时间", index = 11)
    //private Date handletime;
    @ApiModelProperty(value = "发票状态")
    @ExcelProperty(value = "发票状态", index = 9)
    private String istatus;
    @ApiModelProperty(value = "发票信息")
    private String invoiceinfo;
    @ApiModelProperty(value = "发票流水号")
    private String invoiceid;
    @ApiModelProperty(value = "发票号码")
    @ExcelProperty(value = "发票号码", index = 8)
    private String invoiceno;
    @ApiModelProperty(value = "发票代码")
    private String invoicecode;
    @ApiModelProperty(value = "发票类型， 1-普票，2-专票")
    private Integer type = 1;
    @ApiModelProperty(value = "发票类型描述")
    private String typedesc;
    @ApiModelProperty("操作人")
    @ExcelProperty(value = "操作人", index = 10)
    private String handler;
    @ApiModelProperty(value = "不含税发票金额")
    private BigDecimal extaxamount;
    @ApiModelProperty(value = "，不含税对冲金额")
    private BigDecimal redextaxamount;
    @ApiModelProperty(value = "含税发票金额")
    private BigDecimal incltaxamount;
    @ApiModelProperty(value = "含税对冲金额")
    private BigDecimal redincltaxamount;


    @ApiModelProperty(value = "接收发票邮箱")
    private String email;
    @ApiModelProperty(value = "接收发票手机号")
    private String  phone;




}
