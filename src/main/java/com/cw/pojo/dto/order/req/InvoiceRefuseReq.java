package com.cw.pojo.dto.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-19
 */
@Data
@ApiModel(description = "手动确认发票")
public class InvoiceRefuseReq {
    @ApiModelProperty(value = "发票记录标识ID",required = true)
    @NotNull(message = "发票标识记录ID不能为空")
    Long sqlid;


}
