package com.cw.mapper;

import com.cw.entity.Invoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/13 0013
 */
public interface InvoiceMapper extends JpaRepository<Invoice, Long>, JpaSpecificationExecutor<Invoice> {

    Invoice findBySqlid(Long sqlid);


    /**
     * @param bookingid
     * @param projectid
     * @return 按照创建时间倒序的发票记录，可以多张发票记录
     */
    List<Invoice> findAllByBookingidAndProjectidOrderByCreatetimeDesc(String bookingid, String projectid);


    @Query(value = "select  * from Invoice where uid=?1 and projectid=?2 group by bookingid", nativeQuery = true)
    List<Invoice> findMyInvoiceOrderList(String userid, String projectid);

    /**
     * @param userid
     * @param projectid
     * @return 取主订单号bookingid最新发票记录
     */
    @Query(value = "select  * from Invoice where sqlid in (select max(sqlid) from Invoice where uid=?1 and projectid=?2 and istatus<>'N'  group by bookingid) ", nativeQuery = true)
    List<Invoice> findInvoiceOrderList(String userid, String projectid);

    /**
     * @param invoiceId 发票流水号
     * @param projectId 项目ID
     * @return
     */
    Invoice findByInvoiceidAndProjectid(String invoiceId, String projectId);

    /**
     * @param invoiceno 发票号码
     * @param projectId 项目ID
     * @return
     */
    Invoice findByInvoicenoAndProjectid(String invoiceno, String projectId);

    /**
     * @param redInvoiceId 红冲发票流水号
     * @param projectId    项目ID
     * @return
     */
    Invoice findByRedinvoiceidAndProjectid(String redInvoiceId, String projectId);

    /**
     * @param bookingid
     * @param projectId
     * @return 根据主订单号返回
     */
    @Query(value = "select * from Invoice where bookingid=?1 and projectid=?2 and invoiceid='' and createdate <='1900-01-01 00:00:00'", nativeQuery = true)
    Invoice findByBookingidAndProjectid(String bookingid, String projectId);

    /**
     * @param projectid
     * @return 返回发票状态未开，发票流水号不为空的发票记录集合
     */
    @Query(value = "select * from Invoice where projectId=?1 and istatus='N' and invoiceid <>'' and createdate <='1900-01-01 00:00:00'", nativeQuery = true)
    List<Invoice> findInitAllByProjectid(String projectid);

    /**
     * @param projectid
     * @return 返回红冲发票流水号不为空，红冲开票时间未更新的发票记录
     */
    @Query(value = "select * from Invoice where projectId=?1  and redinvoiceid <>'' and redcreatedate <='1900-01-01 00:00:00'", nativeQuery = true)
    List<Invoice> findInitRedAllByProjectid(String projectid);


    /**
     *
     * @param projectId
     * @return
     */
    @Query(value = "select  count(*) from Invoice where istatus in ('N','P','Z') and  projectid=?1")
    Integer countInvoiceOrder(String projectId);


    /**
     * @param bookingid   主订单号
     * @param payid       支付流水号
     * @param payno       支付订单号
     * @param bookername  预订人姓名/电话
     * @param status      订单支付状态
     * @param payment     支付方式
     * @param ptype       订单商品类型
     * @param invoiceno   发票号码
     * @param invoicetype 发票类型
     * @param istatus     发票状态
     * @param handletime  发票更新时间
     * @param projectId   项目ID
     * @return 返回发票记录列表
     */
    //@Query(value = "SELECT i.sqlid,i.handletime,i.bookingid,i.handler,i.invoiceno,i.invoicecode,i.type,i.status as istatus,rs.ptype,rs.bookername," +
    //        "rs.tel,p.paymethod," +
    //        "p.serialno,p.payno,p.pstatus,p.status,p.amount from Invoice i " +
    //        "LEFT JOIN Booking_rs rs on i.bookingid = rs.bookingid " +
    //        "LEFT JOIN Prepay p on p.bookingid = i.bookingid " +
    //        "WHERE if(?1!='' and ?1 is not null ,i.bookingid =?1, 1=1) " +
    //        "and if(?2!='' and ?2 is not null ,p.serialno =?2, 1=1) " +
    //        "and if(?3!='' and ?3 is not null ,p.payno =?3, 1=1) " +
    //        "and if(?4!='' and ?4 is not null ,rs.bookername = ?4, 1=1) " +
    //        "and if(?5!='' and ?5 is not null ,p.pstatus = ?5, 1=1) " +
    //        "and if(?6='' and ?5 is not null ,p.status = ?6, 1=1) " +
    //        "and if(?7!='' and ?5 is not null ,p.paymethod = ?7, 1=1) " +
    //        "and if(?8!='' and ?7 is not null ,rs.ptype =?8, 1=1) " +
    //        "and if(?9!='' and ?7 is not null ,i.invoiceno =?9, 1=1) " +
    //        "and if(?10!='' and ?7 is not null ,i.type =?10, 1=1) " +
    //        "and if(?11!='' and ?7 is not null ,i.status =?11, 1=1) " +
    //        "and if(?12!='' and ?8 is not null ,i.handletime =?12, 1=1) " +
    //        "and i.projectId =?13", nativeQuery = true)
    //List<Map<String, Object>> findAllInvoice(String bookingid, String payid, String payno, String bookername, String pstatus,
    //                                         String status, String payment, String ptype, String invoiceno, String invoicetype, String istatus, Date handletime, String projectId);
}
