package com.cw.utils;

public class StatusUtil {


    /**
     * 主订单状态枚举
     */
    public enum BookingRSStatusEnum {
        INITIAL(BookingRsStatus.INITIAL, "预定"),
        CONFIRM(BookingRsStatus.CONFIRM, "待支付"),
        WAIT(BookingRsStatus.WAIT, "待确认"),
        PAY(BookingRsStatus.PAY, "已支付"),
        CHECKIN(BookingRsStatus.CHECKIN, "到店"),
        AUDTING(BookingRsStatus.AUDTING, "退款审核中"),
        REFUNDING(BookingRsStatus.REFUNDING, "退款中"),
        CANCEL(BookingRsStatus.CANCEL, "已取消"),
        FINISH(BookingRsStatus.FINISH, "完成");
        private String desc;
        private String code;

        BookingRSStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    public class PrepayStatus {//预付款状态
        public final static String PART = "P";//部分
        public final static String ALL = "A";//全部
        public final static String REFUND = "R";//退款
    }

    public class RsPayStatus {//综合预定付款状态
        public final static String NOPAY = "";//未付款
        public final static String PAY = "P";//已付款
    }

    public class TicketRsStatus {//门票预定状态
        public final static String BOOKING = "N";//预定
        public final static String PRINT = "P";//出票
        public final static String RETURN = "R";//退票
        public final static String CHECKPOINT = "O";//检票
        public final static String FINISH = "F";//完成
        public final static String CANCEL = "C";//取消
    }

    public class CaterRsStatus {//餐饮预定状态
        public final static String BOOKING = "N";//预定
        public final static String ARRIVAL = "S";//到店
        public final static String FINISH = "F";//完成
        public final static String CANCEL = "C";//取消
        public final static String CHECKIN = "I";//checkin
        public final static String NOSHOW = "NS";//应到未到
    }


    public class RoomRsStatus {//客房预定状态
        public final static String EXPECTED = "";
        public final static String CHECKIN = "C";
        public final static String CANCELED = "X";
        public final static String NOSHOW = "N";
        public final static String CHECKOUT = "L";
        public final static String PAYED = "P"; //入住前已付款?暂定
        public final static String DELETED = "D";//表示状态已经删除 用来表示R8接口
    }

    /**
     * 主单状态
     */
    public class BookingRsStatus {//
        public final static String INITIAL = "N";//初始状态
        public final static String PAY = "P";//已支付
        public final static String WAIT = "W";//审核待确认
        public final static String CONFIRM = "C";//订单已创建.等待支付
        public final static String CHECKIN = "A";//到店
        public final static String AUDTING = "U";// 订单退款待审核状态
        public final static String REFUNDING = "R";//退款中.当商家账户余额不足时.将订单标记为退款中.定时重试
        public final static String CANCEL = "X";//已经取消
        public final static String FINISH = "F";//完成
    }

    /**
     * 活动预约状态
     */
    public class ActRsStatus {//
        public final static String PAY = "P";//已支付
        public final static String CONFIRM = "C";//订单已创建.等待支付
        public final static String AUDTING = "U";// 订单退款待审核状态
        public final static String CANCEL = "X";//已经取消
        public final static String FINISH = "F";//完成
    }

    /**
     * 发票状态枚举
     */
    public enum InvoiceStatusEnum {
        INIT("N", "未开"),
        FINISH("S", "已开"),
        REPUSH("P", "需要重发"),
        NEEDRED("Z", "待红冲"),
        REDED("R", "已红冲"),
        //PARTRED("U", "部分红冲"),
        REFUSE("X", "开票失败"),
        CANCEL("C", "作废"); //电子发票没有这个状态
        private String desc;
        private String code;

        InvoiceStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }

        public static String getInvoiceStatusDesc(String code) {
            InvoiceStatusEnum[] values = InvoiceStatusEnum.values();
            for (InvoiceStatusEnum item : values) {
                if (item.getCode().equals(code)) {
                    return item.getDesc();
                }
            }
            return code;
        }
    }

    /**
     * 发票状态枚举
     */
    public enum PassrsStatusEnum {
        WAIT(PassRsStatus.WAIT, "未发起"),
        SENDED(PassRsStatus.SENDED, "已发起"),
        CONFIRM(PassRsStatus.CONFIRM, "已下发"),
        CANCEL(PassRsStatus.CANCEL, "已取消");
        private String desc;
        private String code;

        PassrsStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 退款状态枚举  prepay表支付后申请
     */
    public enum AuditingStatusEnum {
        EXPECTED("N", "待处理"),
        CONFIRM("O", "审核通过"),
        REFUSE("X", "已拒绝");
        private String desc;
        private String code;

        AuditingStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 退款状态枚举  prepay表支付后申请
     */
    public enum ActRsStatusEnum {
        PAY("P", "已支付"),
        COMFIRM("C", "待支付"),
        AUDITING("U", "审核中"),
        CANCEL("X", "已取消"),
        FINISH("F", "完成");

        private String desc;
        private String code;

        ActRsStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 发票用来判断支付状态  未支付或者取消支付不应该有发票
     * 已支付状态 pstatus=R status=F
     * 退款状态 pstatus=S status={I-退款审核,R-退款中, C-退款已拒绝, F-已退款}
     * 取消状态 pstatus=C
     */
    public enum PrePayStatusEnum {
        PAY("P", "已支付"),
        REFUNDINIT("SI", "申请退款"),
        REFUNDING("SR", "退款中"),
        REFUNDCANCEL("SX", "退款拒绝"),
        REFUND("SF", "退款完成");
        //CANCEL("C", "已取消");
        private String desc;
        private String code;

        PrePayStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 伴手礼发货状态
     */
    public enum SendStatusEnum {
        INIT(0, "待备货"),
        READYSEND(1, "待发货"),
        TORECEIVE(2, "待收货"),
        RECEIVED(3, "已收货");
        private String desc;
        private Integer code;

        SendStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getStatus(Integer value) {
            SendStatusEnum[] sendStatus = SendStatusEnum.values();
            for (SendStatusEnum item : sendStatus) {
                if (item.code.equals(value)) {
                    return item.getDesc();
                }
            }
            return SendStatusEnum.INIT.getDesc();
        }

        public String getDesc() {
            return desc;
        }

        public Integer getCode() {
            return code;
        }
    }


    /**
     * 伴手礼退货状态
     */
    public enum GiftBackStatusEnum {
        INIT(0, "审核中"),
        CONFIRM(1, "审核通过"),
        RECEIVING(2, "退货接收中"),
        RECEIVED(3, "退货已签收"),
        REFUNDED(4, "已退款"),
        REFUSE(5, "已取消");
        private String desc;
        private Integer code;

        GiftBackStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getStatus(Integer value) {
            GiftBackStatusEnum[] sendStatus = GiftBackStatusEnum.values();
            for (GiftBackStatusEnum item : sendStatus) {
                if (item.code.equals(value)) {
                    return item.getDesc();
                }
            }
            return SendStatusEnum.INIT.getDesc();
        }

        public String getDesc() {
            return desc;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 伴手礼发货处理状态
     */
    public enum SendProgressEnum {
        PREPARE(0, "商品已备货"),
        SENDED(1, "商品已发货"),
        CONFIRM(2, "确认收货");
        private String desc;
        private Integer code;

        SendProgressEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }


        public String getDesc() {
            return desc;
        }

        public static String getDesc(Integer code) {
            SendProgressEnum[] progressEnums = SendProgressEnum.values();
            for (SendProgressEnum progress : progressEnums) {
                if (progress.getCode().equals(code)) {//|| value.startsWith(type.val) 2022.1.22 value.startWith 这段是因为老代码扣减时有 BUG.
                    return progress.getDesc();
                }
            }
            return SendProgressEnum.PREPARE.getDesc();
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 退款状态枚举  prepay表支付后申请
     */
    public enum RefundStatusEnum {
        INIT("I", "申请退款"),
        REFUND("R", "退款中"),
        CANCEL("C", "退款拒绝"),
        FINISH("F", "退款完成");
        private String desc;
        private String code;

        RefundStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    public class AuditingStatus {//审核状态
        public final static String EXPECTED = "N";//审核中
        public final static String CONFIRM = "O";//审核通过
        public final static String REFUSE = "X"; //拒绝
    }

    /**
     * 会场预约状态枚举
     */
    public enum MeetingRsStatusEnum {
        BOOKING("N", "未跟进"),
        FINISH("F", "已跟进");
        private String desc;
        private String code;

        MeetingRsStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    public class PassRsStatus {
        public final static String WAIT = "W";//待下发
        public final static String SENDED = "S";//已发起
        public final static String CONFIRM = "O";//已下发
        public final static String CANCEL = "C";//已取消
    }


    public class MeetingRsStatus {//会议预定状态
        public final static String BOOKING = "N";//预定 未跟进
        //public final static String START = "A";//开始 跟进
        public final static String FINISH = "F";//跟进
        //public final static String CANCEL = "C";//取消
    }

    public enum WriteOffStatusEnum {
        EXPECTED("N", "未核销"),
        FINISH("F", "已核销");
        private String desc;
        private String code;

        WriteOffStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String writeOffDesc(boolean lcheck) {
            if (lcheck) {
                return WriteOffStatusEnum.FINISH.getCode();
            } else {
                return WriteOffStatusEnum.EXPECTED.getCode();
            }
        }

        public String getDesc() {
            return desc;
        }

        public String getCode() {
            return code;
        }
    }

    public class ColRsStatus {//综合预定状态
        public final static String INITIAL = "N";//初始
        public final static String ARRIVAL = "A";//到店
        public final static String FINISH = "F";//完成
        public final static String CANCEL = "C";//取消
        public final static String NOSHOW = "H";//应到未到
    }


}
