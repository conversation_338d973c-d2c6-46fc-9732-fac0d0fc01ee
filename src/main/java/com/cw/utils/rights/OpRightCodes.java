package com.cw.utils.rights;

import cn.hutool.core.lang.ClassScanner;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.cw.pojo.common.MenuNodes;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 */
@Slf4j
public class OpRightCodes {

    /*****************操作权限***********************/
    //大组

    @Right(description = "创建栏目", type = RightType.OP)
    public static final int OP_MENU_SAVE = 10000;
    @Right(description = "更新菜单栏目", type = RightType.OP)
    public static final int OP_MENU_UPDATE = 10001;
    @Right(description = "删除菜单栏目", type = RightType.OP)
    public static final int OP_MENU_DELETE = 10002;

    static String writelist = "UserController";
    @Right(description = "菜单栏目列表", type = RightType.OP)
    public static final int OP_MENU_LIST = 10003;
    @Right(description = "查看菜单栏目", type = RightType.OP)
    public static final int OP_MENU_LOAD = 10004;
    @Right(description = "栏目列表排序", type = RightType.OP)
    public static final int OP_MENU_SEQ = 10005;
    @Right(description = "添加卡片内容", type = RightType.OP)
    public static final int OP_MENU_CONTENT_LOAD = 10006;
    @Right(description = "添加卡片内容", type = RightType.OP)
    public static final int OP_MENU_CONTENT_SAVE = 10007;
    @Right(description = "更新卡片内容", type = RightType.OP)
    public static final int OP_MENU_CONTENT_UPDATE = 10008;
    @Right(description = "删除卡片内容", type = RightType.OP)
    public static final int OP_MENU_CONTENT_DELETE = 10009;
    @Right(description = "查看卡片内容列表", type = RightType.OP)
    public static final int OP_MENU_CONTENT_LIST = 10010;
    @Right(description = "卡牌内容状态", type = RightType.OP)
    public static final int OP_MENU_CONTENT_STATUS = 10011;
    @Right(description = "卡片内容审核", type = RightType.OP)
    public static final int OP_MENU_CONTENT_AUDIT = 10012;
    @Right(description = "卡片内容排序", type = RightType.OP)
    public static final int OP_MENU_CONTENT_SEQ = 10013;
    @Right(description = "更新菜单栏目状态", type = RightType.OP)
    public static final int OP_MENU_STATUS = 10014;
    //公共数据 80800
    @Right(description = "查看资源数据列表", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_LIST = 80800;
    @Right(description = "查看资源数据", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_LOAD = 80801;
    @Right(description = "新建/编辑资源数据", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_SAVE = 80802;
    @Right(description = "删除资源数据", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_DELETE = 80803;
    @Right(description = "变更资源状态", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_STATUS = 80804;
    @Right(description = "更新资源排序", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_SEQ = 80805;
    @Right(description = "获取资源及子项集合", type = RightType.OP)
    public static final int OP_RESOURCE_DATA_CHILDREN = 80806;
    //房型设置 81800
    @Right(description = "查看房型列表", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_LIST = 81800;
    @Right(description = "查看房型", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_LOAD = 81801;
    @Right(description = "新建/编辑房型", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_SAVE = 81802;
    @Right(description = "删除房型", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_DELETE = 81803;
    @Right(description = "变更房型", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_STATUS = 81804;
    @Right(description = "获取房型库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_RESOURCE_LOAD = 81806;
    @Right(description = "获取房型库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_RMTYPE_RESOURCE_SAVE = 81807;
    //酒店设置 81900
    @Right(description = "查看酒店列表", type = RightType.OP)
    public static final int OP_CONFIG_HOTEL_LIST = 81900;
    @Right(description = "查看酒店", type = RightType.OP)
    public static final int OP_CONFIG_HOTEL_LOAD = 81901;
    @Right(description = "新建/编辑酒店", type = RightType.OP)
    public static final int OP_CONFIG_HOTEL_SAVE = 81902;
    @Right(description = "删除酒店", type = RightType.OP)
    public static final int OP_CONFIG_HOTEL_DELETE = 81903;
    @Right(description = "变更酒店状态", type = RightType.OP)
    public static final int OP_CONFIG_HOTEL_STATUS = 81904;

    //票务设置 82000
    @Right(description = "查看票务组列表", type = RightType.OP)
    public static final int OP_CONFIG_TICKETGROUP_LIST = 82000;
    @Right(description = "查看票务组", type = RightType.OP)
    public static final int OP_CONFIG_TICKETGROUP_LOAD = 82001;
    @Right(description = "新建/编辑票务组", type = RightType.OP)
    public static final int OP_CONFIG_TICKETGROUP_SAVE = 82002;
    @Right(description = "删除票务组", type = RightType.OP)
    public static final int OP_CONFIG_TICKETGROUP_DELETE = 82003;
    @Right(description = "变更票务组状态", type = RightType.OP)
    public static final int OP_CONFIG_TICKETGROUP_STATUS = 82004;
    @Right(description = "查看票务列表", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_LIST = 82005;
    @Right(description = "新建/编辑票务", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_LOAD = 82006;
    @Right(description = "新建/编辑票务", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_SAVE = 82007;
    @Right(description = "删除票务", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_DELETE = 82008;
    @Right(description = "变更票务状态", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_STATUS = 82009;
    @Right(description = "获取票务库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_RESOURCE_LOAD = 82010;
    @Right(description = "更新票务库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_TICKET_RESOURCE_SAVE = 82011;

    //预约设置 82100
    @Right(description = "查看预约项目列表", type = RightType.OP)
    public static final int OP_CONFIG_ACTGROUP_LIST = 82100;
    @Right(description = "查看预约项目", type = RightType.OP)
    public static final int OP_CONFIG_ACTGROUP_LOAD = 82101;
    @Right(description = "新建/编辑预约项目", type = RightType.OP)
    public static final int OP_CONFIG_ACTGROUP_SAVE = 82102;
    @Right(description = "删除预约项目", type = RightType.OP)
    public static final int OP_CONFIG_ACTGROUP_DELETE = 82103;
    @Right(description = "变更预约项目状态", type = RightType.OP)
    public static final int OP_CONFIG_ACTGROUP_STATUS = 82104;
    @Right(description = "查看预约场所列表", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_LIST = 82105;
    @Right(description = "查看预约场所", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_LOAD = 82106;
    @Right(description = "新建/编辑预约场所", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_SAVE = 82107;
    @Right(description = "删除预约场所", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_DELETE = 82108;
    @Right(description = "变更预约场所状态", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_STATUS = 82109;
    @Right(description = "获取预约场所库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_RESOURCE_LOAD = 82110;
    @Right(description = "更新预约场所库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_ACTSITE_RESOURCE_SAVE = 82111;
    @Right(description = "查看预约时段列表", type = RightType.OP)
    public static final int OP_CONFIG_ACTPERIOD_LIST = 82112;
    @Right(description = "查看预约时段", type = RightType.OP)
    public static final int OP_CONFIG_ACTPERIOD_LOAD = 82113;
    @Right(description = "新建/编辑预约时段", type = RightType.OP)
    public static final int OP_CONFIG_ACTPERIOD_SAVE = 82114;
    @Right(description = "删除预约时段", type = RightType.OP)
    public static final int OP_CONFIG_ACTPERIOD_DELETE = 82115;
    @Right(description = "查看组合项目列表", type = RightType.OP)
    public static final int OP_CONFIG_ACTQR_LIST = 82116;
    @Right(description = "查看组合项目", type = RightType.OP)
    public static final int OP_CONFIG_ACTQR_LOAD = 82117;
    @Right(description = "新建/编辑组合项目", type = RightType.OP)
    public static final int OP_CONFIG_ACTQR_SAVE = 82118;
    @Right(description = "删除组合项目", type = RightType.OP)
    public static final int OP_CONFIG_ACTSQR_DELETE = 82119;

    //演出节目 82130
    @Right(description = "查看演出节目列表", type = RightType.OP)
    public static final int OP_CONFIG_PERFORM_LIST = 82130;
    @Right(description = "查看演出节目", type = RightType.OP)
    public static final int OP_CONFIG_PERFORM_LOAD = 82131;
    @Right(description = "新建/编辑演出节目", type = RightType.OP)
    public static final int OP_CONFIG_PERFORM_SAVE = 82132;
    @Right(description = "删除演出节目", type = RightType.OP)
    public static final int OP_CONFIG_PERFORM_DELETE = 82133;
    @Right(description = "变更演出节目状态", type = RightType.OP)
    public static final int OP_CONFIG_PERFORM_STATUS = 82134;

    //会议设置 82200
    @Right(description = "查看会场大类列表", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGGROUP_LIST = 82200;
    @Right(description = "查看会场大类", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGGROUP_LOAD = 82201;
    @Right(description = "新建/编辑会场大类", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGGROUP_SAVE = 82202;
    @Right(description = "删除会场大类", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGGROUP_DELETE = 82203;
    @Right(description = "更新会场大类状态", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGGROUP_STATUS = 82204;
    @Right(description = "查看会议室列表", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGROOM_LIST = 82205;
    @Right(description = "查看会议室列表", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGROOM_LOAD = 82206;
    @Right(description = "新建/编辑会议室", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGROOM_SAVE = 82207;
    @Right(description = "删除会议室", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGROOM_DELETE = 8228;
    @Right(description = "更新会议室状态", type = RightType.OP)
    public static final int OP_CONFIG_MEETINGROOM_STATUS = 82209;

    //套餐设置 82300
    @Right(description = "查看套餐大类列表", type = RightType.OP)
    public static final int OP_CONFIG_KITGROUP_LIST = 82300;
    @Right(description = "查看套餐大类", type = RightType.OP)
    public static final int OP_CONFIG_KITGROUP_LOAD = 82301;
    @Right(description = "新建/编辑套餐大类", type = RightType.OP)
    public static final int OP_CONFIG_KITGROUP_SAVE = 82302;
    @Right(description = "删除套餐大类", type = RightType.OP)
    public static final int OP_CONFIG_KITGROUP_DELETE = 82303;
    @Right(description = "更改套餐大类状态", type = RightType.OP)
    public static final int OP_CONFIG_KITGROUP_STATUS = 82304;
    @Right(description = "查看套餐列表", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCTKIT_LIST = 82305;
    @Right(description = "查看套餐", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCTKIT_LOAD = 82306;
    @Right(description = "新建/编辑套餐", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCTKIT_SAVE = 82307;
    @Right(description = "删除套餐", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCTKIT_DELETE = 82308;
    @Right(description = "变更套餐状态", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCTKIT_STATUS = 82309;
    @Right(description = "获取套餐库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCTKIT_RESOURCE = 82310;
    @Right(description = "获取套餐小类详情列表", type = RightType.OP)
    public static final int OP_CONFIG_KITITEM_LIST = 82311;


    //产品设置82360
    @Right(description = "查看产品大类列表", type = RightType.OP)
    public static final int OP_CONFIG_PRODGROUP_LIST = 82360;
    @Right(description = "查看产品大类", type = RightType.OP)
    public static final int OP_CONFIG_PRODGROUP_LOAD = 82361;
    @Right(description = "新建/编辑产品大类", type = RightType.OP)
    public static final int OP_CONFIG_PRODGROUP_SAVE = 82362;
    @Right(description = "删除产品大类", type = RightType.OP)
    public static final int OP_CONFIG_PRODGROUP_DELETE = 82363;
    @Right(description = "更改产品大类状态", type = RightType.OP)
    public static final int OP_CONFIG_PRODGROUP_STATUS = 82364;
    @Right(description = "查看产品列表", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_LIST = 82365;
    @Right(description = "查看产品", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_LOAD = 82366;
    @Right(description = "新建/编辑产品", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_SAVE = 82367;
    @Right(description = "删除产品", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_DELETE = 82368;
    @Right(description = "变更产品状态", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_STATUS = 82369;
    @Right(description = "获取产品库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_RESOURCE_INDEX = 82370;
    @Right(description = "获取产品库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_PRODUCT_RESOURCE_SAVE = 82371;


    //伴手礼设置82380
    @Right(description = "查看伴手礼列表", type = RightType.OP)
    public static final int OP_CONFIG_GIFT_LIST = 82380;
    @Right(description = "查看伴手礼", type = RightType.OP)
    public static final int OP_CONFIG_GIFT_LOAD = 82381;
    @Right(description = "新建/编辑伴手礼", type = RightType.OP)
    public static final int OP_CONFIG_GIFT_SAVE = 82382;
    @Right(description = "删除伴手礼", type = RightType.OP)
    public static final int OP_CONFIG_GIFT_DELETE = 82383;
    @Right(description = "更改伴手礼状态", type = RightType.OP)
    public static final int OP_CONFIG_GIFT_STATUS = 82384;
    @Right(description = "查看商品规格列表", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_LIST = 82385;
    @Right(description = "查看商品规格", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_LOAD = 82386;
    @Right(description = "新建/编辑商品规格", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_SAVE = 82387;
    @Right(description = "删除商品规格", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_DELETE = 82388;
    @Right(description = "变更商品规格状态", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_STATUS = 82389;
    @Right(description = "获取商品规格库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_RESOURCE_INDEX = 82390;
    @Right(description = "设置商品规格库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_GIFTITEM_RESOURCE_SAVE = 82391;


    //自定义邮费
    @Right(description = "查看区域自定义邮费列表", type = RightType.OP)
    public static final int OP_CONFIG_CUSTOM_POSTAGE_LIST = 82394;
    @Right(description = "查看区域自定义邮费详情", type = RightType.OP)
    public static final int OP_CONFIG_CUSTOM_POSTAGE_LOAD = 82395;
    @Right(description = "新建/编辑区域自定义邮费", type = RightType.OP)
    public static final int OP_CONFIG_CUSTOM_POSTAGE_SAVE = 82396;
    @Right(description = "删除区域自定义邮费", type = RightType.OP)
    public static final int OP_CONFIG_CUSTOM_POSTAGE_DELETE = 82397;
    @Right(description = "获取系统运费配置", type = RightType.OP)
    public static final int OP_CONFIG_SYSTEM_POSTAGE_DELETE = 82398;


    //订单记录权限 82400
    @Right(description = "查看订单记录列表", type = RightType.OP)
    public static final int OP_ORDER_LIST = 82400;
    @Right(description = "订单记录详情", type = RightType.OP)
    public static final int OP_ORDER_DETAIL = 82401;
    @Right(description = "订单商品详情", type = RightType.OP)
    public static final int OP_ORDER_PRODUCT_DETAIL = 82402;
    @Right(description = "订单确定预定", type = RightType.OP)
    public static final int OP_ORDER_CONFIRM = 82403;
    @Right(description = "订单拒绝预定", type = RightType.OP)
    public static final int OP_ORDER_REFUSE = 82404;
    @Right(description = "订单拒绝预定(待确认)", type = RightType.OP)
    public static final int OP_ORDER_REFUSE_WAIT = 82405;
    @Right(description = "订单记录EXCEL导出", type = RightType.OP)
    public static final int OP_ORDER_EXCEL_OUTPUT = 82406;
    @Right(description = "订单历史记录详情", type = RightType.OP)
    public static final int OP_ORDER_HIS_DETAIL = 82407;
    @Right(description = "查看订单历史记录列表", type = RightType.OP)
    public static final int OP_ORDER_HIS_LIST = 82408;
    @Right(description = "订单拒绝预定", type = RightType.OP)
    public static final int OP_ORDER_WRITEOFF_CARD = 82409;

    @Right(description = "发送套餐券", type = RightType.OP)
    public static final int OP_ORDER_SEND_KITCOUPON = 82410;

    //订单记录权限 82450
    @Right(description = "查看综合预约记录列表", type = RightType.OP)
    public static final int OP_ACTORDER_LIST = 82450;
    @Right(description = "综合预约订单记录详情", type = RightType.OP)
    public static final int OP_ACTORDER_DETAIL = 82451;
    @Right(description = "取消综合预约", type = RightType.OP)
    public static final int OP_ACTORDER_CANCEL = 82452;
    @Right(description = "订单记录EXCEL导出", type = RightType.OP)
    public static final int OP_ACTORDER_EXCEL_OUTPUT = 82453;
    //@Right(description = "综合预约订单历史记录详情", type = RightType.OP)
    //public static final int OP_ACTORDERHIS_DETAIL = 82454;

    //线下订单记录权限 82410
    @Right(description = "查看线下订单记录列表", type = RightType.OP)
    public static final int OP_PASS_ORDER_LIST = 82410;
    @Right(description = "线下订单记录详情", type = RightType.OP)
    public static final int OP_PASS_ORDER_DETAIL = 82411;
    @Right(description = "更新线下订单状态", type = RightType.OP)
    public static final int OP_UPDATE_PASS_ORDER_STATUS = 82412;
    @Right(description = "线下订单退款", type = RightType.OP)
    public static final int OP_REFUND_PASS_ORDER = 82413;

    //订单退款审核权限 82420
    @Right(description = "订单退款审核列表", type = RightType.OP)
    public static final int OP_AUDITING_LIST = 82420;
    @Right(description = "订单退款审核历史列表", type = RightType.OP)
    public static final int OP_AUDITING_HIS_LIST = 82421;
    @Right(description = "审核通过", type = RightType.OP)
    public static final int OP_AUDITING_PASS = 82422;
    @Right(description = "审核拒绝", type = RightType.OP)
    public static final int OP_AUDITING_REFUSE = 82423;
    @Right(description = "审核强制退款", type = RightType.OP)
    public static final int OP_AUDITING_PASS_ENFORCE = 82424;
    @Right(description = "退款申请记录EXCEL导出", type = RightType.OP)
    public static final int OP_AUDITING_EXCEL_OUTPUT = 82425;
    @Right(description = "订单退款审核列表", type = RightType.OP)
    public static final int OP_AUDITING_DETAIL = 82426;
    @Right(description = "订单退款审核历史列表", type = RightType.OP)
    public static final int OP_AUDITING_HIS_DETAIL = 82427;
    @Right(description = "强制审核通过", type = RightType.OP)
    public static final int OP_AUDITING_FORCE_PASS = 82428;

    @Right(description = "完结订单退款 ", type = RightType.OP)
    public static final int OP_FINISH_REFUND = 82429;

    //预约记录权限 82440
    @Right(description = "查看预约记录列表", type = RightType.OP)
    public static final int OP_APPOINT_LIST = 82440;
    @Right(description = "获取预约详情", type = RightType.OP)
    public static final int OP_APPOINT_LOAD = 82441;
    @Right(description = "确定预约", type = RightType.OP)
    public static final int OP_APPOINT_CONFIRM = 82442;
    //发票权限 82460
    @Right(description = "查看发票记录列表", type = RightType.OP)
    public static final int OP_INVOICE_LIST = 82460;
    @Right(description = "发票红冲", type = RightType.OP)
    public static final int OP_INVOICE_RED = 82461;
    @Right(description = "开票重试", type = RightType.OP)
    public static final int OP_INVOICE_REINVOICE = 82462;
    @Right(description = "发票详情", type = RightType.OP)
    public static final int OP_INVOICE_LOAD = 82463;
    @Right(description = "发票手动确认", type = RightType.OP)
    public static final int OP_INVOICE_CONFIRM = 82464;
    //规则权限 82500
    @Right(description = "查看规则列表", type = RightType.OP)
    public static final int OP_RULE_LIST = 82500;
    @Right(description = "获取规则", type = RightType.OP)
    public static final int OP_RULE_LOAD = 82501;
    @Right(description = "保存规则", type = RightType.OP)
    public static final int OP_RULE_SAVE = 82502;
    @Right(description = "删除规则", type = RightType.OP)
    public static final int OP_RULE_DELETE = 82503;
    @Right(description = "规则使用对象", type = RightType.OP)
    public static final int OP_RULE_USING = 82504;
    @Right(description = "变更规则状态", type = RightType.OP)
    public static final int OP_RULE_STATUS = 82505;


    @Right(description = "查看策略列表", type = RightType.OP)
    public static final int OP_RULESPOLICY_LIST = 82510;
    @Right(description = "获取策略", type = RightType.OP)
    public static final int OP_RULESPOLICY_LOAD = 82511;
    @Right(description = "保存策略", type = RightType.OP)
    public static final int OP_RULESPOLICY_save = 82513;
    @Right(description = "删除策略", type = RightType.OP)
    public static final int OP_RULESPOLICY_DELETE = 82514;
    //意见反馈 82600
    @Right(description = "查看意见反馈列表", type = RightType.OP)
    public static final int OP_FEEDBACK_LIST = 82600;
    @Right(description = "获取反馈意见详情", type = RightType.OP)
    public static final int OP_FEEDBACK_LOAD = 82601;
    @Right(description = "跟进反馈意见", type = RightType.OP)
    public static final int OP_FEEDBACK_CONFIRM = 82602;
    @Right(description = "删除反馈意见", type = RightType.OP)
    public static final int OP_FEEDBACK_DELETE = 82603;
    @Right(description = "反馈意见记录EXCEL导出", type = RightType.OP)
    public static final int OP_FEEDBACK_EXCEL_OUTPUT = 82404;

    //模板 82700
    @Right(description = "查看短信模板列表", type = RightType.OP)
    public static final int OP_CONFIG_TEMPLATE_LIST = 82700;
    @Right(description = "获取短信模板", type = RightType.OP)
    public static final int OP_CONFIG_TEMPLATE_LOAD = 82701;
    @Right(description = "保存短信模板", type = RightType.OP)
    public static final int OP_CONFIG_TEMPLATE_SAVE = 82702;
    @Right(description = "删除短信模板", type = RightType.OP)
    public static final int OP_CONFIG_TEMPLATE_DELETE = 82703;
    @Right(description = "启用/停用短信模板", type = RightType.OP)
    public static final int OP_CONFIG_TEMPLATE_STATUS = 82704;


    @Right(description = "查看小程序通知列表", type = RightType.OP)
    public static final int OP_CONFIG_WXTEMPLATE_LIST = 82705;
    @Right(description = "获取小程序通知", type = RightType.OP)
    public static final int OP_CONFIG_WXTEMPLATE_LOAD = 82706;
    @Right(description = "保存小程序通知", type = RightType.OP)
    public static final int OP_CONFIG_WXTEMPLATE_SAVE = 82707;
    @Right(description = "删除小程序通知", type = RightType.OP)
    public static final int OP_CONFIG_WXTEMPLATE_DELETE = 82708;
    @Right(description = "启用/停用小程序通知", type = RightType.OP)
    public static final int OP_CONFIG_WXTEMPLATE_STATUS = 82709;

    //发货管理 82800
    @Right(description = "查看伴手礼订单列表", type = RightType.OP)
    public static final int OP_GIFT_ORDER_LIST = 82800;
    @Right(description = "发货流程", type = RightType.OP)
    public static final int OP_GIFT_ORDER_HANDLE_SAVE = 82801;
    @Right(description = "获取发货伴手礼信息", type = RightType.OP)
    public static final int OP_GIFT_ORDER_DETAIL = 82802;
    @Right(description = "导出发货管理EXCEL", type = RightType.OP)
    public static final int OP_GIFT_ORDER_OUTPUT = 82803;

    //退货管理 82810
    @Right(description = "查看退货申请订单列表", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_LIST = 82810;
    @Right(description = "获取退货申请详情", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_DETAIL = 82811;
    @Right(description = "退货申请订单同意退货", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_CONFIRM = 82812;
    @Right(description = "退货申请订单拒绝退货", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_REFUSE = 82813;
    @Right(description = "退货申请订单接收退货", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_RECIEVE = 82813;
    @Right(description = "退货申请记录导出excel", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_OUTPUT = 82813;
    @Right(description = "获取退货物流信息", type = RightType.OP)
    public static final int OP_GIFT_AUDIT_ORDER_POSTAGEINFO = 82813;

    //统计数据
    @Right(description = "首页统计数据", type = RightType.OP)
    public static final int OP_INDEX_DATA = 82900;


    //通知机器人 83300
    @Right(description = "查看机器人列表", type = RightType.OP)
    public static final int OP_CONFIG_ROBOT_LIST = 83300;
    @Right(description = "查看机器人机器人", type = RightType.OP)
    public static final int OP_CONFIG_ROBOT_LOAD = 83301;
    @Right(description = "新建/编辑机器人", type = RightType.OP)
    public static final int OP_CONFIG_ROBOT_SAVE = 83302;
    @Right(description = "删除机器人", type = RightType.OP)
    public static final int OP_CONFIG_ROBOT_DELETE = 83303;
    @Right(description = "测试机器人通知", type = RightType.OP)
    public static final int OP_CONFIG_ROBOT_TEST = 83304;
    @Right(description = "改变机器人状态", type = RightType.OP)
    public static final int OP_CONFIG_ROBOT_STATUS = 83305;


    //地图业态规划
    @Right(description = "查看园区列表", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAK_LIST = 83400;
    @Right(description = "查看园区数据", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAK_LOAD = 83401;
    @Right(description = "新建/编辑园区", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAK_SAVE = 83402;
    @Right(description = "删除园区", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAK_DELETE = 83403;
    @Right(description = "获取业态规划列表", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITE_LIST = 83404;
    @Right(description = "获取业态标注", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITE_LOAD = 83405;
    @Right(description = "新建/编辑园区业态标注", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITE_SAVE = 83406;
    @Right(description = "删除业态标注", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITE_DELETE = 83407;
    @Right(description = "更新业态标注状态", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITE_STATUS = 83408;
    @Right(description = "业态标注快速导入", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITE_INPORT = 83409;
    @Right(description = "获取路线规划列表", type = RightType.OP)
    public static final int OP_CONFIG_MAP_TRAVELTIP_LIST = 83410;
    @Right(description = "获取路线", type = RightType.OP)
    public static final int OP_CONFIG_MAP_TRAVELTIP_LOAD = 83411;
    @Right(description = "新建/编辑路线", type = RightType.OP)
    public static final int OP_CONFIG_MAP_TRAVELTIP_SAVE = 83412;
    @Right(description = "删除路线", type = RightType.OP)
    public static final int OP_CONFIG_MAP_TRAVELTIP_DELETE = 83413;
    @Right(description = "更新路线状态", type = RightType.OP)
    public static final int OP_CONFIG_MAP_TRAVELTIP_STATUS = 83414;
    @Right(description = "获取业态标注服务设施", type = RightType.OP)
    public static final int OP_CONFIG_MAP_PRAKSITEKIT_LOAD = 83415;


    //同步PMS数据 83500
    @Right(description = "同步PMS数据", type = RightType.OP)
    public static final int OP_SYNC_PMS = 83500;
    static String unkonwn = "UNKNOWN";
    static List<MenuNodes> menuNodesList = null;
    static Map<String, MenuNodes> menuNodesMap = new LinkedHashMap<>();
    static Map<Integer, String> rightDscMap = Maps.newHashMap();


    //夜审管理 83600
    @Right(description = "查看夜审配置和参数", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_LIST = 83600;
    @Right(description = "查看夜审参数", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_PARAM_LOAD = 83601;
    @Right(description = "保存夜审配置", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_CONFIG_SAVE = 83602;
    @Right(description = "保存夜审配参数", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_PARAM_SAVE = 83603;
    @Right(description = "删除夜审参数", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_PARAM_DELETE = 83604;
    @Right(description = "获取夜审状态", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_INFO = 83605;
    @Right(description = "重设夜审", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_RESET = 83606;
    @Right(description = "查看夜审日志", type = RightType.OP)
    public static final int OP_SYSCONFIG_NA_LOG = 83607;

    //用户
    //用户 83700
    @Right(description = "查看用户列表", type = RightType.OP)
    public static final int OP_SYSCONFIG_USER_LIST = 83700;
    @Right(description = "查看用户", type = RightType.OP)
    public static final int OP_SYSCONFIG_USER_LOAD = 83701;
    @Right(description = "新建/编辑用户", type = RightType.OP)
    public static final int OP_SYSCONFIG_USER_SAVE = 83702;
    @Right(description = "删除用户", type = RightType.OP)
    public static final int OP_SYSCONFIG_USER_DELETE = 83703;
    @Right(description = "更改密码", type = RightType.OP)
    public static final int OP_SYSCONFIG_USER_PASSWORD = 83704;
    @Right(description = "更改用户状态", type = RightType.OP)
    public static final int OP_SYSCONFIG_USER_STAUS = 83705;

    //角色权限 83800
    @Right(description = "查看用户角色列表", type = RightType.OP)
    public static final int OP_SYSCONFIG_ROLE_LIST = 83800;
    @Right(description = "查看用户角色", type = RightType.OP)
    public static final int OP_SYSCONFIG_ROLE_LOAD = 83801;
    @Right(description = "新建/编辑用户角色", type = RightType.OP)
    public static final int OP_SYSCONFIG_ROLE_SAVE = 83802;
    @Right(description = "删除用户角色", type = RightType.OP)
    public static final int OP_SYSCONFIG_ROLE_DELETE = 83803;
    @Right(description = "查看角色权限列表", type = RightType.OP)
    public static final int OP_SYSCONFIG_RIGHT_LIST = 83804;
    @Right(description = "保存权限", type = RightType.OP)
    public static final int OP_SYSCONFIG_RIGHT_SAVE = 83805;

    //系统设置,更新排序,上传图片 850000
    @Right(description = "加载系统设置", type = RightType.OP)
    public static final int OP_SYSCONFIG_SYSCONF_LOAD = 850000;
    @Right(description = "保存系统设置", type = RightType.OP)
    public static final int OP_SYSCONFIG_SYSCONF_SAVE = 850001;
    @Right(description = "更新表头排序", type = RightType.OP)
    public static final int OP_SYSCONFIG_CHANGEORDER = 850002;
    @Right(description = "上传图片/多媒体文件", type = RightType.OP)
    public static final int OP_SYSCONFIG_UPLOADFILE = 850003;
    @Right(description = "删除图片/多媒体文件", type = RightType.OP)
    public static final int OP_SYSCONFIG_DELFILE = 850004;
    @Right(description = "更新表头排序", type = RightType.OP)
    public static final int OP_SYSCONFIG_SYSCONF_BASE_SAVE = 850005;

    @Right(description = "加载读取厂商配置", type = RightType.OP)
    public static final int OP_VENDOR_LOAD = 851000;
    @Right(description = "保存厂商设置", type = RightType.OP)
    public static final int OP_VENDOR_SAVE = 851001;
    @Right(description = "删除厂商配置", type = RightType.OP)
    public static final int OP_VENDOR_DELETE = 851002;
    @Right(description = "应用厂商配置", type = RightType.OP)
    public static final int OP_VENDOR_APPLY = 851003;


    @Right(description = "加载网站页脚设置", type = RightType.OP)
    public static final int OP_WEBCONF_LOAD = 852000;
    @Right(description = "保存网站页脚设置", type = RightType.OP)
    public static final int OP_WEBCONF_SAVE = 852001;
    @Right(description = "删除网站页脚设置", type = RightType.OP)
    public static final int OP_WEBCONF_DELETE = 852002;

    //优惠券权限
    //产品设置853000
    @Right(description = "查看优惠券大类列表", type = RightType.OP)
    public static final int OP_CONFIG_COUPONGROUP_LIST = 853000;
    @Right(description = "查看优惠券大类", type = RightType.OP)
    public static final int OP_CONFIG_COUPONGROUP_LOAD = 853001;
    @Right(description = "新建/编辑优惠券大类", type = RightType.OP)
    public static final int OP_CONFIG_COUPONGROUP_SAVE = 853002;
    @Right(description = "删除优惠券大类", type = RightType.OP)
    public static final int OP_CONFIG_COUPONGROUP_DELETE = 853003;
    @Right(description = "更改优惠券大类状态", type = RightType.OP)
    public static final int OP_CONFIG_COUPONGROUP_STATUS = 853004;

    @Right(description = "查看优惠券列表", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_LIST = 853005;
    @Right(description = "查看优惠券", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_LOAD = 853006;
    @Right(description = "新建/编辑优惠券", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_SAVE = 853007;
    @Right(description = "删除优惠券", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_DELETE = 853008;
    @Right(description = "更改优惠券状态", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_STATUS = 853009;

    @Right(description = "获取产品库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_RESOURCE_INDEX = 853010;
    @Right(description = "获取产品库存&价格", type = RightType.OP)
    public static final int OP_CONFIG_COUPON_RESOURCE_SAVE = 853011;

    @Right(description = "查看用户优惠券列表", type = RightType.OP)
    public static final int OP_CONFIG_USERCOUPON_LIST = 853012;
    @Right(description = "查看用户优惠券", type = RightType.OP)
    public static final int OP_CONFIG_USERCOUPON_LOAD = 8530013;
    @Right(description = "核销用户优惠券", type = RightType.OP)
    public static final int OP_CONFIG_USERCOUPON_CHECK = 853014;
    @Right(description = "统计优惠券使用情况", type = RightType.OP)
    public static final int OP_CONFIG_COUPONGROUP_STATI = 853015;


    //产品设置854000
    @Right(description = "查看大赛列表", type = RightType.OP)
    public static final int OP_CONFIG_ONLINECONTEST_LIST = 854000;
    @Right(description = "查看大赛", type = RightType.OP)
    public static final int OP_CONFIG_ONLINECONTEST_LOAD = 854001;
    @Right(description = "新建/编辑大赛", type = RightType.OP)
    public static final int OP_CONFIG_ONLINECONTEST_SAVE = 854002;
    @Right(description = "删除大赛", type = RightType.OP)
    public static final int OP_CONFIG_ONLINECONTEST_DELETE = 854003;
    @Right(description = "更改大赛状态", type = RightType.OP)
    public static final int OP_CONFIG_ONLINECONTEST_STATUS = 854004;

    @Right(description = "查看投稿作品列表", type = RightType.OP)
    public static final int OP_CONFIG_CONTESTENTRY_LIST = 854010;
    @Right(description = "查看投稿作品", type = RightType.OP)
    public static final int OP_CONFIG_CONTESTENTRY_LOAD = 854011;
    //@Right(description = "新建/编辑投稿作品", type = RightType.OP)
    //public static final int OP_CONFIG_CONTESTENTRY_SAVE = 854012;
    @Right(description = "删除投稿作品", type = RightType.OP)
    public static final int OP_CONFIG_CONTESTENTRY_DELETE = 854013;
    @Right(description = "投稿作品审核通过", type = RightType.OP)
    public static final int OP_CONFIG_CONTESTENTRY_CONFIRM = 854014;
    @Right(description = "投稿作品审核拒绝", type = RightType.OP)
    public static final int OP_CONFIG_CONTESTENTRY_REFUSE = 854015;


    //站点管理 855000
    @Right(description = "查看站点列表", type = RightType.OP)
    public static final int OP_SHOPSITE_LIST = 855000;
    @Right(description = "获取站点", type = RightType.OP)
    public static final int OP_SHOPSITE_LOAD = 855001;
    @Right(description = "保存站点", type = RightType.OP)
    public static final int OP_SHOPSITE_SAVE = 855002;
    @Right(description = "删除站点", type = RightType.OP)
    public static final int OP_SHOPSITE_DELETE = 855003;
    @Right(description = "变更站点状态", type = RightType.OP)
    public static final int OP_SHOPSITE_STATUS = 855004;

    //行李寄存
    @Right(description = "查看行李", type = RightType.OP)
    public static final int OP_LUGGAGE_VIEW = 856000;
    @Right(description = "行李管理", type = RightType.OP)
    public static final int OP_LUGGAGE_UPD = 856001;


    private static final long serialVersionUID = 1L;
    private static List<RightCode> oprights = new ArrayList<>();
    /*private static List<RightCode> viewrights = new ArrayList<>();
    private static List<RightCode> apirights = new ArrayList<>();*/

    static {
        initArray();
    }

    private static void initArray() {
        oprights = new ArrayList<RightCode>();
     /*   viewrights = new ArrayList<RightCode>();
        apirights = new ArrayList<RightCode>();*/
        Comparator<RightCode> comparator = (o1, o2) -> o1.getCode().compareTo(o2.getCode());

        Field[] fs = OpRightCodes.class.getDeclaredFields();
        for (Field field : fs) {
            Right r = field.getAnnotation(Right.class);
            if (r != null) {
                String val = null;
                try {
                    val = field.get(null).toString();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                if (val != null) {
                    RightCode rightCode = new RightCode(r, val);
                    oprights.add(rightCode);
                    rightDscMap.put(Integer.parseInt(val), r.description());
                }
            }
        }
        oprights.sort(comparator);

    }

    private String rightVal(int right) {
        return right + "";
    }


    //订单管理

    /**
     * @return 获取所有前端显示权限
     */
    public static List<RightCode> getAllOpRights() {
        return oprights;
    }

   /* public static List<RightCode> getAllViewrights() {
        return viewrights;
    }

    public static List<RightCode> getAllApirights() {
        return apirights;
    }*/

    public static String getRightDsc(int right) {
        return rightDscMap.getOrDefault(right, unkonwn);
    }


    public static List<MenuNodes> getAllOpMenus(boolean outputLog) {
        if (menuNodesList == null) {
            menuNodesList = new ArrayList<>();
            List<String> ignoreMethods = new ArrayList<>();
            AtomicInteger id = new AtomicInteger(1);

            MenuNodes pubNode = new MenuNodes("系统权限", "系统权限".hashCode() + ""); //对于出现多次的操作权限.比如排序.置顶等.丢到这里

            Set<Integer> allOpRights = new HashSet<>();
            Set<Integer> pubOpRights = new HashSet<>();

            ClassScanner.scanAllPackageByAnnotation("com.cw.controller.config", RestController.class).forEach(clazz -> {
                List<String> methods = new ArrayList<>();
                try {
                    Method[] declaredMethods = clazz.getDeclaredMethods();
                    for (Method method : declaredMethods) {
                        RequireOpRight requireOpRight = method.getAnnotation(RequireOpRight.class);
                        if (requireOpRight != null) {
                            methods.add(method.getName());
                            if (allOpRights.contains(requireOpRight.opRight())) {
                                pubOpRights.add(requireOpRight.opRight());   //加过的作为公共权限
                            } else {
                                allOpRights.add(requireOpRight.opRight());  //没加过的记录一下
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            ClassScanner.scanAllPackageByAnnotation("com.cw.controller.config", RestController.class).forEach(clazz -> {
                String bathPath = "";
                RequestMapping requestMapping = clazz.getAnnotation(RequestMapping.class);
                if (requestMapping != null) {
                    bathPath = requestMapping.path().length > 0 ? requestMapping.path()[0] : "";//获取路径
                } else {
                    PostMapping postMapping = clazz.getAnnotation(PostMapping.class);//后台一律 Post 请求
                    bathPath = postMapping.path().length > 0 ? postMapping.path()[0] : "";//获取路径

                }
                Api api = clazz.getAnnotation(Api.class);
                List<String> methods = new ArrayList<>();
                String controllerDesc = api.tags().length > 0 ? api.tags()[0] : "";
                MenuNodes controllerNode = new MenuNodes(controllerDesc, Math.abs(clazz.getSimpleName().hashCode()) + "");

                try {
                    Method[] declaredMethods = clazz.getDeclaredMethods();
                    for (Method method : declaredMethods) {
                        RequireOpRight requireOpRight = method.getAnnotation(RequireOpRight.class);
                        ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
                        String methodDesc = apiOperation != null ? apiOperation.value() : method.getName();
                        if (requireOpRight != null) {
                            methods.add(method.getName());
                            //暂时不判断权限是否设置重复
                            Integer opRight = requireOpRight.opRight();
                            controllerNode.addMenu(new MenuNodes(methodDesc, opRight + ""));
                        } else if (!writelist.contains(clazz.getSimpleName())) {
                            ignoreMethods.add(StringUtils.rightPad(clazz.getSimpleName(), 25) + StringUtils.rightPad(methodDesc.trim(), 35, "")
                                    + "方法--->" + StringUtils.rightPad(method.getName(), 25));
                        }
                    }
                    if (ArrayUtil.isNotEmpty(controllerNode.getChildren())) {
                        //判断是否同一个label 合并数据
                        if (menuNodesMap.containsKey(controllerDesc)) {
                            menuNodesMap.get(controllerDesc).addChildren(controllerNode.getChildren());
                        } else {
                            menuNodesMap.put(controllerDesc, controllerNode);
                        }
                        //menuNodesList.add(controllerNode);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
//            if(pubOpRights.size()>0){
//                for (Integer pubOpRight : pubOpRights) {
//                    pubNode.addMenu(new MenuNodes(OpRightCodes.getRightDsc(pubOpRight),pubOpRight));
//                }
//                menuNodesList.add(pubNode);
//            }
            //map赋值
            if (menuNodesMap != null && !menuNodesMap.isEmpty()) {
                for (String key : menuNodesMap.keySet()) {
                    menuNodesList.add(menuNodesMap.get(key));
                }
                //排序
                menuNodesList.sort(Comparator.comparing(MenuNodes::getRightcode).reversed());
            }

            if (outputLog) {
                log.info("权限结构：\n{}", JSON.toJSONString(menuNodesList, true));
                log.info("漏加权限判断的方法{}个:", ignoreMethods.size());
                for (int i = 0; i < ignoreMethods.size(); i++) {
                    System.out.println(ignoreMethods.get(i));
                }
            }
        }
        return menuNodesList;
    }


}
