package com.cw.outsys.pojo.sms.request.gsms;

import com.cw.outsys.api.BaseOutSysRequest;
import com.cw.outsys.api.OutSysApi;
import com.cw.outsys.pojo.sms.resonse.gsms.GsmsSendMsgResponse;
import com.cw.outsys.stdop.request.StdSmsSendMsgRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.http.HttpMethod;

import java.nio.charset.Charset;

/**
 * @Describe 高斯通发送短信
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2024-11-15
 */
@Data
@OutSysApi(description = " 发送短信", path = "/GsmsHttp", reqMethod = HttpMethod.GET)
public class GsmsSendMsgRequest extends BaseOutSysRequest<GsmsSendMsgResponse, StdSmsSendMsgRequest> {
    @ApiModelProperty(notes = "手机号码，用逗号分割", required = true)
    String to;
    @ApiModelProperty(notes = "短信内容(只支持GBK编码,若使用其它编码需要转换一下),如果是语音短信类型，则是语音验证码")
    String content;

    String prefix = "";


    @Override
    public GsmsSendMsgRequest transfer(StdSmsSendMsgRequest stdSmsSendMsgRequest) {
        this.setTo(stdSmsSendMsgRequest.getMobile());
        this.setContent(stdSmsSendMsgRequest.getContent());
        //判断是否使用区号
        if (isInternationalMobile(stdSmsSendMsgRequest.getMobile())) {
            //是否去掉手机号码 +号开头或者00和区号
            this.setPrefix("00");
        }else {
            this.setPrefix("86");
            //如果国内手机号码00开头或者0086开头
            if (this.to.startsWith("0086")) {
                this.setTo(this.to.replace("0086",""));
            }else if (this.to.startsWith("00")) {
                this.setTo(this.to.replace("00",""));
            }else if (this.to.startsWith("+86")) {
                this.setTo(this.to.replace("+86",""));
            }
        }
        return this;
    }

    /**
     * 判断是否国外号码
     * @param mobile
     * @return
     */
    private boolean isInternationalMobile(String mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return false;
        }
        // 以+开头的国际格式号码
        if (mobile.startsWith("+")) {
            return !mobile.startsWith("+86");
        }

        // 判断是否以00开头（国际前缀）
        if (mobile.startsWith("00")) {
            // 提取区号部分（从第3位到第4-5位，常见区号长度）
            String countryCode = extractCountryCode(mobile);
            // 检查是否为国内区号（这里以中国86为例）
            // return !"86".equals(countryCode);
            return !countryCode.startsWith("86");
        }

        // 不以+/00开头，默认为国内号码
        return false;
    }

    private static String extractCountryCode(String mobile) {
        // 提取可能的区号部分（2-5位数字）
        // 常见格式：00+区号+电话号码
        int endIndex = Math.min(mobile.length(), 5); // 最多取前5位
        // 从第3位开始查找连续数字作为区号
        for (int i = 3; i < endIndex; i++) {
            if (!Character.isDigit(mobile.charAt(i))) {
                return mobile.substring(2, i);
            }
        }
        // 如果没有非数字字符，返回截取的部分
        return mobile.substring(2, endIndex);
    }

    @Override
    public Class<GsmsSendMsgResponse> getResponseClass() {
        return GsmsSendMsgResponse.class;
    }

    /**
     * @param str
     * @return 转换GBK字符
     */
    private String transGBKStr(String str) {
        byte[] gbkBytes = str.getBytes(Charset.forName("GBK"));
        return new String(gbkBytes, Charset.forName("GBK"));
    }
}
