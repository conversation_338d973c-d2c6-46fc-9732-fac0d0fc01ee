package com.cw.controller.app;

import com.cw.config.confyaml.node.Conf_Map;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.*;
import com.cw.pojo.dto.app.res.*;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Select_Req;
import com.cw.pojo.dto.common.res.Common_Pic_Res;
import com.cw.pojo.dto.common.res.Common_Select_Res;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.req.sys.GenQrReq;
import com.cw.pojo.dto.conf.req.sys.SysconfReq;
import com.cw.pojo.dto.conf.res.cms.MenuConfigRes;
import com.cw.pojo.dto.conf.res.cms.PageFoot;
import com.cw.pojo.dto.conf.res.cms.PageHeader;
import com.cw.service.app.AppActService;
import com.cw.service.app.AppResource2Service;
import com.cw.service.app.AppResourceService;
import com.cw.service.app.AppSupService;
import com.cw.service.config.cms.MenuService;
import com.cw.service.config.common.CustomDataService;
import com.cw.service.config.sys.ManagerToolService;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.service.order.ContestService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.enums.menus.ProductQrcodePathFactory;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = SwaggerUtil.MainIndex.APP)
@RestController
@RequestMapping(value = "/webapi/resource", method = RequestMethod.POST)
public class AppResourceController {

    @Autowired
    AppResourceService resourceService;

    @Autowired
    AppResource2Service resourceService2;

    @Autowired
    AppActService appActService;

    @Autowired
    ContestService contestService;

    @Autowired
    AppSupService appSupService;

    @Autowired
    CustomDataService customDataService;
    @Autowired
    MenuService menuService;

    @Autowired
    ManagerToolService managerToolService;

    @RequestMapping(value = "/qrcode", method = RequestMethod.POST)
    public byte[] getWxqrcode() {
        return new byte[]{};
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取酒店列表", notes = "酒店列表")
    @RequestMapping(value = "/hotelList", method = RequestMethod.POST)
    public ResultJson<AppHotelListRes> getHotelList(@Valid @RequestBody AppHotelListReq req) {
        AppHotelListRes res = resourceService.getHotelList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取酒店下房型列表", notes = "房型列表")
    @RequestMapping(value = "/roomtypeList", method = RequestMethod.POST)
    public ResultJson<AppRoomProductListRes> getRoomTypeList(@Valid @RequestBody AppProductDetailReq req) {
        AppRoomProductListRes res = resourceService.getRoomTypeList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取景区商品大组列表", notes = "商品大组列表")
    @RequestMapping(value = "/spuGroupList", method = RequestMethod.POST)
    public ResultJson<AppSpuGroupRes> getSpuGroupList(@Valid @RequestBody AppProductGroupReq req) {
        AppSpuGroupRes res = resourceService.getSpuGroupList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取景区商品大组下产品列表", notes = "商品大组产品列表")
    @RequestMapping(value = "/spuList", method = RequestMethod.POST)
    public ResultJson<AppSpuProductListRes> getSpuList(@Valid @RequestBody AppProductDetailReq req) {
        AppSpuProductListRes res = resourceService.getSpuList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取门票大组", notes = " 门票大组列表")
    @RequestMapping(value = "/ticketgroupList", method = RequestMethod.POST)
    public ResultJson<AppProductGroupRes> getTicketGroupList(@Valid @RequestBody AppProductGroupReq req) {
        AppProductGroupRes res = resourceService.getTicketGroupList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取门票组下门票", notes = " 门票列表")
    @RequestMapping(value = "/ticketList", method = RequestMethod.POST)
    public ResultJson<AppTicketProductListRes> getTicketList(@Valid @RequestBody AppProductDetailReq req) {
        AppTicketProductListRes res = resourceService.getTicketList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取套餐大组", notes = " 获取套餐大组")
    @RequestMapping(value = "/kitgroupList")
    public ResultJson<AppProductGroupRes> getKitsGroup(@Valid @RequestBody AppKitListReq req) {
        AppProductGroupRes res = resourceService.getKitList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取套餐明细列表", notes = "获取套餐列表")
    @RequestMapping(value = "/kitdetailList")
    public ResultJson<AppKitProductListRes> getKitDetailList(@Valid @RequestBody AppProductDetailReq req) {
        AppKitProductListRes res = resourceService.getKitdetailList(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取会场列表", notes = "获取会场列表")
    @RequestMapping(value = "/meetingGroupList", method = RequestMethod.POST)
    public ResultJson<AppProductGroupRes> getMeeting(@Valid @RequestBody AppMeetingGroupListReq req) {
        AppProductGroupRes res = resourceService.getMeetingGroupList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-   获取会场下会议室列表", notes = "会议室列表")
    @RequestMapping(value = "/meetingList")
    public ResultJson<AppMeetingRoomListRes> getMeetingRoomList(@Valid @RequestBody AppProductDetailReq req) {
        AppMeetingRoomListRes res = resourceService.getMeetingList(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取产品价格库存明细", notes = "获取产品价格库存明细")
    @RequestMapping(value = "/productdetail")
    public ResultJson<List<AppProductAvailItem>> getProductDetail(@Valid @RequestBody AppProductQueryAvailReq req) {
        List<AppProductAvailItem> res = resourceService.getProductAvailDetail(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取产品免费取消时间", notes = "获取产品免费取消时间")
    @RequestMapping(value = "/getfreecanceltime")
    public ResultJson<Common_response> getFreeCancelTime(@Valid @RequestBody AppProductQueryAvailReq req) {
        Common_response res = resourceService.getFreeCancelTime(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取产品预订须知富文本", notes = "获取产品预订须知富文本")
    @RequestMapping(value = "/getNoticeText")
    public ResultJson<AppOrderNoticeTextRes> getProductNoticeText(@Valid @RequestBody AppProductQueryReq req) {
        AppOrderNoticeTextRes res = resourceService.getProductNoticeText(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  计算套餐产品价格", notes = "计算套餐产品价格")
    @RequestMapping(value = "/calcKitPrice")
    public ResultJson<AppCalcKitDetailRes> calcKitPrice(@Valid @RequestBody AppCalcKitDetailReq req) {
        AppCalcKitDetailRes res = resourceService.calcKitPrice(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端- 获取酒店信息", notes = " 获取酒店配套轮播图与介绍")
    @RequestMapping(value = "/hotelIntro")
    public ResultJson<AppHotelIntroRes> getHotelIntro(@Valid @RequestBody AppProductIntroReq req) {
        AppHotelIntroRes res = resourceService.getHotelIntro(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端- 获取客房配套轮播图与介绍", notes = "小程序- 获取产品配套轮播图与介绍")
    @RequestMapping(value = "/roomIntro")
    public ResultJson<AppProductIntroRes> getRoomInro(@Valid @RequestBody AppProductIntroReq req) {
        AppProductIntroRes res = resourceService.getRoomProductIntro(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端- 获取会议室配套轮播图与介绍", notes = "小程序- 获取产品配套轮播图与介绍")
    @RequestMapping(value = "/meetIntro")
    public ResultJson<AppProductIntroRes> getMeetingInro(@Valid @RequestBody AppProductIntroReq req) {
        AppProductIntroRes res = resourceService.getMeetingProductIntro(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取额外服务选项", notes = "获取额外服务选项")
    @RequestMapping(value = "/extraServices")
    public ResultJson<AppExtraServiceRes> getExtraServices(@Valid @RequestBody AppProductDetailReq req) {
        AppExtraServiceRes res = resourceService.getExtraServices(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.SELECT_VALUE_INDEX)
    @ApiOperation(value = "获取下拉框数据", notes = "APP端获取菜单选项数据.例如酒店主题.会议需求的下拉选择项")
    @RequestMapping(value = "/select_data")
    public ResultJson<List<Common_Select_Res>> select_data(@RequestBody Common_Select_Req req) {
        return ResultJson.ok().data(customDataService.getSelectData(req, WebAppGlobalContext.getCurrentAppProjectId()));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端-获取网页页眉", notes = "PC网页页眉内容")
    @RequestMapping(value = "/pageHeader")
    public ResultJson<List<PageHeader>> pageHeader() {
        return ResultJson.ok().data(resourceService.getPageHeaderInfo());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端-获取网页页眉(修改版)", notes = "获取网页页眉(修改版)")
    @RequestMapping(value = "/pageHeader_new")
    public ResultJson<List<PageHeader>> newPageHeader() {
        return ResultJson.ok().data(resourceService.getNewPageHeaderInfo(WebAppGlobalContext.getCurrentAppProjectId()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端-获取网页页脚", notes = "PC网页页脚内容")
    @RequestMapping(value = "/pageFoot")
    public ResultJson<PageFoot> pageFoot() {
        return ResultJson.ok().data(resourceService.getPageFootInfo());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端-获取网页页脚（修改版）", notes = "PC网页页脚内容（修改版）")
    @RequestMapping(value = "/pageFoot_new")
    public ResultJson<PageFoot> newPageFoot() {
        return ResultJson.ok().data(resourceService.getNewPageFootInfo(WebAppGlobalContext.getCurrentAppProjectId()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端和移动端-获取配置信息", notes = "获取配置信息")
    @RequestMapping(value = "/getConf")
    public ResultJson<MenuConfigRes> getConf(@RequestBody SysconfReq req) {
        MenuConfigRes entity = menuService.getSysconf(req.getType(), WebAppGlobalContext.getCurrentAppProjectId());
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端和移动端-获取客服页面链接", notes = "获取客服页面链接")
    @RequestMapping(value = "/getSupurl")
    public ResultJson<AppUrlRes> getSupurl(@RequestBody AppSupUrlReq req) {
        AppUrlRes res = appSupService.getAppSupUrl(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取预约活动项目列表", notes = " 预约活动项目列表")
    @RequestMapping(value = "/actgrouplist", method = RequestMethod.POST)
    public ResultJson<AppActGroupRes> getActGroupList(@RequestBody AppProductGroupReq req) {
        AppActGroupRes res = appActService.getActList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取预约活动站点", notes = "预约活动站点列表")
    @RequestMapping(value = "/actlist", method = RequestMethod.POST)
    public ResultJson<AppActProductListRes> getActSiteList(@Valid @RequestBody AppProductDetailReq req) {
        AppActProductListRes res = appActService.getActProdList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取预约活动站点时段价格库存", notes = "获取指定产品规格库存与价格OR站点时段价格")
    @RequestMapping(value = "/skuavlprice")
    public ResultJson<List<AppProdSpecAvailItem>> getActSkuAvlprice(@Valid @RequestBody AppActsiteQueryAvailReq req) {
        List<AppProdSpecAvailItem> res = appActService.getProductAvailDetail(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取伴手礼商品列表", notes = "伴手礼商品列表")
    @RequestMapping(value = "/giftitemList", method = RequestMethod.POST)
    public ResultJson<AppGiftitemListRes> getGiftitemList(@Valid @RequestBody AppGiftitemListReq req) {
        AppGiftitemListRes res = resourceService.getGiftitemList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取伴手礼商品详情", notes = "获取伴手礼商品详情")
    @RequestMapping(value = "/giftitem", method = RequestMethod.POST)
    public ResultJson<AppGiftitemProductRes> getGiftitem(@Valid @RequestBody AppProductDetailReq req) {
        AppGiftitemProductRes res = resourceService.getGiftitem(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取演出节目列表", notes = "获取演出节目列表")
    @RequestMapping(value = "/performList", method = RequestMethod.POST)
    public ResultJson<AppPerformListRes> getPerformList(@Valid @RequestBody AppPerformListReq req) {
        AppPerformListRes res = resourceService.getPerformList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取演出节目详情", notes = "获取演出节目详情")
    @RequestMapping(value = "/perform", method = RequestMethod.POST)
    public ResultJson<AppPerformProductRes> getPerform(@Valid @RequestBody AppPerformQueryReq req) {
        AppPerformProductRes res = resourceService.getPerform(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取优惠券产品详情", notes = "获取优惠券产品详情")
    @RequestMapping(value = "/getCoupon_Detail", method = RequestMethod.POST)
    public ResultJson<AppCouponDetailRes> getUserCouponList(@Valid @RequestBody AppGetCouponDetailReq req) {
        AppCouponDetailRes res = resourceService.getCouponDetail(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取伴手礼价格&库存", notes = "获取指定产品规格库存与价格OR站点时段价格")
    @RequestMapping(value = "/giftitem_skuavlprice")
    public ResultJson<AppGiftitemSkuAvlPrice> getGiftitemSkuAvlprice(@Valid @RequestBody AppActsiteQueryAvailReq req) {
        AppGiftitemSkuAvlPrice res = resourceService.getGiftitemAvailDetail(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  获取指定省份代码运费", notes = "获取指定省份代码运费")
    @RequestMapping(value = "/getPostage")
    public ResultJson<AppPostageDetail> getPostage(@Valid @RequestBody AppProvinceReq req) {
        AppPostageDetail res = resourceService.getPostage(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-计算单件或多件商品费用", notes = "计算单件或多件商品费用")
    @RequestMapping(value = "/calcprice")
    public ResultJson<AppPriceDetail> calcPrice(@Valid @RequestBody AppCalcPriceReq req) throws DefinedException {
        AppPriceDetail res = resourceService.calcPrice(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取产品信息详情", notes = "获取产品信息详情")
    @RequestMapping(value = "/getProdinfo")
    public ResultJson<AppProdInfoRes> calcPrice(@Valid @RequestBody AppProdInfoReq req) throws DefinedException {
        AppProdInfoRes res = resourceService2.getProdInfo(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取路线列表", notes = "获取路线列表")
    @RequestMapping(value = "/travelTipList")
    public ResultJson<AppTravelTipListRes> traveltipList(@Valid @RequestBody AppTravelTipListReq req) throws DefinedException {
        AppTravelTipListRes res = resourceService.getTravelTipList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取路线详情(日程管理)", notes = "获取路线详情（日程管理）")
    @RequestMapping(value = "/travelTip")
    public ResultJson<AppTravelTipDetRes> traveltip(@Valid @RequestBody AppTraveltipDetReq req) throws DefinedException {
        AppTravelTipDetRes res = resourceService.getTravelTipDet(req.getSearchkey());
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-查看路线地图", notes = "查看路线地图")
    @RequestMapping(value = "/travelTipMap")
    public ResultJson<List<AppTravelTipMap>> travelTipMap(@Valid @RequestBody AppTraveltipMapReq req) throws DefinedException {
        List<AppTravelTipMap> res = resourceService.getTravelTipDetMap(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取地图配置", notes = "获取地图配置")
    @RequestMapping(value = "/getMapConf")
    public ResultJson<Conf_Map> getMapConf(@Valid @RequestBody AppTraveltipMapReq req) throws DefinedException {
        Conf_Map res = resourceService.getMapConf(WebAppGlobalContext.getCurrentAppProjectId());
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取移动端购买二维码", notes = "获取移动端购买二维码")
    @RequestMapping(value = "/getQrCode")
    public ResultJson<Common_Pic_Res> genqrCode(@Validated @RequestBody GenQrReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();//.getCurrentProjectId();
        Common_Pic_Res pic_res = managerToolService.generateQrList(projectId, req.getPathNo(), req.getType(), req.getParam(), ProductQrcodePathFactory.QRSIZE_ONLINE);
        return ResultJson.ok().data(pic_res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取投稿比赛简介", notes = "获取投稿比赛简介")
    @RequestMapping(value = "/getContestIntro")
    public ResultJson<AppOnlineContestRes> getMapConf(@RequestBody AppOnlineContestReq req) throws DefinedException {
        AppOnlineContestRes res = contestService.loadOnlineContest(req.getContestid());
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-比赛作品综合排序&投票排名", notes = "比赛作品综合排序")
    @RequestMapping(value = "/contestEntryList")
    public ResultJson<List<AppContestEntryRes>> contestEntryList(@Valid @RequestBody AppContestEntryQueryListReq req) throws DefinedException {
        List<AppContestEntryRes> result = contestService.queryContestEntryList(req);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-比赛作品详情页", notes = "比赛作品详情页")
    @RequestMapping(value = "/load_contestEntry")
    public ResultJson<AppContestEntryRes> load_contestEntry(@Valid @RequestBody AppContestEntryReq req) throws DefinedException {
        AppContestEntryRes result = contestService.loadContestEntry(req);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取园区封面版本列表", notes = "获取园区封面版本列表")
    @RequestMapping(value = "/parkList")
    public ResultJson<AppParkListRes> getParkList(@Valid @RequestBody Common_Load_Req req) {
        AppParkListRes res = resourceService2.getParkList(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取园区内容详情", notes = "获取园区内容详情")
    @RequestMapping(value = "/parkDetail")
    public ResultJson<AppParkDetailRes> getParkDetail(@Valid @RequestBody Common_Load_Req req) {
        AppParkDetailRes res = resourceService2.getParkDetail(req);
        return ResultJson.ok().data(res);
    }



    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-查看是否本地发票开票", notes = "查看是否本地发票开票")
    @RequestMapping(value = "/query_invoiceType")
    public ResultJson<AppIsLocalInvoiceRes> query_invoiceType() throws DefinedException {
        AppIsLocalInvoiceRes result = resourceService.queryInvoiceType();
        return ResultJson.ok().data(result);
    }


}
