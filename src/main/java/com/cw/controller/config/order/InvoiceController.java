package com.cw.controller.config.order;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.order.req.*;
import com.cw.pojo.dto.order.res.InvoiceListRes;
import com.cw.pojo.dto.order.res.InvoiceLoadRes;
import com.cw.pojo.dto.order.res.InvoiceLoadTitleRes;
import com.cw.pojo.dto.order.res.InvoiceLocalInfoRes;
import com.cw.service.context.GlobalContext;
import com.cw.service.order.InvoiceService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Describe 发票管理
 * <AUTHOR> Tony Leung
 * @Create on 2021/12/13 0013
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RestController
@RequestMapping(value = "api/order/invoice", method = RequestMethod.POST)
public class InvoiceController {

    @Autowired
    InvoiceService invoiceService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_LIST_INDEX)
    @ApiOperation(value = "发票管理 - 发票列表", notes = "发票列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_LIST)
    public ResultJson<InvoiceListRes> list(@RequestBody InvoiceListReq req) {
        return ResultJson.ok().data(invoiceService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_LIST_INDEX)
    @ApiOperation(value = "发票管理 - 发票记录详情", notes = "发票记录详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_LOAD)
    public ResultJson<InvoiceLoadRes> load(@RequestBody InvoiceLoadReq req) {
        return ResultJson.ok().data(invoiceService.load(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_SAVE_INDEX)
    @ApiOperation(value = "发票管理 - 发票红冲操作", notes = "发票红冲操作")
    @RequestMapping(value = "/invoiceRed")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_RED)
    public ResultJson invoiceRed(@Validated @RequestBody InvoiceRedReq req) throws DefinedException {
        invoiceService.invoiceRed(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_SAVE_INDEX)
    @ApiOperation(value = "发票管理 - 发票红冲拒绝操作", notes = "发票红冲拒绝操作")
    @RequestMapping(value = "/invoiceRed_refuse")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_RED)
    public ResultJson invoiceRedRefust(@Validated @RequestBody InvoiceLoadReq req) throws DefinedException {
        invoiceService.invoiceRedRefuse(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_SAVE_INDEX)
    @ApiOperation(value = "发票管理 - 发票开票失败重试操作", notes = "发票开票失败重试操作")
    @RequestMapping(value = "/invoiceReCreate")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_REINVOICE)
    public ResultJson invoiceReCreate(@Validated @RequestBody InvoiceReCreateReq req) throws DefinedException {
        String projectId = GlobalContext.getCurrentProjectId();
        invoiceService.invoiceReCreate(req.getIncoideid(), projectId);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "发票管理 - 发票记录导出", notes = "发票记录导出")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody InvoiceListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        invoiceService.outPutExcel(req, httpServletResponse);
    }

    //@ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_DELETE_INDEX)
    //@ApiOperation(value = "发票管理 - 发票作废", notes = "发票作废")
    //@RequestMapping(value = "/invoiceCancel")
    //@RequireOpRight(opRight = OpRightCodes.OP_INVOICE_CANCEL)
    //public ResultJson invoiceCancel(@Validated @RequestBody InvoiceCancelReq req) throws DefinedException {
    //    invoiceService.invoiceCancel(req);
    //    return ResultJson.ok();
    //}


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "发票管理 - 查看是否支持手动确认开票", notes = "查看是否支持手动确认开票")
    @RequestMapping(value = "/isLocalInvoice")
    public ResultJson<InvoiceLocalInfoRes> isLocalInvoice() throws DefinedException {
        InvoiceLocalInfoRes result = invoiceService.invoiceIsLocalType();
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "发票管理 - 手动确认开票成功", notes = "手动确认开票成功")
    @RequestMapping(value = "/confirm_status")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_CONFIRM)
    public ResultJson confirmStatus(@Validated @RequestBody InvoiceConfirmReq req) throws DefinedException {
        invoiceService.invoiceConfirmStatus(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "发票管理 - 手动拒绝开票", notes = "手动拒绝开票")
    @RequestMapping(value = "/refuse_status")
    @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_CONFIRM)
    public ResultJson refuse_status(@Validated @RequestBody InvoiceRefuseReq req) throws DefinedException {
        invoiceService.invoiceRefuse(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.INVOICE_LIST_INDEX)
    @ApiOperation(value = "发票管理 - 发票抬头记录详情", notes = "发票抬头记录详情")
    @RequestMapping(value = "/load_InvoiceTitle")
    // @RequireOpRight(opRight = OpRightCodes.OP_INVOICE_LOAD)
    public ResultJson<InvoiceLoadTitleRes> loadTitleInfo(@RequestBody InvoiceLoadReq req) {
        return ResultJson.ok().data(invoiceService.loadInvoiceTitle(req));
    }
}
