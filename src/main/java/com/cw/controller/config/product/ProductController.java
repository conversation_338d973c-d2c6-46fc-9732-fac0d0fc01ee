package com.cw.controller.config.product;

import com.alibaba.fastjson.JSON;
import com.cw.entity.Spugroup;
import com.cw.entity.Spuqr;
import com.cw.entity.Spusitem;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.common.res.Common_Pic_Res;
import com.cw.pojo.dto.conf.req.products.*;
import com.cw.pojo.dto.conf.req.sys.GenQrReq;
import com.cw.pojo.dto.conf.req.sys.GenShortLinkReq;
import com.cw.pojo.dto.conf.res.sys.GenShortLinkRes;
import com.cw.pojo.dto.conf.res.products.*;
import com.cw.pojo.entity.Spugroup_Entity;
import com.cw.pojo.entity.Spuqr_Entity;
import com.cw.pojo.entity.Spusitem_Entity;
import com.cw.service.config.products.ProdSetupService;
import com.cw.service.config.sys.ManagerToolService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.enums.menus.ProductQrcodePathFactory;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2022-02-21
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGPRODUCT, position = SwaggerUtil.MainIndex.CONFIGPRODUCT_INDEX)
@RestController
@RequestMapping(value = "api/product", method = RequestMethod.POST)
public class ProductController {
    @Autowired
    ProdSetupService prodSetupService;

    @Autowired
    ManagerToolService managerToolService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODGROUP_SETUP_LIST_INDEX)
    @ApiOperation(value = "产品设置 - 产品大类列表", notes = "获取产品大类列表数据")
    @RequestMapping(value = "/group_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODGROUP_LIST)
    public ResultJson<SpugroupListRes> group_list(@Validated @RequestBody SpugroupListReq req) {
        return ResultJson.ok().data(prodSetupService.queryProductGroupList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODGROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "产品设置 - 获取产品大类对象", notes = "根据唯一id获取产品大类对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODGROUP_LOAD)
    public ResultJson<Spugroup_Entity> load_PRODGROUP(@Validated @RequestBody Common_Load_Req req) {
        Spugroup spugroup = prodSetupService.loadProductGroup(req.getSqlid());
        Spugroup_Entity entity = new Spugroup_Entity();
        BeanUtils.copyProperties(spugroup, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODGROUP_SETUP_SAVE_INDEX)
    @ApiOperation(value = "产品设置 - 保存产品大类", notes = "保存产品大类")
    @RequestMapping(value = "/save_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODGROUP_SAVE)
    public ResultJson<Spugroup_Entity> save_group(@RequestBody Spugroup_Entity req) {
        return ResultJson.ok().data(prodSetupService.saveProductGroup(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODGROUP_SETUP_DELETE_INDEX)
    @ApiOperation(value = "产品设置 - 删除产品大类", notes = "删除产品大类")
    @RequestMapping(value = "/delete_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODGROUP_DELETE)
    public ResultJson delete_group(@RequestBody Common_Del_Req req) {
        prodSetupService.deleteProductGroup(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODGROUP_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "产品设置 - 更新产品大类开关状态.对当前状态取反", notes = "更新产品大类状态")
    @RequestMapping(value = "/update_Group_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODGROUP_STATUS)
    public ResultJson updGroupStatus(@Valid @RequestBody Common_Load_Req req) {
        prodSetupService.updProductGroupStatus(req.getSqlid());
        return ResultJson.ok();
    }

    /**
     * 产品小类
     */
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_LIST_INDEX)
    @ApiOperation(value = "产品设置 - 产品小类列表", notes = "获取产品小类列表数据")
    @RequestMapping(value = "/product_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_LIST)
    public ResultJson<SpusitemListRes> product_list(@RequestBody SpusitemListReq req) {
        return ResultJson.ok().data(prodSetupService.queryProductList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_GET_INDEX)
    @ApiOperation(value = "产品设置 - 获取产品小类对象", notes = "根据唯一id获取产品详情对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_product")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_LOAD)
    public ResultJson<Spusitem_Entity> load_product(@RequestBody SpusitemQueryReq req) {
        Spusitem spusitem = prodSetupService.loadProductByCode(req);
        Spusitem_Entity entity = new Spusitem_Entity();
        BeanUtils.copyProperties(spusitem, entity);
        if (StringUtils.isNotBlank(spusitem.getPackinfo())) {
            List<SpusitemPackInfo> packInfo = JSON.parseArray(spusitem.getPackinfo(), SpusitemPackInfo.class);
            entity.setPackInfo(packInfo);
        }
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_SAVE_INDEX)
    @ApiOperation(value = "产品设置 - 保存产品小类", notes = "保存产品小类")
    @RequestMapping(value = "/save_product")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_SAVE)
    public ResultJson<Spusitem_Entity> save_product(@RequestBody Spusitem_Entity req) {
        Spusitem_Entity entity = prodSetupService.saveProduct(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_DELETE_INDEX)
    @ApiOperation(value = "产品设置 - 删除产品小类", notes = "删除产品小类")
    @RequestMapping(value = "/delete_product")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_DELETE)
    public ResultJson delete_product(@RequestBody Common_Del_Req req) {
        prodSetupService.deleteProduct(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "产品设置 - 获取产品代码库存&价格", notes = "获取产品代码库存&库存")
    @RequestMapping(value = "/load_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_RESOURCE_INDEX)
    public ResultJson<List<SpusitemResourceRes>> load_resources_price(@Validated @RequestBody SpusitemResourceQueryReq req) {
        return ResultJson.ok().data(prodSetupService.queryProductResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "产品设置 - 更新产品开关状态.对当前状态取反", notes = "更新产品状态")
    @RequestMapping(value = "/update_product_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_STATUS)
    public ResultJson updProductStatus(@Valid @RequestBody Common_Switch_Req req) {
        prodSetupService.updProductStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "产品设置 - 设置产品代码库存&价格", notes = "设置产品代码库存&价格")
    @RequestMapping(value = "/save_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCT_RESOURCE_SAVE)
    public ResultJson save_resources_price(@Validated @RequestBody RspusitemSaveReq req) {
        prodSetupService.batchProduct(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "产品设置 - 生成购买链接二维码", notes = "生成访问链接二维码")
    @RequestMapping(value = "/genqrcode")
    public ResultJson<Common_Pic_Res> genqrCode(@Validated @RequestBody GenQrReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        Common_Pic_Res pic_res = managerToolService.generateQrList(projectId, req.getPathNo(), req.getType(), req.getParam(), ProductQrcodePathFactory.QRSIZE_OFFLINE);
        return ResultJson.ok().data(pic_res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "产品设置 - 生成销售员二维码", notes = "生成销售员二维码")
    @RequestMapping(value = "/genSalesQr")
    public ResultJson<Common_Pic_Res> genSalesQr(@Validated @RequestBody GenQrReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        //Common_Pic_Res pic_res = managerToolService.generateQrList("001", 4, "WXAPP", "TESTTICKET2", ProductQrcodePathFactory.QRSIZE_ONLINE);
        Common_Pic_Res pic_res = managerToolService.generateSalesQr(projectId, req.getPathNo(), "WXAPP", req.getProdType(), req.getGroupId(), req.getProductCode(), req.getSalesId());
        return ResultJson.ok().data(pic_res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "产品设置 - 生成动态二维码", notes = "生成动态二维码")
    @RequestMapping(value = "/genDynamicQr")
    public ResultJson<Common_Pic_Res> genDynamicQr(@Validated @RequestBody GenQrReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        //Common_Pic_Res pic_res = managerToolService.generateQrList("001", 4, "WXAPP", "TESTTICKET2", ProductQrcodePathFactory.QRSIZE_ONLINE);
        Common_Pic_Res pic_res = managerToolService.generateStampQr(projectId, req.getPathNo(), req.getType(), req.getProductCode());
        return ResultJson.ok().data(pic_res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_LIST_INDEX)
    @ApiOperation(value = "产品设置 - 组合项目列表", notes = "组合项目列表")
    @RequestMapping(value = "/spuqr_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTQR_LIST)
    public ResultJson<Spuqr_List_Res> spuqr_list(@Validated @RequestBody SpuqrListReq req) {
        return ResultJson.ok().data(prodSetupService.querySpuqrList(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_GET_INDEX)
    @ApiOperation(value = "产品设置 - 获取组合项目对象", notes = "根据唯一id获取组合项目对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_spurq")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTQR_LOAD)
    public ResultJson<Spuqr_Entity> load_spurq(@RequestBody Common_Load_Req req) {
        Spuqr spuqr = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            spuqr = prodSetupService.loadSpuqr(req.getSqlid());
        }
        Spuqr_Entity entity = new Spuqr_Entity();
        BeanUtils.copyProperties(spuqr, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_SAVE_INDEX)
    @ApiOperation(value = "产品设置 - 保存组合项目", notes = "保存组合项目")
    @RequestMapping(value = "/save_spuqr")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTQR_SAVE)
    public ResultJson<Spuqr_Entity> save_spuqr(@RequestBody Spuqr_Entity req) {
        Spuqr_Entity entity = prodSetupService.saveSpuqr(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_DELETE_INDEX)
    @ApiOperation(value = "产品设置 - 删除组合项目", notes = "删除组合项目")
    @RequestMapping(value = "/delete_spuqr")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSQR_DELETE)
    public ResultJson delete_spuqr(@RequestBody Common_Del_Req req) {
        prodSetupService.deleteSpuqr(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "产品设置 - 生成短链接", notes = "为指定路径生成跳转到小程序的短链接")
    @RequestMapping(value = "/genWxappShortLink")
    public ResultJson<GenShortLinkRes> genShortLink(@Validated @RequestBody GenShortLinkReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        GenShortLinkRes result = managerToolService.generateShortLink(
                projectId,
                req.getTargetPath(),
                req.getParams(),
                req.getDescription(),
                req.getExpireDays()
        );
        return ResultJson.ok().data(result);
    }

}
